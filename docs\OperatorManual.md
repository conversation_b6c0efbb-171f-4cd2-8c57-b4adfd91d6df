# Recoater HMI - Operator's Guide

**Version 2.1**

## Table of Contents

1. [Introduction](#1-introduction)
2. [The Main Interface](#2-the-main-interface)
   - 2.1. [The Connection Status Indicator](#21-the-connection-status-indicator)
3. [Status View (Home Screen)](#3-status-view-home-screen)
4. [Recoater Dashboard](#4-recoater-dashboard)
   - 4.1. [Drum Controls](#41-drum-controls)
   - 4.2. [Hopper Controls (Scraping Blades)](#42-hopper-controls-scraping-blades)
   - 4.3. [Leveler Control](#43-leveler-control)
5. [Print Control View](#5-print-control-view)
   - 5.1. [Layer Parameters](#51-layer-parameters)
   - 5.2. [Layer Preview](#52-layer-preview)
   - 5.3. [File Management (Drum Cache Upload)](#53-file-management-drum-cache-upload)
   - 5.4. [Multi-Layer CLI File Workflow](#54-multi-layer-cli-file-workflow)
   - 5.5. [CLI Layer Range Selection (Batch Processing)](#55-cli-layer-range-selection-batch-processing)
   - 5.6. [Print Job Management](#56-print-job-management)
   - 5.7. [Troubleshooting](#57-troubleshooting)
6. [Upload Single Layer Workflow](#6-upload-single-layer-workflow)
7. [Configuration View](#7-configuration-view)
8. [Debug View - Direct Hardware Control](#8-debug-view---direct-hardware-control)
   - 8.1. [Purpose and Use Cases](#81-purpose-and-use-cases)
   - 8.2. [Key Differences from Automated Workflow](#82-key-differences-from-automated-workflow)
   - 8.3. [Interface Components](#83-interface-components)
   - 8.4. [Transitioning Between Manual and Automated Operations](#84-transitioning-between-manual-and-automated-operations)
9. [Technical Specifications](#9-technical-specifications)

---

## 1. Introduction

Welcome to the Recoater HMI (Human-Machine Interface). This guide provides comprehensive instructions for new operators to effectively control and monitor the Aerosint SPD Recoater using this web-based application.

This guide is structured to follow the layout of the application, with sections corresponding to each of the main views available in the navigation sidebar.

---

## 2. The Main Interface

![Main Interface](images/main-interface.png)

The HMI is composed of three primary areas:

1.  **Header:** Located at the top, it displays the application title and the **Connection Status Indicator**.
2.  **Navigation Sidebar:** On the left, this bar allows you to switch between the HMI's main functional views.
3.  **Content Area:** The central part of the screen where the controls and information for the selected view are displayed.

### 2.1. The Connection Status Indicator

This indicator, located in the top-right corner, is the most critical element for understanding the system's state. **Always check its status before attempting any operation.**

![Connection Status Indicator](images/connection-status.png)

*   <span style="color: #27ae60;">**● Connected**</span>: The HMI is successfully communicating with the recoater hardware. The system is operational.
*   <span style="color: #f39c12;">**● Error**</span>: The HMI is connected, but the recoater hardware is reporting an error. You may need to check the hardware for issues.
*   <span style="color: #e74c3c;">**● Disconnected**</span>: The HMI cannot communicate with the recoater backend. This could be due to a network issue, or the backend service or recoater hardware not being turned on.

---

## 3. Status View (Home Screen)

This is the default view when you first load the application. It provides a high-level summary of the system's connection status.

![Status View](images/status-view.png)

*   **Backend Status:** Shows if your browser is connected to the HMI's backend software.
*   **Recoater Status:** Shows if the HMI's backend is successfully connected to the physical recoater hardware.
*   **Last Update:** Timestamps the last successful communication.
*   **Error Information:** If an error is present, this card will appear with a description of the problem.

The **Refresh Status** button allows you to manually request an immediate update from the system.



---

## 4. Recoater Dashboard

![Recoater Dashboard](images/recoater-dashboard.png)

This is the primary dashboard for controlling the individual components of the recoater head. It is divided into sections for Drums, Hoppers (scraping blades), and the Leveler.

### 4.1. Drum Controls

This section contains a control card for each of the three drums (Drum 0, Drum 1, Drum 2). Each card allows you to monitor and control its associated drum.

![Drum Status](images/drum-status.png)

**Status Display:**
*   **Position:** The current rotational position of the drum in millimeters (mm).
*   **Circumference:** The total circumference of the drum in millimeters (mm).
*   **Running/Stopped:** A status light indicates if the drum is currently in motion.

**Motion Control:**
You can command the drum to move using several modes. The `Start Motion` button is disabled if the drum is already running.

![Drum Motion](images/drum-motion.png)

*   **Mode:**
    *   `Relative`: Moves the drum by the specified `Distance (mm)`.
    *   `Absolute`: Moves the drum to a specific absolute `Position (mm)`.
    *   `Turns`: Rotates the drum by the specified number of `Turns`.
    *   `Speed`: Rotates the drum continuously at the specified `Speed (mm/s)`.
    *   `Homing`: Returns the drum to its zero-reference position.
*   **Speed (mm/s):** The speed of the movement.
*   **Buttons:**
    *   `Start Motion`: Initiates the configured movement.
    *   `Cancel Motion`: Immediately stops any ongoing drum movement. This is only enabled when the drum is running.

**Pressure Controls:**

![Drum Pressure](images/drum-pressure.png)

*   **Ejection Pressure:**
    *   Displays the current and target pressure.
    *   Set a new `Target` pressure and select the `Unit` (`Pa` for Pascals, `bar` for Bar). Click `Set` to apply.
*   **Suction Pressure:**
    *   Displays the current and target pressure in Pascals (Pa).
    *   Set a new `Target` pressure. Click `Set` to apply.

### 4.2. Hopper Controls (Scraping Blades)

This section contains a control card for each drum's hopper and scraping blade assembly.

> **CRITICAL SAFETY WARNING - SCRAPING BLADE OPERATION**
> 
> **The motion of the scraping blade can damage the drum!**
> 
> **Before operating scraping blades:**
> - **Make sure the drum is not rotating** when a motion of the scraping blade is performed
> - **Make sure, at all times, that the scraping blade is not in contact with the drum.** This can be checked by sliding a 50µm feeler gauge between the mesh and the scraping blade
> 
> **If the scraping blade gets mechanically stuck:**
> - This can happen when one side of the scraping blade is considerably higher or lower than the other side
> - The blade will appear visually sideways
> - **To unlock it:** Move one side of the blade to make it horizontal again

![Hopper Status](images/hopper-status.png)

**Status Display:**
*   **Screw Positions:** Shows the current position of each of the two blade screws in micrometers (µm).
*   **Running/Stopped:** A status light indicates if any part of the blade assembly is in motion.


![Hopper Motion](images/hopper-motion.png)

**Collective Blade Motion:**
Controls both blade screws together.
*   **Mode:**
    *   `Relative`: Moves both screws by the specified `Distance (µm)`.
    *   `Absolute`: Moves both screws to a specific `Position (µm)`.
    *   `Homing`: Returns both screws to their zero-reference position. The distance input is hidden in this mode.
*   **Buttons:** `Start Motion` and `Cancel Motion` function similarly to the drum controls.

![Hopper Screw](images/hopper-screw.png)

**Individual Screw Control:**
Allows for fine-tuning of each screw.
*   Enter a relative `Distance (µm)` for the specific screw.
*   Click `Move` to start the motion for that screw only.
*   Click `Stop` to cancel motion for that screw only.

### 4.3. Leveler Control

This card manages the powder leveler.

![Leveler](images/leveler.png)

*   **Pressure Control:** Displays the `Current`, `Target`, and `Maximum` allowed pressure in Pascals (Pa). You can input a new target and click `Set` to apply it.
*   **Magnetic Sensor:** Displays the state of the leveler's magnetic sensor as either `Active (Field Detected)` or `Inactive`.

---

## 5. Print Control View

This view consolidates all functions related to preparing and executing a print job.

### 5.1. Layer Parameters

These parameters define how the recoater will process the current layer.

![Layer Parameters](images/layer-parameters.png)

*   **Filling Drum ID:** The ID of the drum (0, 1, or 2) containing the filling material. Set to **-1** for no filling.
*   **Patterning Speed (mm/s):** The speed at which the recoater will operate.
*   **X Offset (mm):** Horizontal X offset applied during deposition.
*   **Enable Powder Saving:** Toggles the use of powder-saving strategies.

Use the `Load Current` button to fetch the existing parameters from the hardware, and `Save Parameters` to apply your changes.

### 5.2. Layer Preview

This area shows a PNG image of what the layer will look like, with color-coded geometry to distinguish between different drums.

![Layer Preview](images/layer-preview.png)

**Color Legend:**
A color legend is displayed above the preview showing the color mapping for each drum:
*   **Blue (#3498db)**: Drum 0 geometry
*   **Orange (#e67e22)**: Drum 1 geometry
*   **Green (#27ae60)**: Drum 2 geometry

*   **Preview Source (Dropdown):**
    *   `Current Layer Configuration`: Generates a color-coded preview showing how geometry would be distributed across drums. Each drum's geometry is rendered in its designated color.
    *   `Drum [0-2] Geometry`: Loads and displays the preview of the geometry file *currently stored* on the selected drum, rendered in that drum's specific color.
*   **Layer Number (Dropdown):**    
    *   `Layers 1 to Unknown`: Automatically calculates the max layer upon upload of any CLI file, remains as 'unknown' if no files are cached. Allows for viewing of combined layers across all drums or any drum for desired layer.
*   **Load Preview (Button):** Generates and displays the image based on the selected source.

### 5.3. File Management (Drum Cache Upload)

This section allows you to upload whole CLI files to the backend cache for each drum, preparing them for automated multi-material job execution. This interface provides a streamlined way to cache geometry files for each drum in the multi-material printing workflow.

![File Management](images/file-management.png)

**Interface Description:**
The File Management section displays three identical columns, one for each drum (Drum 0, Drum 1, Drum 2), allowing independent file management for each drum.

#### Per-Drum File Upload

Each drum column contains:

*   **Geometry File Selection:**
    *   `Choose File` button to select a PNG or CLI file from your computer
    *   Displays "No file chosen" when no file is selected
    *   Shows selected filename once a file is chosen

*   **Upload Status:**
    *   `LAST UPLOADED:` section showing name of last uploaded file
    *   Displays "No file uploaded" when no file is cached for that drum
    *   Shows file information when a file has been successfully cached

*   **Action Buttons:**
    *   `Upload` button (grayed out until a file is selected) - Caches the selected file for this drum
    *   `Delete` button (red) - Removes the cached file for this drum

#### Important Notes

**Cache-Based Workflow:**
*   Files are uploaded to the HMI's backend cache, not directly to hardware
*   Cached files are used during automated multi-material job execution
*   Each drum maintains its own cached file independently

**File Persistence:**
*   Cached files remain available until manually deleted or system restart

### 5.4. Multi-Layer CLI File Workflow

This is an advanced feature for working with standard multi-layer CLI files (both ASCII and binary formats). It allows you to visualize individual layers and cache specific layer geometries in the drum system for automated multi-material printing.

**Integration with Multi-Material Jobs:**
CLI files processed through this workflow are cached in the HMI's drum management system. Once cached, these files become available for automated multi-material job execution (Section 5.6). The system automatically uses cached drum data during layer-by-layer printing operations.

**Supported CLI Formats:**
*   **Binary CLI**: Compressed binary format (automatically detected by header)
*   **ASCII CLI**: Text-based format with space-delimited or slash-delimited commands

![CLI Upload](images/cli-upload.png)

*   **Step 1: Upload & Parse**
    1.  In the **CLI Layer Preview** section, use the **"Choose File"** input to select a multi-layer CLI file.
    2.  Click **Upload & Parse CLI**.
    3.  The HMI automatically detects the file format and parses it in memory, displaying the **File ID** and **Total Layers** found.
    4.  **Note:** This parsing step happens entirely on the HMI server for preview purposes only.
    5.  **Note:** The system has a 5-minute timeout for CLI parsing. If the parsing takes longer than 5 minutes, the operation will be canceled. Split large files if needed.

![CLI Layer Preview](images/preview-cli-layer.png)

*   **Step 2: Preview Individual Layers**
    1.  Once parsed, use the **Layer Number** input and click **Preview Layer**.
    2.  Only the selected layer is rendered and displayed as a PNG preview.
    3.  **Note:** CLI layer previews use a different color scheme than the main layer preview. Individual CLI layers show polylines in black and hatch patterns in blue, as they represent raw geometry data rather than drum-specific assignments.

![CLI Send to Drum](images/send-cli-layer.png)

*   **Step 3: Cache Layer in Drum System**
    1.  Select **Layer Number** and **Target Drum**.
    2.  Click **Send Layer to Recoater**.
    3.  The HMI extracts the 2D geometry data (polylines and hatches) from the selected layer and generates a new **ASCII CLI file** containing only that layer's geometry (regardless of the original file format). This ensures compatibility with the drum firmware which requires ASCII CLI format. The ASCII CLI file is then **cached in the drum system** (replacing any existing cached file on that drum).
    4.  **Note:** The layer geometry is cached as ASCII CLI format for automated job processing, not sent directly to hardware. The PNG preview is only for visualization purposes. The full parsed CLI file remains in HMI/IPC memory (keyed by File ID) until cleared or the backend server restarts, and can be re-used for further previews or caching operations without re-uploading.
    5.  **Integration:** Cached drum data becomes immediately available for multi-material job execution and will be automatically used during layer-by-layer printing operations.

### 5.5. CLI Layer Range Selection (Batch Processing)

This advanced feature allows you to cache multiple consecutive layers from a parsed CLI file to a drum in a single operation, enabling efficient batch processing for multi-material manufacturing workflows.

**Integration with Multi-Material Jobs:**
Layer ranges processed through this workflow are cached in the HMI's drum management system. The cached range becomes immediately available for automated multi-material job execution, where the system will automatically iterate through the cached layers during printing operations.

*   **Prerequisites:** You must first upload and parse a multi-layer CLI file using the steps in section 5.4.

*   **How Layer Range Processing Works:**
    *   The HMI extracts the specified layer range from the parsed CLI file
    *   Multiple layers' geometry data (polylines and hatches) are combined into a single optimized **ASCII CLI file**
    *   This combined ASCII CLI file contains all the layers in the range with proper layer commands and Z-height information
    *   The resulting file is **cached in the drum system**, replacing any existing cached geometry for that drum
    *   Each drum can only store one cached geometry file at a time

**Generated CLI Format:**
The system generates ASCII CLI files with a standard structure to ensure compatibility with drum firmware that requires ASCII CLI formatting.

*   **Default Values:** When a CLI file is uploaded, the End Layer Number automatically defaults to the total number of layers in the file for convenience.

![CLI Layer Range](images/send-cli-range.png)

*   **Layer Range Selection:**
    1.  In the **CLI Layer Selection** section, specify the **Start Layer Number** and **End Layer Number** (both 1-indexed).
    2.  Select the **Target Drum** (0, 1, or 2) where the layer range should be cached.
    3.  Ensure the range is valid: start layer ≤ end layer, and both within the total number of layers.
    4.  The interface validates your range and displays the number of layers to be processed.

*   **Batch Cache Operation:**
    1.  Click **Send Layer Range** to initiate the batch caching operation.
    2.  The system processes layers sequentially and provides progress feedback.
    3.  Upon completion, you'll receive confirmation that the range was cached successfully.
    4.  **Note:** The original parsed CLI file remains available in memory for additional operations.
    5.  **Integration:** The cached drum data is immediately available for multi-material job execution.

*   **Important Considerations:**
    *   **Processing time**: Layer range operations may take time for large ranges
    *   **Cache limitation**: Each drum overwrites its previous cached content when receiving a new layer range
    *   **Memory efficiency**: The original CLI file structure and metadata are preserved in cache until manually cleared or new CLI file uploaded
    *   **Validation**: The system validates layer numbers and drum availability before processing

### 5.6. Print Job Management

This section displays the current status and controls for managing multi-material print jobs. The interface provides real-time monitoring and control of automated layer-by-layer printing operations.

![Print Job Management](images/print-job-management.png)

#### Print Job Status Display

The top section shows the current state of the print job system:

![Print Job State](images/print-job-state.png)

*   **Current State**: Displays the overall system status:
    *   `Ready`: System is prepared and available to start a new job
    *   `Printing`: A multi-material job is currently executing

*   **Print Active**: Shows whether a print job is currently running:
    *   `Yes`: A job is actively processing layers
    *   `No`: No job is currently active

#### Print Job Controls

*   **Start Print Job**: Initiates a new multi-material print job using cached CLI files from the drums. This button is only enabled when:
    *   The system status shows `Ready`
    *   At least one drum has cached CLI data
    *   No other job is currently active

*   **Cancel Print Job**: Immediately stops the current print job. This button is only enabled during active printing and will:
    *   Require confirmation before proceeding
    *   Stop all layer processing operations
    *   Clear cached drum files
    *   Reset the system to `Ready` state
    *   Note: Error flags, if any, requires manual clearing by operator

*   **Refresh Status**: Manually updates the job status information by querying the hardware for the latest state

#### Job Progress Section

The bottom section shows the current progress of the print job:

![Print Job Progress](images/print-job-progress.png)

*   **Overall Progress**: Shows the completion percentage and current layer status
    *   Format: `Layer X of Y` with percentage completion
    *   Progress bar provides visual indication of job completion
    *   Real-time updates as layers are processed

#### Drum Status Display

The drum status section shows the current state of each drum:

*   **Drum 0, Drum 1, Drum 2**: Individual status indicators for each drum showing:
    *   `File`: Name of file uploaded with layer range
    *   `Layers`: Total number of layers in the uploaded file

#### No Active Job State

When no job is running, the interface displays:
*   **"No Active Job"** message
*   **Instruction text**: "Upload CLI files and start a multi-material job to see progress here"
*   This guides operators to use the CLI file workflows (Sections 5.4 and 5.5) before starting a job

#### Error Handling and System Status

When errors occur during job execution, the system provides comprehensive error information and recovery tools:

![Critical System Error](images/critical-error-modal.png)

**Critical Error Pop-up**

When critical errors are detected, a pop-up appears with detailed information:

*   **Error Type**: Identifies if error is from the recoater HMI program on the IPC (backend error) or from the PLC within TwinCAT XAR (plc error)
*   **Error Details**: Shows specific technical information about the error if identified via recoater HMI program. Does not specify plc errors.
*   **Impact Assessment**: Explains how the error affects operations:
    *   "All print operations have been paused"
    *   "New jobs cannot be started until errors are resolved"
    *   "Operator intervention is required"

**Required Actions for Error Recovery**

The modal provides a step-by-step recovery process:

1. **Investigate the Error**: Check system logs and hardware status to identify the root cause
2. **Resolve the Issue**: Take appropriate corrective action based on error type and diagnostics
3. **Clear Error Flags**: Use the "Clear Error Flags" button to reset the system state

**Closing the Error Pop-up**

Operators have one of two possible paths forward:

![Error Paths](images/error-paths.png)

*   **Clear Error Flags**: Immediately clears any error flagged on the recoater HMI and resumes printing. Recommended if obstacle is minor and easily removed.
*   **Acknowledge**: Closes the pop-up and remain on print page with errors enabled. Operators can then decide to cancel the job or maintain the error message on screen for reference or to block off usage of the recoater. Error flags remain in-place indefinitely until manually cleared.


**System Error Status Display**

Operator is returned back to the print page with the following view if "Acknowledge" was selected to close the error pop-up:

![System Error Status Display](images/system-error-status-display.png)

*   **System Error Status**: 
    *   Red indicator with "Errors Detected" when issues are present
    *   Shows specific error types (Backend Error, PLC Error)
    *   Error status remains "Active" until manually cleared

*   **Clear Errors Button**: Red button to reset system to error-free state and resume print jobs.

**Recent Errors Log**

The system maintains a history of recent errors for troubleshooting:

*   **Timestamp**: Shows when each error occurred
*   **Error Source**: Identifies whether error came from Backend, or Hardware
*   **Error Message**: Displays the specific error description
*   **Frequency Indicator**: Shows how many times the same error has occurred
*   **Clear History**: Option to clear the error log for a fresh start

**Coordination Status**

PLC programmers may also scroll down to the bottom of the print page to view the status of the OPC UA error variable exposed to the PLC:

![Coordination Status Error](images/coordination-status-error.png)

**Important Notes**:
*   Clearing errors does not restart the entire job - it resumes from the current layer
*   Multiple error types may be active simultaneously and require individual attention
*   The system preserves job progress and drum cache during error recovery
*   Some errors may require hardware intervention before they can be cleared
*   Cancelling job does not reset error flags

### 5.7. Troubleshooting

**CLI File Issues:**
*   **"Unrecognized directive" warnings**: In rare cases, some CLI files may contain non-standard directives that are safely ignored during parsing.
*   **Upload failures**: Check that the CLI file is not corrupted and is in ASCII or binary CLI format.
*   **Layer range errors**: Ensure start layer ≤ end layer and both are within the total layer count.

**Preview Issues:**
*   **Color-coded previews**: The main layer preview uses drum-specific colors (blue, orange, green) while CLI layer previews use black/blue coloring. This is normal and expected behavior.
*   **Preview generation failures**: If color-coded previews fail to generate, the system will fall back to simple placeholder images.

**Hardware Communication:**
*   **Connection issues**: Check the connection status indicator in the header before attempting operations.
*   **Upload timeouts**: Large layer ranges or files may take time to process - wait for completion confirmation.

---

## 6. Sample Workflow

1. Navigate to Print Tab
2. Choose [your_cli_file].cli file in CLI Layer Preview
3. Press 'Upload & Parse CLI'
4. In CLI layer preview, set Layer Number=2, Target Drum=Drum 1, then press 'Send Layer to Recoater'
5. Check that Drum 1 has a file with the correct layer uploaded under Job Progress > Drum Status
6. Press 'Start Print Job'
7. Operator should see Layer 1 of 1 100%
8. Wait for the layer to finish printing
9. Upon job completion, overall Progress resets to layer 0 of 0 0%, Drums and cache are cleared and back to idle. Print Job Management automatically returns back to "Current State: Ready, Print Active: No"

---

## 7. Configuration View

This view is for setting persistent, system-wide hardware parameters. **Changes made here affect the machine's core configuration and should be done with care.**

![Configuration View](images/configuration-view.png)

*   **Build Space Configuration:** Set the machine's physical build volume dimensions (diameter, length, width).
*   **System Configuration:** Set the `Resolution (µm)` and `Ejection Matrix Size`.
*   **Drum Gap Configuration:**
    *   Displays a list of the gaps between the drums in millimeters (mm).
    *   Use the `Add Gap` and `Remove Gap` buttons to modify the list to match your hardware setup.
*   **Action Buttons:**
    *   `Save Configuration`: Applies all changes and saves them to the recoater hardware.

---
## 8. Debug View - Direct Hardware Control

The Debug View provides direct, low-level access to recoater hardware functions for testing, troubleshooting, and manual operations. This view is designed for advanced users and contrasts significantly with the automated multi-material job workflow described in previous sections despite a similar interface to the Print page.

![Debug View](images/debug-view.png)

**Important Note**: The Debug View bypasses the automated job management system and provides direct hardware control. Use this view **only** for testing, troubleshooting, or when automated workflows are not suitable for your specific needs. This function is **not** meant for print jobs.

### 8.1. Purpose and Use Cases

The Debug View serves several critical purposes:

**Testing and Validation**:
- Test individual hardware components before running automated jobs
- Validate drum functionality and file uploads
- Verify layer parameter configurations
- Debug hardware communication issues

**Manual Operations**:
- Direct file upload to specific drums when automated caching is not desired
- Manual layer parameter adjustment for experimental setups
- Individual drum geometry management
- Direct print job control for simple, single-layer operations

**Troubleshooting**:
- Isolate hardware issues by testing components individually
- Verify file formats and compatibility
- Test network connectivity and API responses
- Debug CLI file parsing and layer extraction

### 8.2. Key Differences from Automated Workflow

```
Debug View (Manual Upload)              Multi-Material Jobs (Cache Upload)
┌─────────────────────────┐           ┌─────────────────────────────┐
│     Operator            │           │        Operator             │
│        │                │           │           │                 │
│        ▼                │           │           ▼                 │
│   Upload File           │           │    Upload File              │
│        │                │           │           │                 │
│        ▼                │           │           ▼                 │
│ ╔═══════════════════╗   │           │  ┌─────────────────────┐    │
│ ║    RECOATER       ║   │           │  │    HMI CACHE        │    │
│ ║   ┌─────────────┐ ║   │           │  │  ┌───────────────┐  │    │
│ ║   │   Drum 0    │ ║   │           │  │  │  drum0.cli    │  │    │
│ ║   │   Drum 1    │ ║   │           │  │  │  drum1.cli    │  │    │
│ ║   │   Drum 2    │ ║   │           │  │  │  drum2.cli    │  │    │
│ ║   └─────────────┘ ║   │           │  │  └───────────────┘  │    │
│ ║    [Hardware]     ║   │           │  │   [File System]     │    │
│ ╚═══════════════════╝   │           │  └─────────────────────┘    │
│                         │           │           │                 │
│   Direct to Machine     │           │           ▼                 │
└─────────────────────────┘           │  Automated Job sends to:    │
                                      │ ╔═══════════════════╗       │
                                      │ ║    RECOATER       ║       │
                                      │ ║   ┌─────────────┐ ║       │
                                      │ ║   │   Drum 0    │ ║       │
                                      │ ║   │   Drum 1    │ ║       │
                                      │ ║   │   Drum 2    │ ║       │
                                      │ ║   └─────────────┘ ║       │
                                      │ ║    [Hardware]     ║       │
                                      │ ╚═══════════════════╝       │
                                      └─────────────────────────────┘
```

| Feature | Debug View (Manual) | Multi-Material Jobs (Automated) |
|---------|-------------------|--------------------------------|
| **File Management** | Direct upload to hardware drums | Cached in HMI drum management system |
| **Job Coordination** | Manual start/stop of simple print jobs | Automated layer-by-layer processing with OPC UA |
| **Layer Processing** | Manual layer parameter setting | Automatic layer iteration through cached data |
| **Error Handling** | Manual operator intervention required | Automatic pause-and-retry with guided recovery |
| **Multi-Drum Coordination** | Manual coordination between drums | Automatic synchronized processing across all drums |
| **Progress Tracking** | Basic job status only | Comprehensive progress tracking with layer counts |

### 8.3. Interface Components

#### Connection Status
Displays real-time connection status identical to other views, showing Connected/Disconnected state with visual indicators.

#### Layer Parameters (Manual Configuration)
Direct hardware parameter control:

*   **Filling Drum ID**: Specify which drum contains filling material (-1 for none)
*   **Patterning Speed (mm/s)**: Set drum movement speed for current operation
*   **X Offset (mm)**: Horizontal offset for material deposition
*   **Enable Powder Saving**: Toggle powder conservation strategies

**Controls**:
*   `Load Current`: Retrieve current hardware parameter settings
*   `Save Parameters`: Apply parameter changes directly to hardware

#### Layer Preview (Direct Hardware Preview)
Real-time preview generation from current hardware state:

*   **Preview Sources**:
    *   `Current Layer Configuration`: Shows preview based on currently loaded parameters
    *   `Drum [0-2] Geometry`: Displays geometry currently stored on specific drum hardware

**Key Difference**: Previews show the actual hardware state, not cached or planned configurations like in automated workflows.

#### File Management (Direct Hardware Upload)
Direct drum-to-hardware file operations:

**Per-Drum Upload Columns**:
*   **Drum 0, 1, 2**: Individual file management for each drum
*   **Direct Upload**: Files sent immediately to drum hardware (no caching)
*   **Download**: Retrieve files directly from drum storage
*   **Delete**: Remove files immediately from drum hardware

**Important Notes**:
*   Files uploaded here are sent directly to hardware, bypassing the HMI caching system
*   No integration with automated multi-material job workflows
*   Each drum operation is independent - no coordination between drums
*   File operations take effect immediately

#### CLI Layer Preview (Manual Layer Operations)
Advanced CLI file handling for testing and manual operations:

**Upload & Parse**:
*   Upload multi-layer CLI files for parsing and individual layer access
*   Provides `File ID` and `Total Layers` information
*   Files remain in HMI memory for manual layer operations

**Layer Preview**:
*   Select specific layer numbers for individual preview
*   Generate PNG previews of individual layers
*   Test CLI file integrity and layer content

**Send Layer to Recoater**:
*   Extract individual layers and send directly to specific drums
*   Bypasses automated job caching system
*   Immediate hardware upload with no job coordination

**Key Difference**: Unlike automated workflows that cache entire ranges and process layers sequentially, this allows manual selection and immediate upload of individual layers.

#### Print Job Management (Direct Hardware Control)
Basic print job operations without automation:

**Status Display**:
*   `Current State`: Shows hardware state (Ready/Printing/Error)
*   `Print Active`: Indicates if hardware is currently printing
*   `Has Errors`: Shows error status requiring manual intervention

**Job Controls**:
*   `Start Print Job`: Initiates immediate printing with current hardware configuration
*   `Cancel Print Job`: Stops current operation immediately
*   `Refresh Status`: Manual status update from hardware

**Key Differences**:
*   No automated layer progression
*   No multi-drum coordination
*   No automatic error recovery
*   No progress tracking through multiple layers
*   Simple start/stop operation only

### 8.4. Transitioning Between Manual and Automated Operations

**From Debug to Automated**:
1. Complete all manual operations
2. Verify hardware status is Ready
3. Clear any temporary files if necessary
4. Return to Print Control View for automated operations

**From Automated to Debug**:
1. Ensure automated jobs are completed or properly cancelled
2. Wait for system to return to Ready state
3. Clear error flags if necessary
4. Proceed with manual operations

**Important**: Never attempt to run automated jobs while manual operations are in progress, as this can cause hardware conflicts and unpredictable behavior.

---

## 9. Technical Specifications

**Hardware Limitations:**
*   **Drum Count**: System supports exactly 3 drums (IDs 0, 1, 2)
*   **CLI Format**: Hardware requires ASCII CLI format for geometry data
*   **Cache Storage**: Each drum can store only one cached geometry file at a time

**Supported File Formats:**
*   **PNG**: Direct upload to drums (section 5.3)
*   **ASCII CLI**: Direct upload or generated from parsed multi-layer files
*   **Binary CLI**: Parsed and converted to ASCII CLI for hardware compatibility

**Preview System:**
*   **Color-Coded Rendering**: Layer previews use drum-specific colors for visual distinction
*   **Drum Color Mapping**: Drum 0 (Blue #3498db), Drum 1 (Orange #e67e22), Drum 2 (Green #27ae60)
*   **Fallback Support**: System includes fallback mechanisms for preview generation failures

**System Requirements:**
*   **Network Connection**: Required for communication between HMI and recoater hardware
*   **Browser Compatibility**: Modern web browsers with WebSocket support
*   **File Size Limits**: CLI files should be reasonable size for processing and transmission

**Operator-Configurable System Settings:**

The HMI system behavior can be adjusted through configuration settings in the backend `.env` file. Operators may need to modify these settings based on specific hardware requirements or operational preferences:

*Connection and Network Settings:*
*   **RECOATER_API_HOST**: IP address of the recoater hardware (default: localhost)
*   **RECOATER_API_PORT**: Port number for recoater communication (default: 8080)
*   **RECOATER_API_BASE_URL**: Complete URL for recoater API access

*Polling and Update Intervals (in seconds):*
*   **WEBSOCKET_POLL_INTERVAL**: How often the system checks for real-time updates (default: 2.0)
*   **LAYER_COMPLETION_POLL_INTERVAL**: Frequency of layer completion status checks (default: 2.5)
*   **JOB_STATUS_POLL_INTERVAL_SECONDS**: How often job status is refreshed (default: 2.5)

*Job Execution Timing:*
*   **JOB_DRUM_UPLOAD_DELAY_SECONDS**: Delay between uploading to different drums to prevent hardware overload (default: 2.0)
*   **JOB_READY_TIMEOUT_SECONDS**: Maximum wait time for recoater ready signal (default: 300)
*   **JOB_COMPLETION_TIMEOUT_SECONDS**: Maximum wait time for layer completion (default: 1800)

*Hardware Configuration:*
*   **JOB_MAX_DRUMS**: Number of drums available for multi-material printing (default: 3)
*   **JOB_EMPTY_LAYER_TEMPLATE_PATH**: Path to blank CLI template for empty layers

*Development and Troubleshooting:*
*   **DEVELOPMENT_MODE**: Enable mock hardware mode for testing (true/false)
*   **LOG_LEVEL**: System logging detail level (INFO, DEBUG, WARNING, ERROR)

*OPC UA Coordination:*
*   **OPCUA_SERVER_ENDPOINT**: OPC UA server connection string for PLC coordination
*   **OPCUA_NAMESPACE_URI**: Namespace identifier for OPC UA variables

*Error Handling:*
*   **ERROR_STATE_DEBOUNCE_MS**: Delay to prevent error message flickering (default: 500ms)
*   **ERROR_CLEARING_TIMEOUT_MS**: Maximum time for error clearing operations (default: 5000ms)

**Configuration Change Procedure:**

1. **Access the Configuration File**: Locate the `.env` file in the backend directory
2. **Edit Settings**: Modify the desired values using a text editor
3. **Restart Services**: Restart the HMI backend service for changes to take effect
4. **Verify Changes**: Check the Status View to confirm the system is operating with new settings
5. **Test Operations**: Perform basic operations to ensure the new settings work correctly

**Important Notes:**
*   Configuration changes require backend service restart to take effect
*   Incorrect network settings may prevent hardware communication
*   Timing values should be adjusted carefully to match hardware capabilities
*   Keep backup copies of working configurations before making changes
*   Contact system administrator for guidance on complex configuration changes
