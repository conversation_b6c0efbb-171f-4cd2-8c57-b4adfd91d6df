# Chapter 3: HTML Fundamentals
## Building the Structure of Industrial Web Applications

```
╔══════════════════════════════════════════════════════════════════════════════════════╗
║                            CHAPTER 3: HTML FUNDAMENTALS                              ║
║                         Structure for Vue.js Development                             ║
╚══════════════════════════════════════════════════════════════════════════════════════╝
```

## Chapter Objectives

By the end of this chapter, you will understand:
- How HTML provides the foundation for Vue.js applications
- Essential HTML elements used in industrial interfaces
- The transformation from static HTML to reactive Vue.js templates
- Accessibility principles for professional applications
- Form handling patterns that work with Vue.js

## HTML as the Foundation

HTML (HyperText Markup Language) is the backbone of all web applications. In Vue.js development, understanding HTML is crucial because Vue.js extends HTML with powerful reactive features while maintaining its semantic structure.

### The Building Blocks

```
┌────────────────────────────────────────────────────────────────────────────────────┐
│                              HTML STRUCTURE HIERARCHY                              │
├────────────────────────────────────────────────────────────────────────────────────┤
│                                                                                    │
│ Document                    Section                     Elements                   │
│     │                          │                          │                        │
│     ▼                          ▼                          ▼                        │
│ ┌─────────────┐         ┌─────────────┐            ┌─────────────┐                 │
│ │ <html>      │         │ <section>   │            │ <input>     │                 │
│ │   <head>    │────────▶│ <header>    │──────────▶│ <button>    │                 │
│ │   <body>    │         │ <main>      │            │ <img>       │                 │
│ │             │         │ <aside>     │            │ <select>    │                 │
│ │ Entire Page │         │             │            │             │                 │
│ │ Structure   │         │ Content     │            │ Interactive │                 │
│ │             │         │ Sections    │            │ Components  │                 │
│ └─────────────┘         └─────────────┘            └─────────────┘                 │
│                                                                                    │
│ Think of it like building architecture:                                            │
│ • Document = The lot and foundation                                                │
│ • Sections = Rooms and areas                                                       │
│ • Elements = Furniture and fixtures                                                │
│                                                                                    │
└────────────────────────────────────────────────────────────────────────────────────┘
```

## 🔧 Essential HTML Elements in Our Layer Preview

### 1. Form Elements - The Foundation of User Input

**Traditional HTML Form:**
```html
<form>
  <label for="layer-input">Layer Number:</label>
  <input id="layer-input" type="number" min="1" max="100" value="1">
  <button type="submit">Load Preview</button>
</form>
```

**Vue.js Enhanced Form:**
```html
<div class="form-group">
  <label for="layer-number-input" class="form-label">Layer Number:</label>
  <input 
    id="layer-number-input"
    v-model.number="selectedLayerNumber" 
    type="number" 
    :min="1" 
    :max="getMaxLayersForSelectedDrum()"
    :disabled="!statusStore.isConnected || previewLoading"
    class="form-input"
  >
  <button @click="loadPreview" :disabled="!isConnected" class="btn btn-primary">
    {{ previewLoading ? 'Loading...' : 'Load Preview' }}
  </button>
</div>
```

**Key HTML → Vue.js Transformations:**

| HTML Attribute | Purpose | Vue Enhancement | Reactive Purpose |
|----------------|---------|-----------------|------------------|
| `value="1"` | Static initial value | `v-model.number="selectedLayerNumber"` | Two-way binding with JavaScript |
| `max="100"` | Fixed maximum | `:max="getMaxLayersForSelectedDrum()"` | Dynamic based on uploaded files |
| `disabled` | Always disabled | `:disabled="!isConnected"` | Changes based on connection status |
| `onclick="fn()"` | Basic event | `@click="loadPreview"` | Reactive method binding |

### 2. Semantic HTML Structure for Industrial Interfaces

Professional applications require proper semantic structure for accessibility, maintainability, and SEO:

```html
<!-- Well-structured HTML for industrial application -->
<section class="layer-preview-section" aria-label="Layer Preview Controls">
  <header class="section-header">
    <h2>Layer Preview</h2>
    <p class="section-description">
      Select and preview individual layers from uploaded CLI files
    </p>
  </header>
  
  <main class="preview-controls">
    <!-- Preview source selection -->
    <fieldset class="preview-source-group">
      <legend>Preview Source</legend>
      <select 
        v-model="previewSource" 
        aria-label="Select preview source"
        class="source-selector"
      >
        <option value="layer">Layer</option>
        <option value="drum-0">Drum 0 Geometry</option>
        <option value="drum-1">Drum 1 Geometry</option>
        <option value="drum-2">Drum 2 Geometry</option>
      </select>
    </fieldset>
    
    <!-- Conditional layer input -->
    <fieldset v-if="previewSource.startsWith('drum-')" class="layer-input-group">
      <legend>Layer Selection</legend>
      <label for="layer-number" class="visually-hidden">Layer Number</label>
      <input 
        id="layer-number"
        v-model.number="selectedLayerNumber"
        type="number"
        :min="1"
        :max="getMaxLayersForSelectedDrum()"
        :aria-label="`Select layer number, range 1 to ${getMaxLayersForSelectedDrum()}`"
      >
      <span class="range-indicator" aria-live="polite">
        Range: 1 to {{ getMaxLayersForSelectedDrum() || 'Unknown' }}
      </span>
    </fieldset>
    
    <!-- Action button -->
    <div class="action-group">
      <button 
        @click="loadPreview"
        :disabled="!statusStore.isConnected || previewLoading"
        :aria-label="previewLoading ? 'Loading preview, please wait' : 'Load layer preview'"
        class="btn btn-primary"
      >
        <span class="btn-text">
          {{ previewLoading ? 'Loading...' : 'Load Preview' }}
        </span>
        <span v-if="previewLoading" class="loading-spinner" aria-hidden="true"></span>
      </button>
    </div>
  </main>
  
  <!-- Preview display area -->
  <aside class="preview-display" aria-label="Layer preview image">
    <img 
      v-if="previewImageUrl" 
      :src="previewImageUrl" 
      :alt="`Layer ${selectedLayerNumber} preview for ${previewSource}`"
      class="preview-image"
    >
    <div v-else class="empty-preview" role="img" aria-label="No preview available">
      <p>No preview available</p>
    </div>
  </aside>
</section>
```

**HTML Semantic Elements Explained:**
- `<section>`: Thematic grouping of content
- `<header>`: Introductory content for the section
- `<main>`: Primary content area
- `<fieldset>` + `<legend>`: Grouped form controls with labels
- `<aside>`: Supplementary content (preview display)

### 3. Form Input Types for Industrial Applications

Different input types serve different purposes in our interface:

```html
<!-- Number input for layer selection -->
<input type="number" min="1" max="100" step="1" 
       placeholder="Enter layer number">

<!-- Select dropdown for drum selection -->
<select aria-label="Select drum">
  <option value="">Choose a drum...</option>
  <option value="drum-0">Drum 0 (Blue Material)</option>
  <option value="drum-1">Drum 1 (Orange Material)</option>
  <option value="drum-2">Drum 2 (Green Material)</option>
</select>

<!-- Range slider for quick layer navigation -->
<input type="range" min="1" max="100" value="50"
       aria-label="Quick layer selection">

<!-- File input for CLI uploads -->
<input type="file" accept=".cli,.CLI"
       aria-label="Upload CLI file">

<!-- Checkbox for preview options -->
<input type="checkbox" id="show-annotations">
<label for="show-annotations">Show layer annotations</label>
```

## Vue.js Integration Patterns

### From Static to Reactive

**Traditional HTML (Static):**
```html
<div class="status-indicator">
  <span class="status-text">Disconnected</span>
  <div class="status-light red"></div>
</div>

<script>
// Manual updates required
function updateStatus(isConnected) {
  const text = document.querySelector('.status-text')
  const light = document.querySelector('.status-light')
  
  if (isConnected) {
    text.textContent = 'Connected'
    light.className = 'status-light green'
  } else {
    text.textContent = 'Disconnected'
    light.className = 'status-light red'
  }
}
</script>
```

**Vue.js HTML (Reactive):**
```html
<div class="status-indicator">
  <span class="status-text">{{ isConnected ? 'Connected' : 'Disconnected' }}</span>
  <div :class="['status-light', isConnected ? 'green' : 'red']"></div>
</div>

<script setup>
// Automatic updates
const isConnected = ref(false)
// When isConnected.value changes, HTML automatically updates!
</script>
```

### Conditional Rendering

Vue.js provides powerful directives for conditional content:

```html
<!-- Show/hide based on conditions -->
<div v-if="previewSource.startsWith('drum-')" class="layer-controls">
  <input v-model.number="selectedLayerNumber" type="number">
</div>

<!-- Alternative content -->
<div v-else class="layer-controls">
  <p>Select a drum to choose layer number</p>
</div>

<!-- Show/hide with CSS (element always exists) -->
<div v-show="showAdvancedOptions" class="advanced-options">
  <!-- Advanced controls -->
</div>

<!-- Multiple conditions -->
<div v-if="previewLoading" class="loading-state">
  <span>Loading preview...</span>
</div>
<div v-else-if="previewError" class="error-state">
  <span>Error: {{ previewError }}</span>
</div>
<div v-else-if="previewImageUrl" class="success-state">
  <img :src="previewImageUrl" alt="Layer preview">
</div>
<div v-else class="empty-state">
  <span>No preview available</span>
</div>
```

## Accessibility in Industrial Applications

### Why Accessibility Matters in Industrial Settings

1. **Legal Requirements**: Many industries have accessibility standards
2. **Operator Safety**: Clear interfaces reduce errors
3. **Efficiency**: Accessible interfaces are easier to use for everyone
4. **Future-Proofing**: Works with assistive technologies

### Accessibility Best Practices

```html
<!-- Proper labeling -->
<label for="layer-input" class="control-label">
  Layer Number
  <span class="required" aria-label="required field">*</span>
</label>

<!-- Comprehensive input attributes -->
<input 
  id="layer-input"
  v-model.number="selectedLayerNumber"
  type="number"
  :min="1"
  :max="maxLayers"
  :aria-describedby="layer-help layer-error"
  :aria-invalid="hasValidationError"
  required
  class="form-input"
>

<!-- Help text -->
<div id="layer-help" class="help-text">
  Enter a layer number between 1 and {{ maxLayers }}
</div>

<!-- Error message -->
<div 
  v-if="validationError" 
  id="layer-error" 
  class="error-message" 
  role="alert"
  aria-live="assertive"
>
  {{ validationError }}
</div>

<!-- Loading states -->
<button 
  @click="loadPreview"
  :disabled="previewLoading"
  :aria-label="previewLoading ? 'Loading preview, please wait' : 'Load layer preview'"
>
  <span v-if="previewLoading" class="sr-only">Loading...</span>
  {{ previewLoading ? 'Loading...' : 'Load Preview' }}
</button>
```

### ARIA Attributes for Dynamic Content

```html
<!-- Live regions for status updates -->
<div aria-live="polite" aria-label="Connection status">
  {{ connectionStatus }}
</div>

<!-- Progress indication -->
<div role="progressbar" 
     :aria-valuenow="uploadProgress" 
     aria-valuemin="0" 
     aria-valuemax="100"
     :aria-label="`Upload progress: ${uploadProgress}%`">
  <div class="progress-bar" :style="`width: ${uploadProgress}%`"></div>
</div>

<!-- Expandable sections -->
<button 
  @click="toggleAdvanced"
  :aria-expanded="showAdvanced"
  aria-controls="advanced-panel"
>
  Advanced Options
</button>
<div id="advanced-panel" v-show="showAdvanced">
  <!-- Advanced controls -->
</div>
```

## Form Handling Patterns

### Traditional vs Vue.js Form Processing

**Traditional Approach (Avoid):**
```html
<form onsubmit="return handleSubmit(event)">
  <input type="number" id="layer" name="layer">
  <input type="text" id="filename" name="filename">
  <button type="submit">Process</button>
</form>

<script>
function handleSubmit(event) {
  event.preventDefault()
  const formData = new FormData(event.target)
  const layer = formData.get('layer')
  const filename = formData.get('filename')
  // Manual processing...
}
</script>
```

**Vue.js Approach (Preferred):**
```html
<template>
  <form @submit.prevent="handleSubmit">
    <input v-model.number="formData.layer" type="number">
    <input v-model="formData.filename" type="text">
    <button type="submit" :disabled="!isFormValid">Process</button>
  </form>
</template>

<script setup>
const formData = reactive({
  layer: 1,
  filename: ''
})

const isFormValid = computed(() => {
  return formData.layer > 0 && formData.filename.length > 0
})

const handleSubmit = async () => {
  // formData is always current, no extraction needed
  try {
    await processForm(formData)
  } catch (error) {
    // Handle error
  }
}
</script>
```

## Real-World Example: Complete Form

Here's how our layer preview form looks with all HTML best practices:

```html
<template>
  <section class="layer-preview-form" role="region" aria-labelledby="form-title">
    <h2 id="form-title">Layer Preview Configuration</h2>
    
    <form @submit.prevent="loadPreview" novalidate>
      <!-- Preview source selection -->
      <fieldset class="form-group">
        <legend>Preview Source</legend>
        <select 
          v-model="previewSource"
          id="preview-source"
          class="form-select"
          aria-describedby="source-help"
          required
        >
          <option value="">Select source...</option>
          <option value="layer">Layer Overview</option>
          <option value="drum-0">Drum 0 (Blue)</option>
          <option value="drum-1">Drum 1 (Orange)</option>
          <option value="drum-2">Drum 2 (Green)</option>
        </select>
        <div id="source-help" class="help-text">
          Choose the preview type to display
        </div>
      </fieldset>
      
      <!-- Layer number (conditional) -->
      <fieldset 
        v-if="previewSource.startsWith('drum-')" 
        class="form-group"
      >
        <legend>Layer Selection</legend>
        <label for="layer-number" class="form-label">
          Layer Number
          <span class="required" aria-label="required">*</span>
        </label>
        <input 
          v-model.number="selectedLayerNumber"
          id="layer-number"
          type="number"
          class="form-input"
          :min="1"
          :max="getMaxLayersForSelectedDrum()"
          :aria-describedby="layer-help"
          :aria-invalid="layerError ? 'true' : 'false'"
          required
        >
        <div id="layer-help" class="help-text">
          Range: 1 to {{ getMaxLayersForSelectedDrum() || 'Unknown' }}
        </div>
        <div v-if="layerError" class="error-message" role="alert">
          {{ layerError }}
        </div>
      </fieldset>
      
      <!-- Submit button -->
      <div class="form-actions">
        <button 
          type="submit"
          class="btn btn-primary"
          :disabled="!canSubmit"
          :aria-label="submitButtonLabel"
        >
          <span v-if="previewLoading" class="loading-spinner" aria-hidden="true"></span>
          {{ previewLoading ? 'Loading...' : 'Load Preview' }}
        </button>
      </div>
    </form>
    
    <!-- Results area -->
    <div class="preview-results" aria-live="polite">
      <img 
        v-if="previewImageUrl"
        :src="previewImageUrl"
        :alt="`Layer ${selectedLayerNumber} preview`"
        class="preview-image"
      >
      <div v-else-if="previewLoading" class="loading-placeholder">
        Loading preview...
      </div>
      <div v-else class="empty-placeholder">
        No preview loaded
      </div>
    </div>
  </section>
</template>
```

## Key Takeaways

### HTML Best Practices for Vue.js
1. **Semantic Structure**: Use proper HTML5 elements
2. **Accessibility First**: Include ARIA attributes and proper labeling
3. **Form Validation**: Leverage HTML5 validation with Vue.js enhancement
4. **Progressive Enhancement**: Start with working HTML, enhance with Vue.js

### Vue.js HTML Enhancements
1. **Directives**: `v-if`, `v-show`, `v-for`, `v-model`
2. **Attribute Binding**: `:class`, `:disabled`, `:aria-label`
3. **Event Handling**: `@click`, `@submit`, `@input`
4. **Template Interpolation**: `{{ }}` for dynamic content

### Industrial Application Considerations
1. **Reliability**: Accessible and robust form handling
2. **Clarity**: Clear labeling and error messages
3. **Efficiency**: Keyboard navigation and shortcuts
4. **Safety**: Validation and confirmation for critical actions

## What's Next

Now that you understand HTML structure and its integration with Vue.js, we'll move on to styling these elements professionally.

**Chapter 4: CSS Fundamentals** will cover:
- Industrial-grade styling patterns
- Responsive design for various screen sizes
- CSS architecture in Vue.js applications
- Modern layout techniques (Flexbox, Grid)

---

**Continue your learning journey** → [Chapter 4: CSS Fundamentals](04-css-fundamentals.md)

## Practice Exercises

1. Create a semantic HTML structure for a file upload form
2. Add proper accessibility attributes to an existing form
3. Convert a static HTML form to use Vue.js directives
4. Implement conditional rendering for different user roles

*Remember: Good HTML is the foundation of great applications. Take time to understand semantic structure and accessibility - it pays dividends in maintenance and user experience.*

---

## 🚀 Mini Project: Task Dashboard - HTML Structure Implementation

### Exercise 3.1: Create Semantic HTML Foundation

Build the complete HTML structure for your Task Dashboard using semantic elements:

Create `index.html`:

```html
<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <meta name="description" content="Personal Task Dashboard - Manage your daily tasks efficiently">
    <title>Personal Task Dashboard</title>
    
    <!-- Preload critical resources -->
    <link rel="preload" href="styles/main.css" as="style">
    <link rel="stylesheet" href="styles/main.css">
    
    <!-- Icon for browser tab -->
    <link rel="icon" href="data:image/svg+xml,<svg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 100 100'><text y='.9em' font-size='90'>✅</text></svg>">
</head>
<body>
    <!-- Skip navigation for accessibility -->
    <a href="#main-content" class="skip-nav">Skip to main content</a>
    
    <!-- Application container -->
    <div id="app" class="app-container">
        <!-- Application header -->
        <header class="app-header" role="banner">
            <div class="header-content">
                <h1 class="app-title">
                    <span class="app-icon" aria-hidden="true">✅</span>
                    Task Dashboard
                </h1>
                
                <!-- Dashboard statistics -->
                <div class="stats-container" role="region" aria-label="Task statistics">
                    <div class="stat-item">
                        <span class="stat-value" id="total-tasks">0</span>
                        <span class="stat-label">Total Tasks</span>
                    </div>
                    <div class="stat-item">
                        <span class="stat-value" id="completed-tasks">0</span>
                        <span class="stat-label">Completed</span>
                    </div>
                    <div class="stat-item">
                        <span class="stat-value" id="completion-percentage">0%</span>
                        <span class="stat-label">Progress</span>
                    </div>
                </div>
                
                <!-- Theme toggle button -->
                <button 
                    id="theme-toggle" 
                    class="theme-toggle" 
                    aria-label="Toggle dark mode"
                    type="button"
                >
                    <span class="theme-icon light-icon" aria-hidden="true">☀️</span>
                    <span class="theme-icon dark-icon" aria-hidden="true">🌙</span>
                </button>
            </div>
        </header>

        <!-- Main application content -->
        <main id="main-content" class="main-content" role="main">
            <!-- Task creation form -->
            <section class="task-form-section" aria-labelledby="form-heading">
                <h2 id="form-heading" class="section-title">Add New Task</h2>
                
                <form id="task-form" class="task-form" novalidate>
                    <!-- Task title input -->
                    <div class="form-group">
                        <label for="task-title" class="form-label">
                            Task Title
                            <span class="required" aria-label="required field">*</span>
                        </label>
                        <input 
                            type="text" 
                            id="task-title" 
                            name="title"
                            class="form-input" 
                            placeholder="What needs to be done?"
                            required
                            maxlength="100"
                            aria-describedby="title-help title-error"
                        >
                        <div id="title-help" class="help-text">
                            Enter a clear, concise task description
                        </div>
                        <div id="title-error" class="error-message" role="alert" aria-live="polite">
                            <!-- Error messages will appear here -->
                        </div>
                    </div>

                    <!-- Task description textarea -->
                    <div class="form-group">
                        <label for="task-description" class="form-label">
                            Description (Optional)
                        </label>
                        <textarea 
                            id="task-description" 
                            name="description"
                            class="form-textarea" 
                            placeholder="Add more details about this task..."
                            rows="3"
                            maxlength="500"
                            aria-describedby="description-help"
                        ></textarea>
                        <div id="description-help" class="help-text">
                            Optional: Add context or details about the task
                        </div>
                    </div>

                    <!-- Form controls row -->
                    <div class="form-row">
                        <!-- Category selection -->
                        <div class="form-group">
                            <label for="task-category" class="form-label">Category</label>
                            <select 
                                id="task-category" 
                                name="category"
                                class="form-select"
                                aria-describedby="category-help"
                            >
                                <option value="">Select category...</option>
                                <option value="work">🏢 Work</option>
                                <option value="personal">🏠 Personal</option>
                                <option value="learning">📚 Learning</option>
                                <option value="health">💪 Health</option>
                            </select>
                            <div id="category-help" class="help-text">
                                Choose a category to organize your task
                            </div>
                        </div>

                        <!-- Priority selection -->
                        <div class="form-group">
                            <label for="task-priority" class="form-label">Priority</label>
                            <select 
                                id="task-priority" 
                                name="priority"
                                class="form-select"
                                aria-describedby="priority-help"
                            >
                                <option value="low">🟢 Low</option>
                                <option value="medium" selected>🟡 Medium</option>
                                <option value="high">🔴 High</option>
                            </select>
                            <div id="priority-help" class="help-text">
                                Set task priority level
                            </div>
                        </div>

                        <!-- Due date input -->
                        <div class="form-group">
                            <label for="task-due-date" class="form-label">Due Date</label>
                            <input 
                                type="date" 
                                id="task-due-date" 
                                name="dueDate"
                                class="form-input"
                                aria-describedby="date-help"
                            >
                            <div id="date-help" class="help-text">
                                Optional: Set a deadline for this task
                            </div>
                        </div>
                    </div>

                    <!-- Form submit button -->
                    <div class="form-actions">
                        <button type="submit" class="btn btn-primary" id="add-task-btn">
                            <span class="btn-icon" aria-hidden="true">➕</span>
                            Add Task
                        </button>
                        <button type="reset" class="btn btn-secondary">
                            <span class="btn-icon" aria-hidden="true">🔄</span>
                            Clear Form
                        </button>
                    </div>
                </form>
            </section>

            <!-- Task filtering and controls -->
            <section class="task-controls-section" aria-labelledby="controls-heading">
                <h2 id="controls-heading" class="section-title visually-hidden">Task Controls</h2>
                
                <div class="controls-container">
                    <!-- Search input -->
                    <div class="search-group">
                        <label for="task-search" class="visually-hidden">Search tasks</label>
                        <input 
                            type="search" 
                            id="task-search" 
                            class="search-input"
                            placeholder="🔍 Search tasks..."
                            aria-label="Search through your tasks"
                        >
                    </div>

                    <!-- Filter controls -->
                    <div class="filter-group">
                        <div class="filter-item">
                            <label for="filter-category" class="filter-label">Category:</label>
                            <select id="filter-category" class="filter-select">
                                <option value="">All Categories</option>
                                <option value="work">Work</option>
                                <option value="personal">Personal</option>
                                <option value="learning">Learning</option>
                                <option value="health">Health</option>
                            </select>
                        </div>

                        <div class="filter-item">
                            <label for="filter-status" class="filter-label">Status:</label>
                            <select id="filter-status" class="filter-select">
                                <option value="">All Tasks</option>
                                <option value="pending">Pending</option>
                                <option value="completed">Completed</option>
                                <option value="overdue">Overdue</option>
                            </select>
                        </div>

                        <div class="filter-item">
                            <label for="filter-priority" class="filter-label">Priority:</label>
                            <select id="filter-priority" class="filter-select">
                                <option value="">All Priorities</option>
                                <option value="high">High</option>
                                <option value="medium">Medium</option>
                                <option value="low">Low</option>
                            </select>
                        </div>
                    </div>

                    <!-- View controls -->
                    <div class="view-controls">
                        <button 
                            id="clear-completed-btn" 
                            class="btn btn-outline"
                            aria-label="Remove all completed tasks"
                        >
                            🗑️ Clear Completed
                        </button>
                        <button 
                            id="export-tasks-btn" 
                            class="btn btn-outline"
                            aria-label="Export tasks to file"
                        >
                            📥 Export
                        </button>
                    </div>
                </div>
            </section>

            <!-- Task list display -->
            <section class="task-list-section" aria-labelledby="task-list-heading">
                <h2 id="task-list-heading" class="section-title visually-hidden">Your Tasks</h2>
                
                <!-- Progress indicator -->
                <div class="progress-container" role="progressbar" 
                     aria-valuenow="0" aria-valuemin="0" aria-valuemax="100"
                     aria-label="Overall task completion progress">
                    <div class="progress-bar">
                        <div class="progress-fill" style="width: 0%"></div>
                    </div>
                    <span class="progress-text">0% Complete</span>
                </div>

                <!-- Task list container -->
                <div id="task-list" class="task-list" role="list">
                    <!-- Empty state -->
                    <div id="empty-state" class="empty-state">
                        <div class="empty-icon" aria-hidden="true">📝</div>
                        <h3 class="empty-title">No tasks yet!</h3>
                        <p class="empty-description">
                            Add your first task using the form above to get started.
                        </p>
                    </div>

                    <!-- Sample task (will be hidden when JavaScript loads) -->
                    <article class="task-card sample-task" role="listitem">
                        <div class="task-header">
                            <input 
                                type="checkbox" 
                                class="task-checkbox" 
                                id="sample-task-checkbox"
                                aria-label="Mark task as complete"
                            >
                            <h3 class="task-title">Sample Task Title</h3>
                            <span class="task-priority priority-medium" aria-label="Medium priority">🟡</span>
                        </div>
                        
                        <div class="task-body">
                            <p class="task-description">
                                This is a sample task description to show the layout.
                            </p>
                            <div class="task-meta">
                                <span class="task-category category-learning">📚 Learning</span>
                                <time class="task-due-date" datetime="2024-01-15">
                                    Due: Jan 15, 2024
                                </time>
                            </div>
                        </div>
                        
                        <div class="task-actions">
                            <button class="task-action-btn edit-btn" aria-label="Edit task">
                                ✏️
                            </button>
                            <button class="task-action-btn delete-btn" aria-label="Delete task">
                                🗑️
                            </button>
                        </div>
                    </article>
                </div>
            </section>
        </main>

        <!-- Application footer -->
        <footer class="app-footer" role="contentinfo">
            <div class="footer-content">
                <p class="footer-text">
                    Built with ❤️ as a learning project | 
                    <button id="data-management-btn" class="link-button">
                        Manage Data
                    </button>
                </p>
                <div class="footer-stats">
                    <span id="last-updated">Last updated: Never</span>
                </div>
            </div>
        </footer>
    </div>

    <!-- Data management modal (hidden by default) -->
    <div id="data-modal" class="modal" role="dialog" aria-labelledby="modal-title" aria-hidden="true">
        <div class="modal-content">
            <header class="modal-header">
                <h2 id="modal-title">Data Management</h2>
                <button class="modal-close" aria-label="Close modal">✕</button>
            </header>
            
            <div class="modal-body">
                <div class="data-actions">
                    <button class="btn btn-outline" id="import-data-btn">
                        📤 Import Tasks
                    </button>
                    <button class="btn btn-outline" id="export-data-btn">
                        📥 Export Tasks  
                    </button>
                    <button class="btn btn-danger" id="clear-all-data-btn">
                        🗑️ Clear All Data
                    </button>
                </div>
                
                <div class="data-info">
                    <h3>Storage Information</h3>
                    <ul>
                        <li>Tasks stored locally in your browser</li>
                        <li>Data persists between sessions</li>
                        <li>Export to backup your tasks</li>
                    </ul>
                </div>
            </div>
        </div>
    </div>

    <!-- File input for import (hidden) -->
    <input type="file" id="import-file-input" accept=".json" style="display: none;">

    <!-- JavaScript will be loaded here -->
    <script defer src="scripts/main.js"></script>
</body>
</html>
```

### Exercise 3.2: Accessibility Audit

Create an accessibility checklist for your HTML:

1. **Semantic Structure**
   - [ ] Uses proper heading hierarchy (h1, h2, h3)
   - [ ] Includes landmark roles (banner, main, contentinfo)
   - [ ] Uses semantic elements (header, main, section, article)

2. **Form Accessibility**
   - [ ] All inputs have associated labels
   - [ ] Required fields are marked with aria-required or required
   - [ ] Form validation uses aria-describedby and role="alert"
   - [ ] Fieldsets group related form controls

3. **Interactive Elements**
   - [ ] All buttons have meaningful text or aria-label
   - [ ] Focus management is logical
   - [ ] Keyboard navigation works throughout
   - [ ] Links and buttons are distinguishable

4. **Screen Reader Support**
   - [ ] Skip navigation link provided
   - [ ] Progress indicators use role="progressbar"
   - [ ] Dynamic content uses aria-live regions
   - [ ] Decorative elements marked with aria-hidden="true"

Create `docs/accessibility-checklist.md` documenting your accessibility decisions.

### Exercise 3.3: Form Validation Strategy

Plan your HTML5 form validation approach:

```html
<!-- Example validation attributes -->
<input 
    type="text" 
    required
    minlength="3"
    maxlength="100"
    pattern="[A-Za-z0-9\s]+"
    title="Only letters, numbers, and spaces allowed"
    aria-invalid="false"
    aria-describedby="title-error"
>
<div id="title-error" class="error-message" role="alert"></div>
```

Document in `docs/form-validation.md`:
1. What HTML5 validation attributes you use
2. How you'll enhance validation with JavaScript
3. Error message display strategy
4. Accessibility considerations for validation

### Exercise 3.4: Data Attributes Strategy

Add data attributes for JavaScript functionality:

```html
<!-- Example task card with data attributes -->
<article class="task-card" 
         data-task-id="1"
         data-category="learning"
         data-priority="high"
         data-completed="false">
    <!-- Task content -->
</article>
```

Plan your data attribute naming convention in `docs/data-attributes.md`:
1. Naming conventions you'll follow
2. What data needs to be accessible from JavaScript
3. How you'll handle data updates
4. Performance considerations

### Success Criteria
- [ ] Complete HTML structure implemented
- [ ] All semantic elements used appropriately
- [ ] Full accessibility compliance achieved
- [ ] Form validation attributes added
- [ ] Data attributes planned for JavaScript integration
- [ ] Sample content shows the intended layout
- [ ] Code validates with HTML5 validator

### 💡 Key Learning Points

1. **Semantic HTML**: Using the right elements for the right purpose
2. **Accessibility First**: Building inclusive interfaces from the start
3. **Progressive Enhancement**: HTML works before CSS and JavaScript
4. **Data Architecture**: Planning how HTML connects to JavaScript

### 🎯 Looking Ahead

In Chapter 4, you'll style this HTML structure with professional CSS, creating a beautiful and responsive interface. The semantic foundation you've built will make styling much easier and more maintainable.

**Your HTML is the foundation** - make sure it's solid before adding styles and interactivity!