"""
Polling Configuration for Conflict Resolution
=============================================

Centralized polling configuration to resolve temporal synchronization conflicts
between multiple independent polling systems. Implements staggered intervals to
prevent simultaneous polling that causes UI flickering and state update races.

Design Principles:
- Stagger intervals by 200-500ms to break synchronization
- Maintain similar response times for user experience  
- Allow dynamic adjustment based on system state
- Provide error state debouncing configuration

Environment Variables:
- WEBSOCKET_POLL_INTERVAL: Backend WebSocket status polling (default: 2.0s)
- FRONTEND_JOB_POLL_INTERVAL: Frontend job status polling (default: 2.3s)  
- LAYER_COMPLETION_POLL_INTERVAL: Layer completion polling (default: 2.5s)
- PRINT_VIEW_POLL_INTERVAL: Print view job polling (default: 2.7s)
- ERROR_STATE_DEBOUNCE_MS: Error state change debouncing (default: 500ms)
- ERROR_MESSAGE_PRIORITY_WINDOW_MS: Priority window for error messages (default: 1000ms)
"""

import os
from dataclasses import dataclass
from typing import Dict, Any


@dataclass
class PollingConfig:
    """Centralized polling configuration with staggered intervals."""
    
    # Backend polling intervals (seconds)
    websocket_poll_interval: float = 2.0
    layer_completion_poll_interval: float = 2.5
    
    # Frontend polling intervals (seconds) 
    frontend_job_poll_interval: float = 2.3
    print_view_poll_interval: float = 2.7
    job_progress_poll_interval: float = 2.1
    multilayer_control_poll_interval: float = 2.9
    
    # Error handling configuration (milliseconds)
    error_state_debounce_ms: int = 500
    error_message_priority_window_ms: int = 1000
    error_clearing_timeout_ms: int = 5000
    
    # System state adjustments
    idle_polling_multiplier: float = 1.5  # Slower when idle
    active_job_multiplier: float = 0.8   # Faster during active jobs
    error_state_multiplier: float = 0.6  # Fastest during errors


def get_polling_config() -> PollingConfig:
    """Load polling configuration from environment with staggered defaults.
    
    Returns:
        PollingConfig: Configuration with anti-conflict intervals
    """
    return PollingConfig(
        # Backend intervals - staggered by 500ms increments
        websocket_poll_interval=float(os.getenv("WEBSOCKET_POLL_INTERVAL", "2.0")),
        layer_completion_poll_interval=float(os.getenv("LAYER_COMPLETION_POLL_INTERVAL", "2.5")),
        
        # Frontend intervals - staggered by 300ms increments  
        frontend_job_poll_interval=float(os.getenv("FRONTEND_JOB_POLL_INTERVAL", "2.3")),
        print_view_poll_interval=float(os.getenv("PRINT_VIEW_POLL_INTERVAL", "2.7")),
        job_progress_poll_interval=float(os.getenv("JOB_PROGRESS_POLL_INTERVAL", "2.1")),
        multilayer_control_poll_interval=float(os.getenv("MULTILAYER_CONTROL_POLL_INTERVAL", "2.9")),
        
        # Error handling configuration
        error_state_debounce_ms=int(os.getenv("ERROR_STATE_DEBOUNCE_MS", "500")),
        error_message_priority_window_ms=int(os.getenv("ERROR_MESSAGE_PRIORITY_WINDOW_MS", "1000")),
        error_clearing_timeout_ms=int(os.getenv("ERROR_CLEARING_TIMEOUT_MS", "5000")),
        
        # System state multipliers
        idle_polling_multiplier=float(os.getenv("IDLE_POLLING_MULTIPLIER", "1.5")),
        active_job_multiplier=float(os.getenv("ACTIVE_JOB_MULTIPLIER", "0.8")),
        error_state_multiplier=float(os.getenv("ERROR_STATE_MULTIPLIER", "0.6"))
    )


def get_adjusted_interval(base_interval: float, system_state: str = "normal") -> float:
    """Get polling interval adjusted for current system state.
    
    Args:
        base_interval: Base polling interval in seconds
        system_state: Current system state ("idle", "active_job", "error", "normal")
        
    Returns:
        Adjusted polling interval in seconds
    """
    config = get_polling_config()
    
    multipliers = {
        "idle": config.idle_polling_multiplier,
        "active_job": config.active_job_multiplier, 
        "error": config.error_state_multiplier,
        "normal": 1.0
    }
    
    multiplier = multipliers.get(system_state, 1.0)
    return base_interval * multiplier


def get_polling_intervals_summary() -> Dict[str, Any]:
    """Get summary of all configured polling intervals for debugging.
    
    Returns:
        Dictionary with all polling intervals and configuration
    """
    config = get_polling_config()
    
    return {
        "backend_intervals": {
            "websocket_poll": config.websocket_poll_interval,
            "layer_completion_poll": config.layer_completion_poll_interval,
        },
        "frontend_intervals": {
            "job_poll": config.frontend_job_poll_interval,
            "print_view_poll": config.print_view_poll_interval,
            "job_progress_poll": config.job_progress_poll_interval,
            "multilayer_control_poll": config.multilayer_control_poll_interval,
        },
        "error_handling": {
            "debounce_ms": config.error_state_debounce_ms,
            "priority_window_ms": config.error_message_priority_window_ms,
            "clearing_timeout_ms": config.error_clearing_timeout_ms,
        },
        "stagger_analysis": {
            "min_interval": min(
                config.websocket_poll_interval,
                config.frontend_job_poll_interval,
                config.layer_completion_poll_interval,
                config.print_view_poll_interval
            ),
            "max_interval": max(
                config.websocket_poll_interval,
                config.frontend_job_poll_interval,
                config.layer_completion_poll_interval,
                config.print_view_poll_interval
            ),
            "interval_spread": "Staggered to prevent synchronization conflicts"
        }
    }