<!DOCTYPE html><html><head>
      <title>DevelopersGuide</title>
      <meta charset="utf-8">
      <meta name="viewport" content="width=device-width, initial-scale=1.0">
      
      <link rel="stylesheet" href="file:///c:\Users\<USER>\.vscode\extensions\shd101wyy.markdown-preview-enhanced-0.8.19\crossnote\dependencies\katex\katex.min.css">
      
      
      <script type="text/javascript" src="file:///c:\Users\<USER>\.vscode\extensions\shd101wyy.markdown-preview-enhanced-0.8.19\crossnote\dependencies\mermaid\mermaid.min.js" charset="UTF-8"></script>
      
      
      <style>
      code[class*=language-],pre[class*=language-]{color:#333;background:0 0;font-family:Consolas,"Liberation Mono",Menlo,Courier,monospace;text-align:left;white-space:pre;word-spacing:normal;word-break:normal;word-wrap:normal;line-height:1.4;-moz-tab-size:8;-o-tab-size:8;tab-size:8;-webkit-hyphens:none;-moz-hyphens:none;-ms-hyphens:none;hyphens:none}pre[class*=language-]{padding:.8em;overflow:auto;border-radius:3px;background:#f5f5f5}:not(pre)>code[class*=language-]{padding:.1em;border-radius:.3em;white-space:normal;background:#f5f5f5}.token.blockquote,.token.comment{color:#969896}.token.cdata{color:#183691}.token.doctype,.token.macro.property,.token.punctuation,.token.variable{color:#333}.token.builtin,.token.important,.token.keyword,.token.operator,.token.rule{color:#a71d5d}.token.attr-value,.token.regex,.token.string,.token.url{color:#183691}.token.atrule,.token.boolean,.token.code,.token.command,.token.constant,.token.entity,.token.number,.token.property,.token.symbol{color:#0086b3}.token.prolog,.token.selector,.token.tag{color:#63a35c}.token.attr-name,.token.class,.token.class-name,.token.function,.token.id,.token.namespace,.token.pseudo-class,.token.pseudo-element,.token.url-reference .token.variable{color:#795da3}.token.entity{cursor:help}.token.title,.token.title .token.punctuation{font-weight:700;color:#1d3e81}.token.list{color:#ed6a43}.token.inserted{background-color:#eaffea;color:#55a532}.token.deleted{background-color:#ffecec;color:#bd2c00}.token.bold{font-weight:700}.token.italic{font-style:italic}.language-json .token.property{color:#183691}.language-markup .token.tag .token.punctuation{color:#333}.language-css .token.function,code.language-css{color:#0086b3}.language-yaml .token.atrule{color:#63a35c}code.language-yaml{color:#183691}.language-ruby .token.function{color:#333}.language-markdown .token.url{color:#795da3}.language-makefile .token.symbol{color:#795da3}.language-makefile .token.variable{color:#183691}.language-makefile .token.builtin{color:#0086b3}.language-bash .token.keyword{color:#0086b3}pre[data-line]{position:relative;padding:1em 0 1em 3em}pre[data-line] .line-highlight-wrapper{position:absolute;top:0;left:0;background-color:transparent;display:block;width:100%}pre[data-line] .line-highlight{position:absolute;left:0;right:0;padding:inherit 0;margin-top:1em;background:hsla(24,20%,50%,.08);background:linear-gradient(to right,hsla(24,20%,50%,.1) 70%,hsla(24,20%,50%,0));pointer-events:none;line-height:inherit;white-space:pre}pre[data-line] .line-highlight:before,pre[data-line] .line-highlight[data-end]:after{content:attr(data-start);position:absolute;top:.4em;left:.6em;min-width:1em;padding:0 .5em;background-color:hsla(24,20%,50%,.4);color:#f4f1ef;font:bold 65%/1.5 sans-serif;text-align:center;vertical-align:.3em;border-radius:999px;text-shadow:none;box-shadow:0 1px #fff}pre[data-line] .line-highlight[data-end]:after{content:attr(data-end);top:auto;bottom:.4em}html body{font-family:'Helvetica Neue',Helvetica,'Segoe UI',Arial,freesans,sans-serif;font-size:16px;line-height:1.6;color:#333;background-color:#fff;overflow:initial;box-sizing:border-box;word-wrap:break-word}html body>:first-child{margin-top:0}html body h1,html body h2,html body h3,html body h4,html body h5,html body h6{line-height:1.2;margin-top:1em;margin-bottom:16px;color:#000}html body h1{font-size:2.25em;font-weight:300;padding-bottom:.3em}html body h2{font-size:1.75em;font-weight:400;padding-bottom:.3em}html body h3{font-size:1.5em;font-weight:500}html body h4{font-size:1.25em;font-weight:600}html body h5{font-size:1.1em;font-weight:600}html body h6{font-size:1em;font-weight:600}html body h1,html body h2,html body h3,html body h4,html body h5{font-weight:600}html body h5{font-size:1em}html body h6{color:#5c5c5c}html body strong{color:#000}html body del{color:#5c5c5c}html body a:not([href]){color:inherit;text-decoration:none}html body a{color:#08c;text-decoration:none}html body a:hover{color:#00a3f5;text-decoration:none}html body img{max-width:100%}html body>p{margin-top:0;margin-bottom:16px;word-wrap:break-word}html body>ol,html body>ul{margin-bottom:16px}html body ol,html body ul{padding-left:2em}html body ol.no-list,html body ul.no-list{padding:0;list-style-type:none}html body ol ol,html body ol ul,html body ul ol,html body ul ul{margin-top:0;margin-bottom:0}html body li{margin-bottom:0}html body li.task-list-item{list-style:none}html body li>p{margin-top:0;margin-bottom:0}html body .task-list-item-checkbox{margin:0 .2em .25em -1.8em;vertical-align:middle}html body .task-list-item-checkbox:hover{cursor:pointer}html body blockquote{margin:16px 0;font-size:inherit;padding:0 15px;color:#5c5c5c;background-color:#f0f0f0;border-left:4px solid #d6d6d6}html body blockquote>:first-child{margin-top:0}html body blockquote>:last-child{margin-bottom:0}html body hr{height:4px;margin:32px 0;background-color:#d6d6d6;border:0 none}html body table{margin:10px 0 15px 0;border-collapse:collapse;border-spacing:0;display:block;width:100%;overflow:auto;word-break:normal;word-break:keep-all}html body table th{font-weight:700;color:#000}html body table td,html body table th{border:1px solid #d6d6d6;padding:6px 13px}html body dl{padding:0}html body dl dt{padding:0;margin-top:16px;font-size:1em;font-style:italic;font-weight:700}html body dl dd{padding:0 16px;margin-bottom:16px}html body code{font-family:Menlo,Monaco,Consolas,'Courier New',monospace;font-size:.85em;color:#000;background-color:#f0f0f0;border-radius:3px;padding:.2em 0}html body code::after,html body code::before{letter-spacing:-.2em;content:'\00a0'}html body pre>code{padding:0;margin:0;word-break:normal;white-space:pre;background:0 0;border:0}html body .highlight{margin-bottom:16px}html body .highlight pre,html body pre{padding:1em;overflow:auto;line-height:1.45;border:#d6d6d6;border-radius:3px}html body .highlight pre{margin-bottom:0;word-break:normal}html body pre code,html body pre tt{display:inline;max-width:initial;padding:0;margin:0;overflow:initial;line-height:inherit;word-wrap:normal;background-color:transparent;border:0}html body pre code:after,html body pre code:before,html body pre tt:after,html body pre tt:before{content:normal}html body blockquote,html body dl,html body ol,html body p,html body pre,html body ul{margin-top:0;margin-bottom:16px}html body kbd{color:#000;border:1px solid #d6d6d6;border-bottom:2px solid #c7c7c7;padding:2px 4px;background-color:#f0f0f0;border-radius:3px}@media print{html body{background-color:#fff}html body h1,html body h2,html body h3,html body h4,html body h5,html body h6{color:#000;page-break-after:avoid}html body blockquote{color:#5c5c5c}html body pre{page-break-inside:avoid}html body table{display:table}html body img{display:block;max-width:100%;max-height:100%}html body code,html body pre{word-wrap:break-word;white-space:pre}}.markdown-preview{width:100%;height:100%;box-sizing:border-box}.markdown-preview ul{list-style:disc}.markdown-preview ul ul{list-style:circle}.markdown-preview ul ul ul{list-style:square}.markdown-preview ol{list-style:decimal}.markdown-preview ol ol,.markdown-preview ul ol{list-style-type:lower-roman}.markdown-preview ol ol ol,.markdown-preview ol ul ol,.markdown-preview ul ol ol,.markdown-preview ul ul ol{list-style-type:lower-alpha}.markdown-preview .newpage,.markdown-preview .pagebreak{page-break-before:always}.markdown-preview pre.line-numbers{position:relative;padding-left:3.8em;counter-reset:linenumber}.markdown-preview pre.line-numbers>code{position:relative}.markdown-preview pre.line-numbers .line-numbers-rows{position:absolute;pointer-events:none;top:1em;font-size:100%;left:0;width:3em;letter-spacing:-1px;border-right:1px solid #999;-webkit-user-select:none;-moz-user-select:none;-ms-user-select:none;user-select:none}.markdown-preview pre.line-numbers .line-numbers-rows>span{pointer-events:none;display:block;counter-increment:linenumber}.markdown-preview pre.line-numbers .line-numbers-rows>span:before{content:counter(linenumber);color:#999;display:block;padding-right:.8em;text-align:right}.markdown-preview .mathjax-exps .MathJax_Display{text-align:center!important}.markdown-preview:not([data-for=preview]) .code-chunk .code-chunk-btn-group{display:none}.markdown-preview:not([data-for=preview]) .code-chunk .status{display:none}.markdown-preview:not([data-for=preview]) .code-chunk .output-div{margin-bottom:16px}.markdown-preview .md-toc{padding:0}.markdown-preview .md-toc .md-toc-link-wrapper .md-toc-link{display:inline;padding:.25rem 0}.markdown-preview .md-toc .md-toc-link-wrapper .md-toc-link div,.markdown-preview .md-toc .md-toc-link-wrapper .md-toc-link p{display:inline}.markdown-preview .md-toc .md-toc-link-wrapper.highlighted .md-toc-link{font-weight:800}.scrollbar-style::-webkit-scrollbar{width:8px}.scrollbar-style::-webkit-scrollbar-track{border-radius:10px;background-color:transparent}.scrollbar-style::-webkit-scrollbar-thumb{border-radius:5px;background-color:rgba(150,150,150,.66);border:4px solid rgba(150,150,150,.66);background-clip:content-box}html body[for=html-export]:not([data-presentation-mode]){position:relative;width:100%;height:100%;top:0;left:0;margin:0;padding:0;overflow:auto}html body[for=html-export]:not([data-presentation-mode]) .markdown-preview{position:relative;top:0;min-height:100vh}@media screen and (min-width:914px){html body[for=html-export]:not([data-presentation-mode]) .markdown-preview{padding:2em calc(50% - 457px + 2em)}}@media screen and (max-width:914px){html body[for=html-export]:not([data-presentation-mode]) .markdown-preview{padding:2em}}@media screen and (max-width:450px){html body[for=html-export]:not([data-presentation-mode]) .markdown-preview{font-size:14px!important;padding:1em}}@media print{html body[for=html-export]:not([data-presentation-mode]) #sidebar-toc-btn{display:none}}html body[for=html-export]:not([data-presentation-mode]) #sidebar-toc-btn{position:fixed;bottom:8px;left:8px;font-size:28px;cursor:pointer;color:inherit;z-index:99;width:32px;text-align:center;opacity:.4}html body[for=html-export]:not([data-presentation-mode])[html-show-sidebar-toc] #sidebar-toc-btn{opacity:1}html body[for=html-export]:not([data-presentation-mode])[html-show-sidebar-toc] .md-sidebar-toc{position:fixed;top:0;left:0;width:300px;height:100%;padding:32px 0 48px 0;font-size:14px;box-shadow:0 0 4px rgba(150,150,150,.33);box-sizing:border-box;overflow:auto;background-color:inherit}html body[for=html-export]:not([data-presentation-mode])[html-show-sidebar-toc] .md-sidebar-toc::-webkit-scrollbar{width:8px}html body[for=html-export]:not([data-presentation-mode])[html-show-sidebar-toc] .md-sidebar-toc::-webkit-scrollbar-track{border-radius:10px;background-color:transparent}html body[for=html-export]:not([data-presentation-mode])[html-show-sidebar-toc] .md-sidebar-toc::-webkit-scrollbar-thumb{border-radius:5px;background-color:rgba(150,150,150,.66);border:4px solid rgba(150,150,150,.66);background-clip:content-box}html body[for=html-export]:not([data-presentation-mode])[html-show-sidebar-toc] .md-sidebar-toc a{text-decoration:none}html body[for=html-export]:not([data-presentation-mode])[html-show-sidebar-toc] .md-sidebar-toc .md-toc{padding:0 16px}html body[for=html-export]:not([data-presentation-mode])[html-show-sidebar-toc] .md-sidebar-toc .md-toc .md-toc-link-wrapper .md-toc-link{display:inline;padding:.25rem 0}html body[for=html-export]:not([data-presentation-mode])[html-show-sidebar-toc] .md-sidebar-toc .md-toc .md-toc-link-wrapper .md-toc-link div,html body[for=html-export]:not([data-presentation-mode])[html-show-sidebar-toc] .md-sidebar-toc .md-toc .md-toc-link-wrapper .md-toc-link p{display:inline}html body[for=html-export]:not([data-presentation-mode])[html-show-sidebar-toc] .md-sidebar-toc .md-toc .md-toc-link-wrapper.highlighted .md-toc-link{font-weight:800}html body[for=html-export]:not([data-presentation-mode])[html-show-sidebar-toc] .markdown-preview{left:300px;width:calc(100% - 300px);padding:2em calc(50% - 457px - 300px / 2);margin:0;box-sizing:border-box}@media screen and (max-width:1274px){html body[for=html-export]:not([data-presentation-mode])[html-show-sidebar-toc] .markdown-preview{padding:2em}}@media screen and (max-width:450px){html body[for=html-export]:not([data-presentation-mode])[html-show-sidebar-toc] .markdown-preview{width:100%}}html body[for=html-export]:not([data-presentation-mode]):not([html-show-sidebar-toc]) .markdown-preview{left:50%;transform:translateX(-50%)}html body[for=html-export]:not([data-presentation-mode]):not([html-show-sidebar-toc]) .md-sidebar-toc{display:none}
/* Please visit the URL below for more information: */
/*   https://shd101wyy.github.io/markdown-preview-enhanced/#/customize-css */

      </style>
      <!-- The content below will be included at the end of the <head> element. --><script type="text/javascript">
  document.addEventListener("DOMContentLoaded", function () {
    // your code here
  });
</script></head><body for="html-export">
    
    
      <div class="crossnote markdown-preview  ">
      
<h1 id="recoater-hmi-developer-guide">Recoater HMI Developer Guide </h1>
<p>Recoater HMI is a full-stack web application designed to manage and control the multi-material Aerosint Recoater system. This guide offers a technical breakdown of the project's architecture and APIs for developers who wish to contribute to the project.</p>
<h2 id="table-of-contents">Table of Contents </h2>
<ul>
<li><a href="#1-introduction">1. Introduction</a>
<ul>
<li><a href="#11-project-overview">1.1 Project Overview</a></li>
<li><a href="#12-target-audience">1.2 Target Audience</a></li>
<li><a href="#13-prerequisites">1.3 Prerequisites</a></li>
<li><a href="#14-technology-stack">1.4 Technology Stack</a></li>
</ul>
</li>
<li><a href="#2-system-architecture">2. System Architecture</a>
<ul>
<li><a href="#21-architecture-diagram">2.1 Architecture Diagram</a></li>
<li><a href="#22-architectural-pattern-analysis">2.2 Architectural Pattern Analysis</a></li>
</ul>
</li>
<li><a href="#3-backend-development">3. Backend Development</a>
<ul>
<li><a href="#31-3-layer-clean-architecture-overview">3.1 3-Layer Clean Architecture Overview</a>
<ul>
<li><a href="#311-architecture-diagram">3.1.1 Architecture Diagram</a></li>
<li><a href="#312-architectural-pattern-analysis">3.1.2 Architectural Pattern Analysis</a></li>
</ul>
</li>
<li><a href="#32-presentation-layer">3.2 Presentation Layer</a>
<ul>
<li><a href="#321-api-structure-and-organization">3.2.1 API Structure and Organization</a>
<ul>
<li><a href="#3211-api-architecture-overview">******* API Architecture Overview</a></li>
<li><a href="#3212-api-design-principles">******* API Design Principles</a></li>
</ul>
</li>
<li><a href="#322-system-apis">3.2.2 System APIs</a>
<ul>
<li><a href="#3221-configuration-management-api">******* Configuration Management API</a>
<ul>
<li><a href="#get-config">GET /config</a></li>
<li><a href="#put-config">PUT /config</a></li>
</ul>
</li>
<li><a href="#3222-system-status-api">******* System Status API</a>
<ul>
<li><a href="#get-status">GET /status</a></li>
<li><a href="#get-statushealth">GET /status/health</a></li>
<li><a href="#post-statusstate">POST /status/state</a></li>
</ul>
</li>
<li><a href="#3223-error-management-api">******* Error Management API</a>
<ul>
<li><a href="#post-errorsclear">POST /errors/clear</a></li>
</ul>
</li>
</ul>
</li>
<li><a href="#323-print-job-management-apis">3.2.3 Print Job Management APIs</a>
<ul>
<li><a href="#3231-cli-file-processing-api">******* CLI File Processing API</a>
<ul>
<li><a href="#post-cliupload">POST /cli/upload</a></li>
<li><a href="#post-cliuploaddrum_id">POST /cli/upload/drum_id</a></li>
<li><a href="#get-clifile_idlayerlayer_numpreview">GET /cli/{file_id}/layer/{layer_num}/preview</a></li>
<li><a href="#post-clifile_idlayerlayer_numsenddrum_id">POST /cli/{file_id}/layer/{layer_num}/send/drum_id</a></li>
<li><a href="#post-clifile_idlayerssendddrum_id">POST /cli/{file_id}/layers/send/{drum_id}</a></li>
</ul>
</li>
<li><a href="#3232-multi-material-job-orchestration-api">******* Multi-Material Job Orchestration API</a>
<ul>
<li><a href="#post-clistart-multimaterial-job">POST /cli/start-multimaterial-job</a></li>
<li><a href="#get-multimaterial-jobstatus">GET /multimaterial-job/status</a></li>
<li><a href="#post-multimaterial-jobcancel">POST /multimaterial-job/cancel</a></li>
</ul>
</li>
<li><a href="#3233-cache-management-api">******* Cache Management API</a>
<ul>
<li><a href="#get-clidrum-cache-status">GET /cli/drum-cache-status</a></li>
<li><a href="#post-cliclear-drum-cachedrum_id">POST /cli/clear-drum-cache/{drum_id}</a></li>
<li><a href="#get-multimaterial-jobdrum-statusdrum_id">GET /multimaterial-job/drum-status/{drum_id}</a></li>
</ul>
</li>
<li><a href="#3234-drum-management-apis">******* Drum Management APIs</a>
<ul>
<li><a href="#get-drumsdrum_idgeometry">GET /drums/{drum_id}/geometry</a></li>
<li><a href="#get-drumsdrum_idgeometrypreview">GET /drums/{drum_id}/geometry/preview</a></li>
<li><a href="#post-drumsdrum_idgeometry">POST /drums/{drum_id}/geometry</a></li>
<li><a href="#delete-drumsdrum_idgeometry">DELETE /drums/{drum_id}/geometry</a></li>
</ul>
</li>
<li><a href="#3235-layer-parameter--preview-apis">******* Layer Parameter &amp; Preview APIs</a>
<ul>
<li><a href="#get-layerparameters">GET /layer/parameters</a></li>
<li><a href="#put-layerparameters">PUT /layer/parameters</a></li>
<li><a href="#get-layerpreview">GET /layer/preview</a></li>
</ul>
</li>
<li><a href="#3236-job-management-apis">******* Job Management APIs</a>
<ul>
<li><a href="#post-job">POST /job</a></li>
<li><a href="#delete-job">DELETE /job</a></li>
<li><a href="#get-jobstatus">GET /job/status</a></li>
</ul>
</li>
</ul>
</li>
<li><a href="#324-recoater-hardware-control-apis">3.2.4 Recoater Hardware Control APIs</a>
<ul>
<li><a href="#3241-drum-control-api">******* Drum Control API</a>
<ul>
<li><a href="#get-drumsdrum_idmotion">GET /drums/{drum_id}/motion</a></li>
<li><a href="#post-drumsdrum_idmotion">POST /drums/{drum_id}/motion</a></li>
<li><a href="#delete-drumsdrum_idmotion">DELETE /drums/{drum_id}/motion</a></li>
<li><a href="#get-drumsdrum_idejection">GET /drums/{drum_id}/ejection</a></li>
<li><a href="#put-drumsdrum_idejection">PUT /drums/{drum_id}/ejection</a></li>
<li><a href="#get-drumsdrum_idsuction">GET /drums/{drum_id}/suction</a></li>
<li><a href="#put-drumsdrum_idsuction">PUT /drums/{drum_id}/suction</a></li>
</ul>
</li>
<li><a href="#3242-leveler-control-api">3.2.4.2 Leveler Control API</a>
<ul>
<li><a href="#get-levelerpressure">GET /leveler/pressure</a></li>
<li><a href="#put-levelerpressure">PUT /leveler/pressure</a></li>
<li><a href="#get-levelersensor">GET /leveler/sensor</a></li>
</ul>
</li>
<li><a href="#3243-blade-control-api">******* Blade Control API</a>
<ul>
<li><a href="#get-drumsdrum_idbladescrews">GET /drums/{drum_id}/blade/screws</a></li>
<li><a href="#get-drumsdrum_idbladescrewsmotion">GET /drums/{drum_id}/blade/screws/motion</a></li>
<li><a href="#post-drumsdrum_idbladescrewsmotion">POST /drums/{drum_id}/blade/screws/motion</a></li>
<li><a href="#delete-drumsdrum_idbladescrewsmotion">DELETE /drums/{drum_id}/blade/screws/motion</a></li>
<li><a href="#get-drumsdrum_idbladescrewsscrew_id">GET /drums/{drum_id}/blade/screws/{screw_id}</a></li>
<li><a href="#get-drumsdrum_idbladescrewsscrew_idmotion">GET /drums/{drum_id}/blade/screws/{screw_id}/motion</a></li>
<li><a href="#post-drumsdrum_idbladescrewsscrew_idmotion">POST /drums/{drum_id}/blade/screws/{screw_id}/motion</a></li>
<li><a href="#delete-drumsdrum_idbladescrewsscrew_idmotion">DELETE /drums/{drum_id}/blade/screws/{screw_id}/motion</a></li>
</ul>
</li>
</ul>
</li>
</ul>
</li>
<li><a href="#33-service-layer-business-logic">3.3 Service Layer (Business Logic)</a></li>
<li><a href="#34-infrastructure-layer">3.4 Infrastructure Layer</a></li>
<li><a href="#35-overall-backend-workflow">3.5 Overall Backend Workflow</a>
<ul>
<li><a href="#351-router-registration-flow">3.5.1 Router Registration Flow</a></li>
</ul>
</li>
</ul>
</li>
<li><a href="#4-frontend-development">4. Frontend Development</a>
<ul>
<li><a href="#41-frontend-layered-architecture">4.1 Frontend Layered Architecture</a></li>
<li><a href="#42-component-development">4.2 Component Development</a></li>
<li><a href="#43-state-management">4.3 State Management</a></li>
<li><a href="#44-routing">4.4 Routing</a></li>
<li><a href="#45-api-integration">4.5 API Integration</a></li>
<li><a href="#46-real-time-features">4.6 Real-time Features</a></li>
<li><a href="#47-styling-and-ui">4.7 Styling and UI</a></li>
</ul>
</li>
<li><a href="#5-api-documentation">5. API Documentation</a>
<ul>
<li><a href="#51-api-overview">5.1 API Overview</a></li>
<li><a href="#52-authentication">5.2 Authentication</a></li>
<li><a href="#53-endpoint-reference">5.3 Endpoint Reference</a></li>
<li><a href="#54-websocket-events">5.4 WebSocket Events</a></li>
<li><a href="#55-error-codes">5.5 Error Codes</a></li>
<li><a href="#56-rate-limiting">5.6 Rate Limiting</a></li>
</ul>
</li>
</ul>
<hr>
<h2 id="1-introduction">1. Introduction </h2>
<h3 id="11-project-overview">1.1 Project Overview </h3>
<p>Recoater HMI originates from a need to replace the default SwaggerUI and API endpoints on the multi-material Aerosint Recoater system with a more user-friendly interface for users.</p>
<h3 id="12-target-audience">1.2 Target Audience </h3>
<ul>
<li>Developers joining the project</li>
<li>Ambitious engineers who wish to try their hands at prompt engineering the development of an industrial application</li>
<li>Interns who are tasked with fixing existing bugs and expanding the program's capabilities</li>
</ul>
<h3 id="13-prerequisites">1.3 Prerequisites </h3>
<ul>
<li>Backend: Python 3.9+, basic FastAPI and asyncio familiarity</li>
<li>Frontend: Node.js v16+ LTS, npm or pnpm, basic Vue 3 and Pinia familiarity</li>
<li>Optional: Knowledge of OPC UA &amp; asyncua or alternatively a very good grasp of prompt/context engineering and spec-driven development for agentic systems or whichever frontier agentic software development technique is trending</li>
<li>Hardware: Access to Aerosint Recoater system (or mock mode for development)</li>
</ul>
<h3 id="14-technology-stack">1.4 Technology Stack </h3>
<ul>
<li>Backend: FastAPI, Pydantic, WebSocket, OPC UA client library, uvicorn, pytest</li>
<li>Frontend: Vue 3 (Composition API), Pinia, Vue Router, Axios HTTP client, Vite</li>
<li>Communication: REST API, WebSocket real-time updates, OPC UA industrial protocol</li>
<li>Tooling: Python/JS test frameworks, ESLint, Prettier</li>
</ul>
<h2 id="2-system-architecture">2. System Architecture </h2>
<h3 id="21-architecture-diagram">2.1 Architecture Diagram </h3>
<div class="mermaid">graph TB
    subgraph "Frontend Application"
        subgraph "Presentation Layer (Views)"
            STATUS_VIEW["StatusView"]
            RECOATER_VIEW["RecoaterView"]
            PRINT_VIEW["PrintView"]
            CONFIG_VIEW["ConfigurationView"]
            DEBUG_VIEW["DebugView"]
        end

        subgraph "Component Layer"
            STATUS_INDICATOR["StatusIndicator"]
            DRUM_CONTROL["DrumControl"]
            LEVELER_CONTROL["LevelerControl"]
            FILE_UPLOAD["FileUploadColumn"]
            JOB_PROGRESS["JobProgressDisplay"]
            MULTI_LAYER_JOB["MultiLayerJobControl"]
            ERROR_MODAL["CriticalErrorModal"]
            ERROR_DISPLAY["ErrorDisplayPanel"]
        end

        subgraph "Application State Layer (Pinia Stores)"
            STATUS_STORE["statusStore"]
            PRINT_JOB_STORE["printJobStore"]
        end

        subgraph "Service Layer"
            API_SERVICE["apiService"]
        end

        subgraph "Infrastructure Layer"
            ROUTER["Vue Router"]
            WEBSOCKET["WebSocket Client"]
            HTTP_CLIENT["Axios HTTP Client"]
        end
    end

    subgraph "Backend"
        subgraph "Presentation Layer"
            SYSTEM_APIS["System APIs"]
            subgraph "Print APIs"
                CLI_API["CLI API"]
                LAYER_API["Layer API"]
                MM_API["Multi-Material API"]
                JOB_API["Job API"]
                PRINT_DRUM_API["Drum API"]
            end
            subgraph "Recoater Control APIs"
                RECOATER_DRUM_API["Drum API"]
                RECOATER_BLADE_API["Blade API"]
                RECOATER_LEVELER_API["Leveler API"]
            end
        end
        subgraph "Service Layer"
            JOB_MANAGEMENT["Job Management"]
            COMMUNICATION["Communication"]
            MONITORING["Monitoring"]
            OPCUA["OPCUA"]
        end
        subgraph "Infrastructure Layer"
            CLI_EDITOR["CLI Editor"]
            RECOATER_CLIENT["Recoater Client"]
            MOCK_CLIENT["Mock Recoater Client"]
        end
    end

    subgraph "Aerosint Recoater"
        SERVER["Server"]
    end

    subgraph "TwinCAT XAR"
        PLC["PLC"]
    end

    %% Frontend Layer Connections
    STATUS_VIEW --&gt; STATUS_INDICATOR
    RECOATER_VIEW --&gt; DRUM_CONTROL
    RECOATER_VIEW --&gt; LEVELER_CONTROL
    PRINT_VIEW --&gt; FILE_UPLOAD
    PRINT_VIEW --&gt; JOB_PROGRESS
    PRINT_VIEW --&gt; MULTI_LAYER_JOB
    STATUS_VIEW --&gt; ERROR_MODAL
    DEBUG_VIEW --&gt; ERROR_DISPLAY

    %% Component to State connections
    STATUS_INDICATOR --&gt; STATUS_STORE
    DRUM_CONTROL --&gt; STATUS_STORE
    LEVELER_CONTROL --&gt; STATUS_STORE
    FILE_UPLOAD --&gt; PRINT_JOB_STORE
    JOB_PROGRESS --&gt; PRINT_JOB_STORE
    MULTI_LAYER_JOB --&gt; PRINT_JOB_STORE



    %% State to Service connections
    STATUS_STORE --&gt; API_SERVICE
    PRINT_JOB_STORE --&gt; API_SERVICE

    %% Service to Infrastructure connections
    API_SERVICE --&gt; HTTP_CLIENT
    API_SERVICE --&gt; WEBSOCKET

    %% Frontend to Backend connections
    HTTP_CLIENT --&gt; SYSTEM_APIS
    HTTP_CLIENT --&gt; CLI_API
    HTTP_CLIENT --&gt; LAYER_API
    HTTP_CLIENT --&gt; MM_API
    HTTP_CLIENT --&gt; JOB_API
    HTTP_CLIENT --&gt; PRINT_DRUM_API
    HTTP_CLIENT --&gt; RECOATER_DRUM_API
    HTTP_CLIENT --&gt; RECOATER_BLADE_API
    HTTP_CLIENT --&gt; RECOATER_LEVELER_API

    WEBSOCKET &lt;--&gt; COMMUNICATION

    %% Backend API to Service connections
    SYSTEM_APIS --&gt; RECOATER_CLIENT
    SYSTEM_APIS --&gt; OPCUA
    SYSTEM_APIS --&gt; JOB_MANAGEMENT
    CLI_API --&gt; JOB_MANAGEMENT
    CLI_API --&gt; CLI_EDITOR
    LAYER_API --&gt; JOB_MANAGEMENT
    MM_API --&gt; JOB_MANAGEMENT
    JOB_API --&gt; JOB_MANAGEMENT
    PRINT_DRUM_API --&gt; RECOATER_CLIENT
    RECOATER_DRUM_API --&gt; RECOATER_CLIENT
    RECOATER_BLADE_API --&gt; RECOATER_CLIENT
    RECOATER_LEVELER_API --&gt; RECOATER_CLIENT

    %% Backend Service to Infrastructure connections
    JOB_MANAGEMENT --&gt; OPCUA
    JOB_MANAGEMENT --&gt; CLI_EDITOR
    JOB_MANAGEMENT --&gt; RECOATER_CLIENT
    COMMUNICATION --&gt; RECOATER_CLIENT
    COMMUNICATION --&gt; MOCK_CLIENT
    MONITORING --&gt; OPCUA
    MONITORING --&gt; RECOATER_CLIENT
    MONITORING &lt;--&gt; COMMUNICATION



    %% External connections
    RECOATER_CLIENT &lt;--&gt; SERVER
    OPCUA &lt;--&gt; PLC

    %% Styling for Frontend layers
    classDef frontendPresentation fill:#e1f5fe,stroke:#0277bd,stroke-width:2px,color:#000
    classDef frontendComponent fill:#e8f5e8,stroke:#388e3c,stroke-width:2px,color:#000
    classDef frontendState fill:#fff3e0,stroke:#f57c00,stroke-width:2px,color:#000
    classDef frontendService fill:#fce4ec,stroke:#c2185b,stroke-width:2px,color:#000
    classDef frontendInfra fill:#f3e5f5,stroke:#7b1fa2,stroke-width:2px,color:#000

    %% Styling for Backend layers
    classDef backendPresentation fill:#e3f2fd,stroke:#1565c0,stroke-width:2px,color:#000
    classDef backendService fill:#e0f2f1,stroke:#00695c,stroke-width:2px,color:#000
    classDef backendInfra fill:#fff8e1,stroke:#ef6c00,stroke-width:2px,color:#000

    %% Styling for External systems
    classDef external fill:#ffebee,stroke:#d32f2f,stroke-width:3px,color:#000

    %% Apply styles to Frontend Presentation Layer
    class STATUS_VIEW,RECOATER_VIEW,PRINT_VIEW,CONFIG_VIEW,DEBUG_VIEW frontendPresentation

    %% Apply styles to Frontend Component Layer
    class STATUS_INDICATOR,DRUM_CONTROL,LEVELER_CONTROL,FILE_UPLOAD,JOB_PROGRESS,MULTI_LAYER_JOB,ERROR_MODAL,ERROR_DISPLAY frontendComponent

    %% Apply styles to Frontend State Layer
    class STATUS_STORE,PRINT_JOB_STORE frontendState

    %% Apply styles to Frontend Service Layer
    class API_SERVICE frontendService

    %% Apply styles to Frontend Infrastructure Layer
    class ROUTER,WEBSOCKET,HTTP_CLIENT frontendInfra

    %% Apply styles to Backend Presentation Layer
    class SYSTEM_APIS,CLI_API,LAYER_API,MM_API,JOB_API,PRINT_DRUM_API,RECOATER_DRUM_API,RECOATER_BLADE_API,RECOATER_LEVELER_API backendPresentation

    %% Apply styles to Backend Service Layer
    class JOB_MANAGEMENT,COMMUNICATION,MONITORING,OPCUA backendService

    %% Apply styles to Backend Infrastructure Layer
    class CLI_EDITOR,RECOATER_CLIENT,MOCK_CLIENT backendInfra

    %% Apply styles to External Systems
    class SERVER,PLC external

</div><p>Note: The Vue Router is included in the Infrastructure Layer and is connected to the Views as it provides low-level routing capabilities for PrintView, RecoaterView, StatusView, ConfigurationView, and DebugView components. However, connections are not drawn in the diagram to avoid clutter.</p>
<h3 id="22-architectural-pattern-analysis">2.2 Architectural Pattern Analysis </h3>
<p>The Recoater HMI system implements a <strong>Layered (N-tier) Architecture</strong> with clear separation of concerns across both frontend and backend systems. This architecture provides scalability, maintainability, and testability through well-defined layer boundaries and dependencies.</p>
<p><strong>Frontend Application - 5-Layer Architecture:</strong></p>
<ol>
<li><strong>Presentation Layer (Views)</strong>: User interface components that handle user interactions and display data</li>
<li><strong>Component Layer</strong>: Reusable UI components that encapsulate specific functionality</li>
<li><strong>Application State Layer (Pinia Stores)</strong>: Centralized state management using reactive stores</li>
<li><strong>Service Layer</strong>: API communication and business logic coordination</li>
<li><strong>Infrastructure Layer</strong>: Low-level services like routing, HTTP client, and WebSocket connections</li>
</ol>
<p><strong>Backend Application - 3-Layer Architecture:</strong></p>
<ol>
<li><strong>Presentation Layer</strong>: REST API endpoints that handle HTTP requests and responses</li>
<li><strong>Service Layer</strong>: Business logic, job management, communication, and monitoring services</li>
<li><strong>Infrastructure Layer</strong>: Hardware clients, file processors, and external system integrations</li>
</ol>
<p><strong>Cross-System Communication:</strong></p>
<ul>
<li><strong>Frontend Service ↔ Backend Presentation</strong>: RESTful API calls for user-initiated actions</li>
<li><strong>Frontend Infrastructure ↔ Backend Service</strong>: WebSocket connections for real-time updates</li>
<li><strong>Backend Infrastructure ↔ External Systems</strong>: RESTful API calls to the Aerosint Recoater server and OPC UA communication with the PLC</li>
</ul>
<p>This layered approach follows Clean Architecture principles, ensuring that business logic remains independent of UI frameworks, databases, and external interfaces.</p>
<hr>
<h2 id="3-backend-development">3. Backend Development </h2>
<h3 id="31-3-layer-clean-architecture-overview">3.1 3-Layer Clean Architecture Overview </h3>
<h4 id="311-architecture-diagram">3.1.1 Architecture Diagram </h4>
<div class="mermaid">graph TB
    subgraph "Backend"
        subgraph "Presentation Layer"
            SYSTEM_APIS["System APIs"]
            subgraph "Print APIs"
                CLI_API["CLI API"]
                LAYER_API["Layer API"]
                MM_API["Multi-Material API"]
                JOB_API["Job API"]
                PRINT_DRUM_API["Drum API"]
            end
            subgraph "Recoater Control APIs"
                RECOATER_DRUM_API["Drum API"]
                RECOATER_BLADE_API["Blade API"]
                RECOATER_LEVELER_API["Leveler API"]
            end
        end
        subgraph "Service Layer"
            JOB_MANAGEMENT["Job Management"]
            COMMUNICATION["Communication"]
            MONITORING["Monitoring"]
            OPCUA["OPCUA"]
        end
        subgraph "Infrastructure Layer"
            CLI_EDITOR["CLI Editor"]
            RECOATER_CLIENT["Recoater Client"]
            MOCK_CLIENT["Mock Recoater Client"]
        end
    end

    subgraph "Aerosint Recoater"
        SERVER["Server"]
    end

    subgraph "TwinCAT XAR"
        PLC["PLC"]
    end

RECOATER_CLIENT &lt;--&gt; SERVER

JOB_MANAGEMENT &lt;--&gt; RECOATER_CLIENT
JOB_MANAGEMENT &lt;--&gt; OPCUA
COMMUNICATION &lt;--&gt; RECOATER_CLIENT
MONITORING &lt;--&gt; RECOATER_CLIENT
MONITORING &lt;--&gt; COMMUNICATION
OPCUA &lt;--&gt; PLC

SYSTEM_APIS &lt;--&gt; RECOATER_CLIENT
SYSTEM_APIS &lt;--&gt; OPCUA

CLI_API &lt;--&gt; JOB_MANAGEMENT
CLI_API &lt;--&gt; CLI_EDITOR
LAYER_API &lt;--&gt; JOB_MANAGEMENT
MM_API &lt;--&gt; JOB_MANAGEMENT
JOB_API &lt;--&gt; JOB_MANAGEMENT
PRINT_DRUM_API &lt;--&gt; RECOATER_CLIENT

RECOATER_DRUM_API &lt;--&gt; RECOATER_CLIENT
RECOATER_BLADE_API &lt;--&gt; RECOATER_CLIENT
RECOATER_LEVELER_API &lt;--&gt; RECOATER_CLIENT

</div><h4 id="312-architectural-pattern-analysis">3.1.2 Architectural Pattern Analysis </h4>
<p>The backend implements a <strong>3-Layer Clean Architecture</strong> that separates concerns and ensures business logic independence from external frameworks and systems. This pattern follows the dependency inversion principle where higher layers depend on abstractions, not concretions. Crucially, each layer of the architecture builds upon the services provided by the layer directly beneath it, creating a clear and manageable flow of dependencies.</p>
<p><strong>Layer 1: Presentation Layer (API Controllers)</strong></p>
<ul>
<li><strong>Purpose</strong>: Handle HTTP requests and responses, input validation, and error formatting</li>
<li><strong>Responsibilities</strong>:
<ul>
<li>Receive and validate incoming HTTP requests</li>
<li>Route requests to appropriate services</li>
<li>Transform service responses into HTTP responses</li>
</ul>
</li>
<li><strong>Dependencies</strong>: Depends on Service Layer interfaces</li>
</ul>
<p><strong>Layer 2: Service Layer (Business Logic)</strong></p>
<ul>
<li><strong>Purpose</strong>: Implement core business rules, workflows, and domain logic</li>
<li><strong>Responsibilities</strong>:
<ul>
<li>Orchestrate complex operations (multi-material job workflows)</li>
<li>Enforce business rules and validation</li>
<li>Coordinate between different domain services</li>
<li>Manage application state and transactions</li>
</ul>
</li>
<li><strong>Dependencies</strong>: Depends on Infrastructure Layer interfaces</li>
</ul>
<p><strong>Layer 3: Infrastructure Layer (External Integrations)</strong></p>
<ul>
<li><strong>Purpose</strong>: Handles communication with Aerosint Recoater hardware and external CLI file processing capabilities that are not part of the core business logic</li>
<li><strong>Responsibilities</strong>:
<ul>
<li>Communicate with Aerosint Recoater hardware</li>
<li>Interface with OPC UA PLC systems</li>
<li>Process CLI files and generate previews</li>
<li>Manage WebSocket connections</li>
</ul>
</li>
<li><strong>Dependencies</strong>: No dependencies on other layers (Sends low level requests directly to the Aerosint Recoater server and directly manipulates CLI files)</li>
</ul>
<p><strong>Key Benefits:</strong></p>
<ul>
<li><strong>Testability</strong>: Each layer can be tested independently with mock dependencies</li>
<li><strong>Flexibility</strong>: Each layer can be modified or replaced without affecting others</li>
<li><strong>Maintainability</strong>: Changes in external systems only affect the Infrastructure Layer</li>
<li><strong>Independence</strong>: Business logic remains pure and framework-agnostic</li>
</ul>
<h3 id="32-presentation-layer">3.2 Presentation Layer </h3>
<p>The Presentation Layer serves as the bridge between the Vue.js frontend and the backend business logic. When users interact with the web interface (clicking buttons, uploading files, viewing status), the frontend makes HTTP requests to these API endpoints. This layer receives those requests, validates the data, processes it through the appropriate services, and sends back responses that the frontend can display to users.</p>
<h4 id="321-api-structure-and-organization">3.2.1 API Structure and Organization </h4>
<p>The backend follows RESTful design principles with clear separation of concerns. Each domain area has its own router module, making the codebase maintainable and scalable.</p>
<h4 id="3211-api-architecture-overview">******* API Architecture Overview: </h4>
<div class="mermaid">graph TB
    subgraph "Backend API Endpoints"
        subgraph "System APIs"
            STATUS_GET["/api/status&lt;br/&gt;Get recoater system status"]
            STATUS_HEALTH["/api/status/health&lt;br/&gt;Perform system health check"]
            STATUS_STATE["/api/status/state&lt;br/&gt;Set server state (restart/shutdown)"]
            CONFIG_GET["/api/config&lt;br/&gt;Get recoater configuration"]
            CONFIG_SET["/api/config&lt;br/&gt;Update recoater configuration"]
            ERRORS_CLEAR["/api/errors/clear&lt;br/&gt;Clear all error flags"]
        end

        subgraph "Print APIs"
            subgraph "CLI APIs"
                CLI_UPLOAD["/api/print/cli/upload&lt;br/&gt;Upload CLI for preview"]
                CLI_UPLOAD_DRUM["/api/print/cli/upload/{drum_id}&lt;br/&gt;Upload CLI to specific drum"]
                CLI_PREVIEW["/api/print/cli/{file_id}/layer/{layer_num}/preview&lt;br/&gt;Get layer preview image"]
                CLI_SEND_LAYER["/api/print/cli/{file_id}/layer/{layer_num}/send/{drum_id}&lt;br/&gt;Send single layer to drum"]
                CLI_SEND_RANGE["/api/print/cli/{file_id}/layers/send/{drum_id}&lt;br/&gt;Send layer range to drum"]
            end

            subgraph "Layer APIs"
                LAYER_PARAMS_GET["/api/print/layer/parameters&lt;br/&gt;Get current layer parameters"]
                LAYER_PARAMS_SET["/api/print/layer/parameters&lt;br/&gt;Update layer parameters"]
                LAYER_PREVIEW["/api/print/layer/preview&lt;br/&gt;Get composite layer preview"]
            end

            subgraph "Multi-Material APIs"
                MM_START["/api/print/cli/start-multimaterial-job&lt;br/&gt;Start multi-material print job"]
                MM_STATUS["/api/print/multimaterial-job/status&lt;br/&gt;Get job status"]
                MM_CANCEL["/api/print/multimaterial-job/cancel&lt;br/&gt;Cancel active job"]
                MM_CACHE_STATUS["/api/print/cli/drum-cache-status&lt;br/&gt;Get drum cache status"]
                MM_CLEAR_CACHE["/api/print/cli/clear-drum-cache/{drum_id}&lt;br/&gt;Clear drum cache"]
                MM_DRUM_STATUS["/api/print/multimaterial-job/drum-status/{drum_id}&lt;br/&gt;Get drum status"]
            end

            subgraph "Drum APIs"
                DRUM_PREVIEW["/api/print/drums/{drum_id}/geometry/preview&lt;br/&gt;Get drum geometry preview"]
                DRUM_UPLOAD["/api/print/drums/{drum_id}/geometry&lt;br/&gt;Upload geometry to drum"]
                DRUM_DOWNLOAD["/api/print/drums/{drum_id}/geometry&lt;br/&gt;Download geometry from drum"]
                DRUM_DELETE["/api/print/drums/{drum_id}/geometry&lt;br/&gt;Delete geometry from drum"]
            end

            subgraph "Job APIs"
                JOB_START["/api/print/job&lt;br/&gt;Start print job"]
                JOB_CANCEL["/api/print/job&lt;br/&gt;Cancel print job"]
                JOB_STATUS["/api/print/job/status&lt;br/&gt;Get job status"]
            end
        end

        subgraph "Recoater Control APIs"
            subgraph "Drum APIs"
                DRUM_MOTION_GET["/api/recoater/drums/{drum_id}/motion&lt;br/&gt;Get drum motion status"]
                DRUM_MOTION_SET["/api/recoater/drums/{drum_id}/motion&lt;br/&gt;Set drum motion"]
                DRUM_MOTION_CANCEL["/api/recoater/drums/{drum_id}/motion&lt;br/&gt;Cancel drum motion"]
                DRUM_EJECTION_GET["/api/recoater/drums/{drum_id}/ejection&lt;br/&gt;Get ejection pressure"]
                DRUM_EJECTION_SET["/api/recoater/drums/{drum_id}/ejection&lt;br/&gt;Set ejection pressure"]
                DRUM_SUCTION_GET["/api/recoater/drums/{drum_id}/suction&lt;br/&gt;Get suction pressure"]
                DRUM_SUCTION_SET["/api/recoater/drums/{drum_id}/suction&lt;br/&gt;Set suction pressure"]
            end

            subgraph "Blade APIs"
                BLADE_SCREWS_INFO["/api/recoater/drums/{drum_id}/blade/screws&lt;br/&gt;Get blade screws info"]
                BLADE_SCREWS_MOTION_GET["/api/recoater/drums/{drum_id}/blade/screws/motion&lt;br/&gt;Get blade screws motion"]
                BLADE_SCREWS_MOTION_SET["/api/recoater/drums/{drum_id}/blade/screws/motion&lt;br/&gt;Set blade screws motion"]
                BLADE_SCREWS_MOTION_CANCEL["/api/recoater/drums/{drum_id}/blade/screws/motion&lt;br/&gt;Cancel blade screws motion"]
                BLADE_SCREW_INFO["/api/recoater/drums/{drum_id}/blade/screws/{screw_id}&lt;br/&gt;Get specific screw info"]
                BLADE_SCREW_MOTION_GET["/api/recoater/drums/{drum_id}/blade/screws/{screw_id}/motion&lt;br/&gt;Get specific screw motion"]
                BLADE_SCREW_MOTION_SET["/api/recoater/drums/{drum_id}/blade/screws/{screw_id}/motion&lt;br/&gt;Set specific screw motion"]
                BLADE_SCREW_MOTION_CANCEL["/api/recoater/drums/{drum_id}/blade/screws/{screw_id}/motion&lt;br/&gt;Cancel specific screw motion"]
            end

            subgraph "Leveler APIs"
                LEVELER_PRESSURE_GET["/api/recoater/leveler/pressure&lt;br/&gt;Get leveler pressure"]
                LEVELER_PRESSURE_SET["/api/recoater/leveler/pressure&lt;br/&gt;Set leveler pressure"]
                LEVELER_SENSOR["/api/recoater/leveler/sensor&lt;br/&gt;Get leveler sensor state"]
            end
        end
    end
</div><h4 id="3212-api-design-principles">******* API Design Principles </h4>
<p>This section explains the key principles that guide how we design and organize our API endpoints.</p>
<p><strong>1. Domain-Driven Structure (Organizing by Purpose)</strong></p>
<p>We group related functions together based on the loose categories of system operations, printing tasks, and hardware control:</p>
<ul>
<li><strong>System APIs</strong>: Handle general system operations (checking status, configuration)</li>
<li><strong>Print APIs</strong>: Handle everything related to 3D printing (uploading files, managing jobs)</li>
<li><strong>Hardware APIs</strong>: Handle direct control of physical hardware (moving drums, adjusting pressure)</li>
</ul>
<p><strong>2. RESTful Resource Hierarchy (Logical URL Patterns)</strong></p>
<p>URLs are structured like a family tree, with broader categories at the top and specific items at the bottom.</p>
<p><em>How it works:</em></p>
<ul>
<li>Start with the general category</li>
<li>Move to specific items within that category</li>
<li>End with the action you want to perform</li>
</ul>
<p><em>Example URL breakdown:</em></p>
<pre data-role="codeBlock" data-info="" class="language-text"><code>/api/recoater/drums/0/blade/screws/2/motion
 │   │        │     │ │     │      │ │
 │   │        │     │ │     │      │ └─ Action: motion control
 │   │        │     │ │     │      └─── Specific screw: #2
 │   │        │     │ │     └─────────── Component: screws
 │   │        │     │ └───────────────── Sub-component: blade
 │   │        │     └─────────────────── Specific drum: #0
 │   │        └───────────────────────── Category: drums
 │   └────────────────────────────────── System: recoater
 └────────────────────────────────────── Base: api
</code></pre><p><strong>3. Consistent HTTP Verb Mapping (Standard Actions)</strong></p>
<p>We use standard "verbs" (HTTP methods) to indicate what action we want to perform.</p>
<p><em>The four main verbs:</em></p>
<ul>
<li><strong>GET</strong>: "Please show me..." (reading/viewing data)</li>
<li><strong>POST</strong>: "Please create..." or "Please do..." (creating new things or triggering actions)</li>
<li><strong>PUT</strong>: "Please update..." (changing existing data)</li>
<li><strong>DELETE</strong>: "Please remove..." (deleting or canceling things)</li>
</ul>
<p><em>Examples:</em></p>
<pre data-role="codeBlock" data-info="" class="language-text"><code>GET    /api/recoater/drums/0/motion     ← "Show me the drum's current motion status"
POST   /api/recoater/drums/0/motion     ← "Start moving the drum"
DELETE /api/recoater/drums/0/motion     ← "Stop the drum's motion"
PUT    /api/recoater/drums/0/pressure   ← "Change the drum's pressure setting"
</code></pre><p><strong>4. Dependency Injection (Automatic Resource Management)</strong></p>
<p>Instead of each endpoint manually connecting to hardware or services, the system automatically provides these connections.</p>
<p><em>How it works:</em></p>
<ul>
<li>Each endpoint declares what it needs (e.g., "I need to talk to the recoater hardware")</li>
<li>The system automatically provides the right connection</li>
<li>The same endpoint can work with real hardware or mock hardware for testing</li>
</ul>
<p><em>Example:</em></p>
<pre data-role="codeBlock" data-info="python" class="language-python python"><code><span class="token keyword keyword-def">def</span> <span class="token function">control_drum</span><span class="token punctuation">(</span>drum_id<span class="token punctuation">:</span> <span class="token builtin">int</span><span class="token punctuation">,</span> client<span class="token punctuation">:</span> RecoaterClient <span class="token operator">=</span> Depends<span class="token punctuation">(</span>get_recoater_client<span class="token punctuation">)</span><span class="token punctuation">)</span><span class="token punctuation">:</span>
    <span class="token comment"># Since our system requires a move_drum() function, we "inject" the client which automatically provides the connection</span>
    <span class="token keyword keyword-return">return</span> client<span class="token punctuation">.</span>move_drum<span class="token punctuation">(</span>drum_id<span class="token punctuation">)</span>
</code></pre><p><em>Why it's helpful:</em></p>
<ul>
<li><strong>Consistency</strong>: All endpoints get connections in the same way</li>
<li><strong>Flexibility</strong>: Easy to switch between real and test hardware</li>
<li><strong>Reliability</strong>: Fewer chances for connection errors</li>
<li><strong>Testing</strong>: Easy to test with fake hardware</li>
</ul>
<h4 id="322-system-apis">3.2.2 System APIs </h4>
<h5 id="3221-configuration-management-api">******* Configuration Management API </h5>
<h6 id="get-config">GET /config </h6>
<p>Retrieves the current recoater system configuration including build space dimensions, ejection matrix size, drum gaps, and resolution settings. This endpoint provides the frontend Configuration View with all necessary parameters to display current system settings to operators.</p>
<p><strong>How does it work:</strong></p>
<ol>
<li>FastAPI dependency injection provides an initialized <code>RecoaterClient</code> instance</li>
<li>The endpoint calls <code>recoater_client.get_config()</code> to fetch raw configuration data from hardware</li>
<li>Raw data is validated and structured using the <code>ConfigurationResponse</code> Pydantic model</li>
<li>Returns JSON response with typed configuration fields or appropriate error status codes</li>
</ol>
<p><strong>Simple Sequence Diagram:</strong></p>
<div class="mermaid">sequenceDiagram
    participant Frontend as ConfigurationView
    participant ConfigRouter as Configuration Router (configuration.py)
    participant Client as RecoaterClient
    participant Hardware as Recoater Hardware

    Frontend-&gt;&gt;ConfigRouter: GET /config
    ConfigRouter-&gt;&gt;ConfigRouter: get_configuration()
    ConfigRouter-&gt;&gt;Client: get_config()
    Client-&gt;&gt;Hardware: HTTP GET /config
    Hardware--&gt;&gt;Client: Raw config JSON
    Client--&gt;&gt;ConfigRouter: Configuration data
    ConfigRouter-&gt;&gt;ConfigRouter: Validate with ConfigurationResponse
    ConfigRouter--&gt;&gt;Frontend: Structured config JSON
</div><p><strong>Design Patterns Used and Why:</strong></p>
<ul>
<li><strong>Dependency Injection Pattern</strong>: Uses <code>Depends(get_recoater_client)</code> to automatically provide hardware client, enabling easy testing with mock implementations</li>
<li><strong>Data Transfer Object Pattern</strong>: <code>ConfigurationResponse</code> model ensures type safety and automatic validation of hardware responses</li>
<li><strong>Repository Pattern</strong>: <code>RecoaterClient</code> abstracts hardware communication details from the API layer</li>
<li><strong>Error Handling Strategy Pattern</strong>: Converts hardware exceptions to appropriate HTTP status codes (503 for connection errors, 502 for API errors)</li>
</ul>
<h6 id="put-config">PUT /config </h6>
<p>Updates recoater system configuration with new parameter values. Supports partial updates where only specified fields are modified while others remain unchanged. This endpoint enables the frontend Configuration View to save operator changes to system settings.</p>
<p><strong>How does it work:</strong></p>
<ol>
<li>Accepts <code>ConfigurationRequest</code> payload with optional configuration fields</li>
<li>Pydantic validation ensures all provided values meet field constraints (e.g., non-negative numbers)</li>
<li>Converts the request model to a dictionary, excluding None values for partial updates</li>
<li>Calls <code>recoater_client.set_config()</code> to apply changes to hardware</li>
<li>Returns success confirmation or appropriate error response</li>
</ol>
<p><strong>Simple Sequence Diagram:</strong></p>
<div class="mermaid">sequenceDiagram
    participant Frontend as ConfigurationView
    participant ConfigRouter as Configuration Router (configuration.py)
    participant Client as RecoaterClient
    participant Hardware as Recoater Hardware

    Frontend-&gt;&gt;ConfigRouter: PUT /config (ConfigurationRequest)
    ConfigRouter-&gt;&gt;ConfigRouter: set_configuration(config)
    ConfigRouter-&gt;&gt;ConfigRouter: Validate request with Pydantic
    ConfigRouter-&gt;&gt;ConfigRouter: Convert to dict (exclude_none=True)
    ConfigRouter-&gt;&gt;Client: set_config(config_dict)
    Client-&gt;&gt;Hardware: HTTP PUT /config
    Hardware--&gt;&gt;Client: Success response
    Client--&gt;&gt;ConfigRouter: Confirmation
    ConfigRouter--&gt;&gt;Frontend: Success JSON response
</div><p><strong>Design Patterns Used and Why:</strong></p>
<ul>
<li><strong>Data Transfer Object Pattern</strong>: <code>ConfigurationRequest</code> provides type-safe input validation with field constraints (e.g., <code>ge=0</code> for non-negative values)</li>
<li><strong>Partial Update Pattern</strong>: Uses <code>model_dump(exclude_none=True)</code> to support partial configuration updates without overriding unchanged values</li>
<li><strong>Model-Dictionary Conversion Pattern</strong>: Converts Pydantic models to dictionaries for compatibility with the underlying hardware API</li>
<li><strong>Command Pattern</strong>: Encapsulates configuration update operations with validation, conversion, and hardware communication steps</li>
<li><strong>Dependency Injection Pattern</strong>: Manages hardware client lifecycle and enables testing with mock implementations</li>
</ul>
<h5 id="3222-system-status-api">******* System Status API </h5>
<h6 id="get-status">GET /status </h6>
<p>Retrieves comprehensive system status information including recoater hardware status, backend operational state, and connection health. This endpoint provides the frontend Status View with real-time system information while gracefully handling connection failures to ensure the interface remains responsive even when hardware is offline.</p>
<p><strong>How does it work:</strong></p>
<ol>
<li>FastAPI dependency injection provides an initialized <code>RecoaterClient</code> instance</li>
<li>The endpoint attempts to call <code>client.get_state()</code> to fetch hardware status</li>
<li>If hardware connection succeeds, combines backend and hardware status into unified response</li>
<li>If hardware connection fails, returns partial status with backend information and connection error details</li>
<li>Returns plain dictionary response with connection status, recoater status, and backend status</li>
<li>Never raises exceptions to frontend - always returns structured response with status indicators</li>
</ol>
<p><strong>Simple Sequence Diagram:</strong></p>
<div class="mermaid">sequenceDiagram
    participant Frontend as StatusView
    participant StatusRouter as Status Router (status.py)
    participant Client as RecoaterClient
    participant Hardware as Recoater Hardware

    Frontend-&gt;&gt;StatusRouter: GET /status
    StatusRouter-&gt;&gt;StatusRouter: get_status()
    StatusRouter-&gt;&gt;Client: get_state()

    alt Hardware Available
        Client-&gt;&gt;Hardware: HTTP GET /status
        Hardware--&gt;&gt;Client: Hardware status JSON
        Client--&gt;&gt;StatusRouter: Combined status data
        StatusRouter--&gt;&gt;Frontend: Complete status response
    else Hardware Unavailable
        Client-&gt;&gt;Hardware: HTTP GET /status
        Hardware--&gt;&gt;Client: Connection error
        Client--&gt;&gt;StatusRouter: Partial status with error
        StatusRouter--&gt;&gt;Frontend: Backend status + error details
    end
</div><p><strong>Design Patterns Used and Why:</strong></p>
<ul>
<li><strong>Graceful Degradation Pattern</strong>: Returns partial information when hardware is unavailable rather than failing completely, ensuring frontend remains functional</li>
<li><strong>Error Boundary Pattern</strong>: Catches and handles connection exceptions internally, converting them to structured error responses instead of propagating failures</li>
<li><strong>Composite Status Pattern</strong>: Combines multiple system components (backend + hardware) into a single unified status response</li>
<li><strong>Dependency Injection Pattern</strong>: Uses <code>Depends(get_recoater_client)</code> for testable hardware abstraction</li>
<li><strong>Plain Dictionary Response Pattern</strong>: Uses simple dictionary format for flexible frontend consumption without strict model constraints</li>
</ul>
<h6 id="get-statushealth">GET /status/health </h6>
<p><strong>IMPORTANT:</strong> This endpoint is currently not is use and may be deprecated in future releases.</p>
<p>Provides a lightweight health check endpoint optimized for monitoring systems and automated health checks. Returns simple boolean indicators for both backend and hardware availability without detailed status information, making it safe for frequent polling by monitoring tools.</p>
<p><strong>How does it work:</strong></p>
<ol>
<li>FastAPI dependency injection provides the <code>RecoaterClient</code> instance</li>
<li>Attempts a minimal health check call to <code>client.health_check()</code></li>
<li>Evaluates backend health based on service availability and basic functionality</li>
<li>Combines backend and hardware health into boolean response indicators</li>
<li>Returns plain dictionary response with backend and hardware health indicators</li>
<li>Optimized for speed and minimal resource consumption</li>
</ol>
<p><strong>Simple Sequence Diagram:</strong></p>
<div class="mermaid">sequenceDiagram
    participant Monitor as Monitoring System
    participant StatusRouter as Status Router (status.py)
    participant Client as RecoaterClient
    participant Hardware as Recoater Hardware

    Monitor-&gt;&gt;StatusRouter: GET /status/health
    StatusRouter-&gt;&gt;StatusRouter: health_check()
    StatusRouter-&gt;&gt;Client: health_check()
    Client-&gt;&gt;Hardware: Minimal health probe

    alt Healthy System
        Hardware--&gt;&gt;Client: OK response
        Client--&gt;&gt;StatusRouter: Health indicators
        StatusRouter--&gt;&gt;Monitor: {"backend": true, "hardware": true}
    else Unhealthy System
        Hardware--&gt;&gt;Client: Error/timeout
        Client--&gt;&gt;StatusRouter: Health indicators
        StatusRouter--&gt;&gt;Monitor: {"backend": true, "hardware": false}
    end
</div><p><strong>Design Patterns Used and Why:</strong></p>
<ul>
<li><strong>Health Check Pattern</strong>: Provides standardized health monitoring interface for infrastructure and monitoring tools</li>
<li><strong>Lightweight Response Pattern</strong>: Returns minimal data optimized for frequent polling without overwhelming the system</li>
<li><strong>Circuit Breaker Pattern</strong>: Quickly fails health checks when hardware is known to be unavailable, preventing resource waste</li>
<li><strong>Boolean Indicator Pattern</strong>: Uses simple true/false responses that monitoring systems can easily interpret and alert on</li>
<li><strong>Timeout Protection Pattern</strong>: Implements fast timeouts to ensure health checks don't block monitoring systems</li>
</ul>
<h6 id="post-statusstate">POST /status/state </h6>
<p>Enables controlled restart and shutdown operations on the recoater server system. This endpoint provides operators with the ability to perform maintenance operations and system recovery procedures through the web interface, with proper validation and safety checks.</p>
<p><strong>How does it work:</strong></p>
<ol>
<li>Accepts simple <code>action</code> string parameter (restart/shutdown)</li>
<li>Manual validation in endpoint logic ensures only valid actions are accepted</li>
<li>Calls <code>client.set_state()</code> with the specified action</li>
<li>Hardware performs the requested state change operation</li>
<li>Returns confirmation response or appropriate error status</li>
<li>Implements proper HTTP status codes for different error conditions</li>
</ol>
<p><strong>Simple Sequence Diagram:</strong></p>
<div class="mermaid">sequenceDiagram
    participant Frontend as DebugView
    participant StatusRouter as Status Router (status.py)
    participant Client as RecoaterClient
    participant Hardware as Recoater Hardware

    Frontend-&gt;&gt;StatusRouter: POST /status/state action="restart"
    StatusRouter-&gt;&gt;StatusRouter: set_server_state(action)
    StatusRouter-&gt;&gt;StatusRouter: Validate action parameter
    StatusRouter-&gt;&gt;Client: set_state("restart")
    Client-&gt;&gt;Hardware: HTTP POST /status/state

    alt Valid Action
        Hardware--&gt;&gt;Client: State change initiated
        Client--&gt;&gt;StatusRouter: Success confirmation
        StatusRouter--&gt;&gt;Frontend: {"success": true, "message": "Restart initiated"}
    else Invalid Action
        Hardware--&gt;&gt;Client: Error response
        Client--&gt;&gt;StatusRouter: Error details
        StatusRouter--&gt;&gt;Frontend: HTTP 400 with error message
    end
</div><p><strong>Design Patterns Used and Why:</strong></p>
<ul>
<li><strong>Command Pattern</strong>: Encapsulates server state change operations as discrete commands with validation and execution logic</li>
<li><strong>State Machine Pattern</strong>: Manages valid state transitions (running → restarting → running) with proper validation</li>
<li><strong>Parameter Validation Pattern</strong>: Uses manual parameter validation logic instead of Pydantic models for simple string validation</li>
<li><strong>Error Response Pattern</strong>: Returns descriptive error messages and appropriate HTTP status codes for different failure scenarios</li>
<li><strong>Safety Check Pattern</strong>: Validates actions before execution to prevent invalid or dangerous state changes</li>
</ul>
<h5 id="3223-error-management-api">******* Error Management API </h5>
<h6 id="post-errorsclear">POST /errors/clear </h6>
<p>Provides unified error clearing functionality for both backend service errors and OPC UA variable states. This endpoint enables operators to recover from error conditions through the web interface by clearing error flags across all system components, including coordinated error recovery with the multi-material job service.</p>
<p><strong>How does it work:</strong></p>
<ol>
<li>FastAPI dependency injection provides <code>MultiMaterialJobService</code> instance</li>
<li>Calls <code>job_manager.clear_all_error_flags()</code> to clear all system error states</li>
<li>This method clears both backend job service errors and OPC UA error variables hosted by our backend</li>
<li>Updates OPC UA variables <code>backend_error</code> and <code>plc_error</code> to <code>False</code> in our hosted OPC UA server</li>
<li>Returns <code>MultiMaterialJobResponse</code> with success/failure status and descriptive message</li>
<li>Provides unified error recovery through single service method</li>
</ol>
<p><strong>Simple Sequence Diagram:</strong></p>
<div class="mermaid">sequenceDiagram
    participant Frontend as ErrorModal/PrintView
    participant ErrorsRouter as Errors Router (errors.py)
    participant JobService as MultiMaterialJobService
    participant OPCUAService as Backend OPC UA Server

    Note over OPCUAService: Before: backend_error=True, plc_error=True

    Frontend-&gt;&gt;ErrorsRouter: POST /errors/clear
    ErrorsRouter-&gt;&gt;ErrorsRouter: clear_errors()
    ErrorsRouter-&gt;&gt;JobService: clear_all_error_flags()
    JobService-&gt;&gt;JobService: Clear backend job errors
    JobService-&gt;&gt;OPCUAService: clear_error_flags()
    OPCUAService-&gt;&gt;OPCUAService: Set backend_error = False
    OPCUAService-&gt;&gt;OPCUAService: Set plc_error = False
    OPCUAService--&gt;&gt;JobService: Error flags cleared
    JobService--&gt;&gt;ErrorsRouter: MultiMaterialJobResponse
    ErrorsRouter--&gt;&gt;Frontend: {"success": true, "message": "All error flags cleared"}

    Note over OPCUAService: After: backend_error=False, plc_error=False
</div><p><strong>Design Patterns Used and Why:</strong></p>
<ul>
<li><strong>Unified Service Pattern</strong>: Uses single service method to handle all error clearing operations, simplifying the API interface</li>
<li><strong>Delegation Pattern</strong>: Delegates complex error clearing logic to specialized service layer rather than handling in API endpoint</li>
<li><strong>Error Recovery Pattern</strong>: Implements systematic approach to error state recovery across multiple system components</li>
<li><strong>Dependency Injection Pattern</strong>: Uses injected <code>MultiMaterialJobService</code> for testable error management operations</li>
<li><strong>Response Model Pattern</strong>: Returns structured <code>MultiMaterialJobResponse</code> for consistent API response format</li>
<li><strong>Internal Variable Management Pattern</strong>: Backend manages OPC UA variables internally without external PLC communication dependency</li>
</ul>
<h4 id="323-print-job-management-apis">3.2.3 Print Job Management APIs </h4>
<p>The Print Job Management APIs provide comprehensive functionality for managing 3D printing operations in the Aerosint Recoater system. These APIs handle everything from CLI file upload and processing to coordinated multi-material printing workflows. The system supports both legacy single-drum operations and modern multi-material workflows with sophisticated layer-by-layer coordination.</p>
<h5 id="3231-cli-file-processing-api">******* CLI File Processing API </h5>
<p>The CLI File Processing API provides comprehensive functionality for uploading, parsing, caching, and managing CLI files in both preview and production workflows. This API supports both generic file uploads for preview purposes and drum-specific uploads for the multi-material printing workflow.</p>
<h6 id="post-cliupload">POST /cli/upload </h6>
<p>Uploads and parses a CLI file for <strong>preview-only</strong> operations without associating it with any specific drum. This endpoint is designed for development, testing, and file validation scenarios where users want to examine CLI content before committing to a production workflow.</p>
<p><strong>How does it work:</strong></p>
<ol>
<li>Accepts <code>.cli</code> file uploads through FastAPI's <code>UploadFile</code> interface with validation</li>
<li>Reads file content asynchronously and validates it's not empty</li>
<li>Uses <code>asyncio.to_thread()</code> to offload CPU-intensive CLI parsing to avoid blocking the event loop</li>
<li>Generates unique UUID-based <code>file_id</code> for caching and subsequent reference operations</li>
<li>Stores parsed CLI file in generic cache through <code>MultiMaterialJobService.add_cli_file()</code></li>
<li>Returns comprehensive metadata including layer count, file size, and generated file ID</li>
</ol>
<p><strong>Simple Sequence Diagram:</strong></p>
<div class="mermaid">sequenceDiagram
    participant Frontend as FileUploadColumn
    participant APIRouter as CLI Router (cli.py)
    participant JobService as MultiMaterialJobService
    participant Parser as CLI Editor

    Note over JobService: cli_cache = {}
    Frontend-&gt;&gt;APIRouter: POST /cli/upload
    APIRouter-&gt;&gt;APIRouter: upload_cli_file_for_preview(file)
    APIRouter-&gt;&gt;APIRouter: Validate file type and size
    APIRouter-&gt;&gt;Parser: asyncio.to_thread(parse)
    Parser--&gt;&gt;APIRouter: ParsedCliFile object
    APIRouter-&gt;&gt;JobService: add_cli_file(file_id, parsed, filename)
    JobService-&gt;&gt;JobService: Store in self.cli_cache[file_id]
    Note over JobService: cli_cache = {file_id: CliCacheEntry}
    JobService--&gt;&gt;APIRouter: Success
    APIRouter--&gt;&gt;Frontend: CliUploadResponse with metadata
</div><p><strong>Design Patterns Used and Why:</strong></p>
<ul>
<li><strong>Asynchronous Processing Pattern</strong>: Uses <code>asyncio.to_thread()</code> to prevent CLI parsing from blocking the event loop, ensuring responsive API performance</li>
<li><strong>UUID Generation Pattern</strong>: Generates unique file identifiers to prevent conflicts and enable reliable file reference</li>
<li><strong>Generic Caching Pattern</strong>: Stores files in generic cache separate from production drum cache, enabling flexible preview operations</li>
<li><strong>Input Validation Pattern</strong>: Validates file extensions and content before processing to prevent invalid data from entering the system</li>
<li><strong>Response Model Pattern</strong>: Returns structured <code>CliUploadResponse</code> with comprehensive metadata for frontend consumption</li>
</ul>
<h6 id="post-cliuploaddrum_id">POST /cli/upload/drum_id </h6>
<p>Uploads and caches a CLI file for a specific drum (0, 1, or 2) in the production multi-material workflow. This is the primary endpoint for production printing operations where each drum requires its own material-specific CLI file.</p>
<p><strong>How does it work:</strong></p>
<ol>
<li>Validates <code>drum_id</code> parameter using FastAPI Path constraints (0-2 range)</li>
<li>Accepts <code>.cli</code> file upload with file type and content validation</li>
<li>Performs CPU-intensive CLI parsing in a separate thread using <code>asyncio.to_thread()</code></li>
<li>Caches parsed file specifically for the target drum using <code>cache_cli_file_for_drum()</code></li>
<li>Associates the file with drum hardware for subsequent printing operations</li>
<li>Returns production-ready response with simplified <code>file_id</code> format (<code>drum_{drum_id}</code>)</li>
</ol>
<p><strong>Simple Sequence Diagram:</strong></p>
<div class="mermaid">sequenceDiagram
    participant Frontend as MultiLayerJobControl
    participant APIRouter as CLI Router (cli.py)
    participant JobService as MultiMaterialJobService
    participant Parser as CLI Editor

    Note over JobService: drum_cli_cache = {0: None, 1: None, 2: None}
    Frontend-&gt;&gt;APIRouter: POST /cli/upload/0
    APIRouter-&gt;&gt;APIRouter: upload_cli_file_to_drum(drum_id=0, file)
    APIRouter-&gt;&gt;APIRouter: Validate drum_id (0-2) and file
    APIRouter-&gt;&gt;Parser: asyncio.to_thread(parse)
    Parser--&gt;&gt;APIRouter: ParsedCliFile object
    APIRouter-&gt;&gt;JobService: cache_cli_file_for_drum(0, parsed, filename)
    JobService-&gt;&gt;JobService: Store in self.drum_cli_cache[0]
    Note over JobService: drum_cli_cache[0] = {parsed_file, layer_count, ...}
    JobService--&gt;&gt;APIRouter: Success
    APIRouter--&gt;&gt;Frontend: CliUploadResponse with drum reference
</div><p><strong>Design Patterns Used and Why:</strong></p>
<ul>
<li><strong>Drum-Specific Caching Pattern</strong>: Uses separate cache structure for production workflow, ensuring proper drum-to-file associations</li>
<li><strong>Path Parameter Validation</strong>: Employs FastAPI Path constraints to validate drum ID ranges at the API layer</li>
<li><strong>Production Workflow Pattern</strong>: Separates production caching from preview caching to prevent accidental mixing of workflows</li>
<li><strong>Resource Association Pattern</strong>: Creates direct association between CLI files and hardware drums for printing operations</li>
</ul>
<h6 id="get-clifile_idlayerlayer_numpreview">GET /cli/{file_id}/layer/{layer_num}/preview </h6>
<p>Generates and returns a PNG preview image of a specific layer from a previously uploaded CLI file. This endpoint enables operators to visually verify layer content before printing and supports both 1-based user-friendly layer numbering and internal 0-based indexing conversion. To get preview image of the composite layer (all drums combined), use the <code>/layer/preview</code> endpoint instead.</p>
<p><strong>How does it work:</strong></p>
<ol>
<li>Retrieves cached CLI file using the provided <code>file_id</code> from generic cache</li>
<li>Validates the requested <code>layer_num</code> against available layers (1-based user input)</li>
<li>Converts 1-based layer numbering to 0-based internal indexing for array access</li>
<li>Extracts the target layer object from the parsed CLI file data structure</li>
<li>Uses CLI editor's <code>render_layer_to_png()</code> method to generate PNG image data</li>
<li>Returns binary PNG response with appropriate headers for browser display</li>
</ol>
<p><strong>Simple Sequence Diagram:</strong></p>
<div class="mermaid">sequenceDiagram
    participant Frontend as Layer Preview Component
    participant APIRouter as CLI Router (cli.py)
    participant JobService as MultiMaterialJobService
    participant Parser as CLI Editor

    Note over JobService: cli_cache = {file_id: CliCacheEntry}
    Frontend-&gt;&gt;APIRouter: GET /cli/{file_id}/layer/{layer_num}/preview
    APIRouter-&gt;&gt;APIRouter: get_cli_layer_preview(file_id, layer_num)
    APIRouter-&gt;&gt;JobService: get_cli_file_with_metadata(file_id)
    JobService-&gt;&gt;JobService: Retrieve from self.cli_cache[file_id]
    Note over JobService: Returns ParsedCliFile with metadata
    JobService--&gt;&gt;APIRouter: Cached file data
    APIRouter-&gt;&gt;APIRouter: Convert layer 5 to index 4
    APIRouter-&gt;&gt;Parser: render_layer_to_png(layer[4])
    Parser--&gt;&gt;APIRouter: PNG binary data
    APIRouter--&gt;&gt;Frontend: PNG Response with headers
</div><p><strong>Design Patterns Used and Why:</strong></p>
<ul>
<li><strong>Layer Indexing Conversion Pattern</strong>: Handles conversion between user-friendly 1-based numbering and internal 0-based indexing</li>
<li><strong>Binary Response Pattern</strong>: Returns binary PNG data with appropriate media type and Content-Disposition headers</li>
<li><strong>Cache Retrieval Pattern</strong>: Efficiently retrieves cached files without re-parsing for performance</li>
<li><strong>Visual Validation Pattern</strong>: Provides visual feedback to operators for quality assurance</li>
</ul>
<h6 id="post-clifile_idlayerlayer_numsenddrum_id">POST /cli/{file_id}/layer/{layer_num}/send/drum_id </h6>
<p>Caches a single layer from a preview-cached CLI file to a specific drum’s cache. This is intended for selective distribution/testing before starting a multi‑material job. It does not upload to hardware; the multi‑material job orchestrator will handle per‑layer uploads during execution.</p>
<p><strong>How does it work:</strong></p>
<ol>
<li>Retrieves the cached CLI file from generic cache using <code>file_id</code></li>
<li>Validates the layer number and converts to 0‑based indexing</li>
<li>Extracts the target layer from parsed CLI</li>
<li>Generates a single‑layer ASCII CLI with proper headers</li>
<li>Parses and stores it in the drum‑specific cache (layerCount = 1)</li>
<li>Returns a response with layer metadata; no hardware upload occurs</li>
</ol>
<p><strong>Simple Sequence Diagram:</strong></p>
<div class="mermaid">sequenceDiagram
    participant Frontend as PrintView.vue
    participant CLIRouter as CLI Router (cli.py)
    participant JobService as MultiMaterialJobService
    participant Parser as CLI Editor

    Frontend-&gt;&gt;CLIRouter: POST /cli/{file_id}/layer/{layer_num}/send/{drum_id}
    CLIRouter-&gt;&gt;CLIRouter: send_cli_layer_to_drum(file_id, layer_num, drum_id)
    CLIRouter-&gt;&gt;JobService: get_cli_file_with_metadata(file_id)
    JobService--&gt;&gt;CLIRouter: Cached ParsedCliFile
    CLIRouter-&gt;&gt;CLIRouter: Extract layer[2] (0-based)
    CLIRouter-&gt;&gt;Parser: generate_single_layer_ascii_cli(layer, headers)
    Parser--&gt;&gt;CLIRouter: Single‑layer CLI bytes
    CLIRouter-&gt;&gt;JobService: cache_cli_file_for_drum(1, parsed_single)
    CLIRouter--&gt;&gt;Frontend: Success with layer metadata (cache‑only)
</div><p><strong>Design Patterns Used and Why:</strong></p>
<ul>
<li><strong>Single Layer Extraction Pattern</strong>: Generates isolated layer CLI data while preserving header information</li>
<li><strong>Dual Cache Strategy</strong>: Maintains both preview cache and drum cache for workflow consistency</li>
<li><strong>Separation of Concerns</strong>: Hardware uploads are delegated to the multi‑material job orchestrator</li>
</ul>
<h6 id="post-clifile_idlayerssend" drum_id="true">POST /cli/{file_id}/layers/send/ </h6>
<p>Caches a range of consecutive layers from a preview‑cached CLI file to a specific drum’s cache. This prepares drum‑specific partial CLIs; it does not upload to hardware. The multi‑material job will upload layers to hardware during execution.</p>
<p><strong>How does it work:</strong></p>
<ol>
<li>Accepts <code>LayerRangeRequest</code> with start and end layer numbers (1‑based)</li>
<li>Validates the range and converts to 0‑based indices</li>
<li>Extracts the layer slice from the parsed CLI</li>
<li>Generates an ASCII CLI containing only the selected range</li>
<li>Parses and stores it in the drum‑specific cache</li>
<li>Returns detailed response with range information and Z‑height data; no hardware upload occurs</li>
</ol>
<p><strong>Simple Sequence Diagram:</strong></p>
<div class="mermaid">sequenceDiagram
    participant Frontend as PrintView.vue
    participant CLIRouter as CLI Router (cli.py)
    participant JobService as MultiMaterialJobService
    participant Parser as CLI Editor

    Frontend-&gt;&gt;CLIRouter: POST /cli/{file_id}/layers/send/{drum_id}
    CLIRouter-&gt;&gt;CLIRouter: send_cli_layer_range_to_drum(file_id, drum_id, body)
    CLIRouter-&gt;&gt;CLIRouter: Validate range (5 &lt;= 10, &lt;= total_layers)
    CLIRouter-&gt;&gt;JobService: get_cli_file_with_metadata(file_id)
    JobService--&gt;&gt;CLIRouter: Cached ParsedCliFile
    CLIRouter-&gt;&gt;CLIRouter: Extract layers[4:10] (0-based slice)
    CLIRouter-&gt;&gt;Parser: generate_ascii_cli_from_layer_range(layers, headers)
    Parser--&gt;&gt;CLIRouter: Multi‑layer CLI bytes
    CLIRouter-&gt;&gt;JobService: cache_cli_file_for_drum(2, parsed_trimmed)
    CLIRouter--&gt;&gt;Frontend: Success with range and Z‑height info (cache‑only)
</div><p><strong>Design Patterns Used and Why:</strong></p>
<ul>
<li><strong>Range Validation Pattern</strong>: Ensures logical consistency of layer ranges before processing</li>
<li><strong>Slice Extraction Pattern</strong>: Uses Python slice notation for efficient consecutive layer extraction</li>
<li><strong>Multi‑Layer CLI Generation</strong>: Maintains CLI format integrity while processing layer ranges</li>
<li><strong>Z‑Height Tracking Pattern</strong>: Preserves Z‑height information for layer positioning verification</li>
<li><strong>Dual Cache Strategy</strong>: Maintains both preview cache and drum cache for workflow consistency</li>
<li><strong>Separation of Concerns</strong>: Hardware uploads are performed by the multi‑material job workflow</li>
</ul>
<h5 id="3232-multi-material-job-orchestration-api">******* Multi-Material Job Orchestration API </h5>
<p>The Multi-Material Job Orchestration API provides sophisticated coordination for complex multi-drum printing operations. This API implements a comprehensive workflow that manages layer-by-layer printing coordination, OPC UA communication, and real-time status monitoring.</p>
<h6 id="post-clistart-multimaterial-job">POST /cli/start-multimaterial-job </h6>
<p>Initiates a coordinated multi-material print job using cached CLI files from multiple drums. This endpoint implements sophisticated layer-by-layer orchestration with OPC UA coordination and real-time monitoring.</p>
<p><strong>How does it work:</strong></p>
<ol>
<li>Validates that at least one drum has cached CLI files using <code>has_cached_files()</code></li>
<li>Creates an asynchronous background task using <code>asyncio.create_task()</code> for long-running job execution</li>
<li>Stores task reference in <code>_background_task</code> for proper cancellation management</li>
<li>Returns immediately while the job runs in the background</li>
<li>Background task executes <code>start_layer_by_layer_job()</code> which implements the full 7-variable OPC UA workflow</li>
<li>Job processes each layer sequentially: upload → print → wait for completion → advance</li>
</ol>
<p><strong>Simple Sequence Diagram:</strong></p>
<div class="mermaid">sequenceDiagram
    participant Frontend as PrintView.vue
    participant MultiMaterialRouter as Multimaterial Router (multimaterial.py)
    participant JobService as MultiMaterialJobService
    participant Background as Background Task
    participant OPCUACoordinator as OPC UA System
    participant Hardware as Recoater Hardware

    Frontend-&gt;&gt;MultiMaterialRouter: POST /cli/start-multimaterial-job
    MultiMaterialRouter-&gt;&gt;MultiMaterialRouter: start_multimaterial_job()
    MultiMaterialRouter-&gt;&gt;JobService: has_cached_files()
    JobService--&gt;&gt;MultiMaterialRouter: Validation result

    alt Cache validation successful
        MultiMaterialRouter-&gt;&gt;JobService: asyncio.create_task(start_layer_by_layer_job)
        JobService-&gt;&gt;Background: Background task created
        MultiMaterialRouter--&gt;&gt;Frontend: Immediate response (job started)

        Background-&gt;&gt;OPCUACoordinator: Setup job (job_active=True, total_layers=N)
        Background-&gt;&gt;Background: current_layer = 1

        loop For each layer (1 to N)
            Background-&gt;&gt;Background: Check cancellation flag

            alt Job not cancelled
                Background-&gt;&gt;OPCUACoordinator: Reset layer flags (ready=False, complete=False)

                par Upload to all drums with delay
                    Background-&gt;&gt;Hardware: Upload layer to Drum 0
                    Background-&gt;&gt;Background: Wait 2s delay
                and
                    Background-&gt;&gt;Hardware: Upload layer to Drum 1
                    Background-&gt;&gt;Background: Wait 2s delay
                and
                    Background-&gt;&gt;Hardware: Upload layer to Drum 2
                end

                Background-&gt;&gt;OPCUACoordinator: Signal ready to print (ready=True)
                Background-&gt;&gt;Hardware: Start print job
                Background-&gt;&gt;Background: Poll for completion

                loop Wait for layer completion
                    Background-&gt;&gt;Background: Check cancellation + error flags

                    alt Layer completed successfully
                        Background-&gt;&gt;OPCUACoordinator: Signal layer complete (complete=True)
                        Background-&gt;&gt;OPCUACoordinator: Update progress (current_layer++)
                        note over Background: Break from completion loop
                    else Error detected (backend_error OR plc_error)
                        Background-&gt;&gt;Background: Pause layer processing
                        Background-&gt;&gt;Background: Wait for error flags to clear
                        note over Background: Retry layer from upload step
                    else Job cancelled
                        Background-&gt;&gt;Background: Exit layer loop gracefully
                        note over Background: Break from completion loop
                    end

                    Background-&gt;&gt;Background: Sleep polling interval
                end
            else Job cancelled
                Background-&gt;&gt;Background: Exit layer loop
                note over Background: Break from layer loop
            end
        end

        alt Job completed successfully
            Background-&gt;&gt;Background: Mark job completed
            Background-&gt;&gt;JobService: Clear drum cache
            Background-&gt;&gt;Background: Clear current_job reference
        else Job cancelled or error
            Background-&gt;&gt;Background: Handle cancellation/error cleanup
        end

        Background-&gt;&gt;OPCUACoordinator: Cleanup (job_active=False, reset flags)
    else Cache validation failed
        MultiMaterialRouter--&gt;&gt;Frontend: HTTP 400 - No CLI files cached
    end
</div><p><strong>Design Patterns Used and Why:</strong></p>
<ul>
<li><strong>Asynchronous Task Pattern</strong>: Uses <code>asyncio.create_task()</code> to run long-duration jobs without blocking API responses</li>
<li><strong>Background Processing Pattern</strong>: Enables immediate API response while complex workflows run in background</li>
<li><strong>Task Reference Management</strong>: Stores task references for proper cancellation and cleanup</li>
<li><strong>OPC UA Coordination Pattern</strong>: Implements 7-variable coordination protocol for PLC communication</li>
<li><strong>Sequential Layer Processing</strong>: Processes layers one at a time to ensure proper hardware coordination</li>
</ul>
<h6 id="get-multimaterial-jobstatus">GET /multimaterial-job/status </h6>
<p>Retrieves comprehensive status information for the currently active multi-material print job. This endpoint provides real-time monitoring data including job progress, current layer, layer counts, and execution state for operator dashboard display.</p>
<p><strong>How does it work:</strong></p>
<ol>
<li>FastAPI dependency injection provides the <code>MultiMaterialJobService</code> instance</li>
<li>Calls <code>job_manager.get_job_status()</code> to retrieve current job state</li>
<li>Returns structured status data including job state, progress percentage, current/total layers</li>
<li>Handles cases where no job is active by returning appropriate null/default values</li>
<li>Provides real-time data for frontend progress indicators and status displays</li>
<li>Includes job timing information and execution statistics</li>
</ol>
<p><strong>Simple Sequence Diagram:</strong></p>
<div class="mermaid">sequenceDiagram
    participant Frontend as PrintView.vue
    participant StatusRouter as Multimaterial Router (multimaterial.py)
    participant JobService as MultiMaterialJobService
    participant JobState as Current Job State

    Frontend-&gt;&gt;StatusRouter: GET /multimaterial-job/status
    StatusRouter-&gt;&gt;StatusRouter: get_multimaterial_job_status()
    StatusRouter-&gt;&gt;JobService: get_job_status()
    JobService-&gt;&gt;JobState: Read current job state
    JobState--&gt;&gt;JobService: Job progress and status data
    JobService--&gt;&gt;StatusRouter: MultiMaterialJobStatusResponse
    StatusRouter--&gt;&gt;Frontend: {status: "running", current_layer: 15, total_layers: 50, progress: 30%}
</div><p><strong>Design Patterns Used and Why:</strong></p>
<ul>
<li><strong>Status Polling Pattern</strong>: Enables real-time frontend updates through periodic status polling without WebSocket complexity</li>
<li><strong>State Snapshot Pattern</strong>: Returns point-in-time snapshot of job state for consistent frontend display</li>
<li><strong>Progress Tracking Pattern</strong>: Provides both absolute (layer numbers) and relative (percentage) progress indicators</li>
<li><strong>Null Object Pattern</strong>: Returns safe default values when no job is active to prevent frontend errors</li>
<li><strong>Response Model Pattern</strong>: Uses structured <code>MultiMaterialJobStatusResponse</code> for type-safe status data</li>
</ul>
<h6 id="post-multimaterial-jobcancel">POST /multimaterial-job/cancel </h6>
<p>Cancels the currently executing multi-material print job and performs proper cleanup operations. This endpoint provides operators with emergency stop capability and graceful job termination functionality through the web interface.</p>
<p><strong>How does it work:</strong></p>
<ol>
<li>FastAPI dependency injection provides the <code>MultiMaterialJobService</code> instance</li>
<li>Calls <code>job_manager.cancel_job()</code> to initiate job cancellation process</li>
<li>Service sets job cancellation flags and waits for current layer to complete safely</li>
<li>Performs cleanup operations including OPC UA state reset and cache management</li>
<li>Returns cancellation confirmation with success/failure status and descriptive message</li>
<li>Ensures hardware is left in safe state after cancellation</li>
</ol>
<p><strong>Simple Sequence Diagram:</strong></p>
<div class="mermaid">sequenceDiagram
    participant Frontend as PrintView.vue
    participant CancelRouter as Multimaterial Router (multimaterial.py)
    participant JobService as MultiMaterialJobService
    participant Background as Background Task
    participant OPCUAService as OPC UA System

    Frontend-&gt;&gt;CancelRouter: POST /multimaterial-job/cancel
    CancelRouter-&gt;&gt;CancelRouter: cancel_multimaterial_job()
    CancelRouter-&gt;&gt;JobService: cancel_job()
    JobService-&gt;&gt;JobService: Set cancellation flags
    JobService-&gt;&gt;Background: Wait for safe cancellation point
    Background-&gt;&gt;Background: Complete current layer safely
    Background-&gt;&gt;OPCUAService: Cleanup OPC UA state
    Background-&gt;&gt;Background: Clear job state
    JobService--&gt;&gt;CancelRouter: Cancellation result
    CancelRouter--&gt;&gt;Frontend: {success: true, message: "Job cancelled successfully"}
</div><p><strong>Design Patterns Used and Why:</strong></p>
<ul>
<li><strong>Graceful Shutdown Pattern</strong>: Allows current layer to complete before cancellation to prevent hardware damage</li>
<li><strong>Safe State Pattern</strong>: Ensures hardware is left in predictable, safe state after cancellation</li>
<li><strong>Background Task Management</strong>: Properly handles cancellation of long-running background tasks</li>
<li><strong>Operator Safety Pattern</strong>: Provides immediate cancellation capability for emergency situations</li>
<li><strong>Cleanup Coordination Pattern</strong>: Coordinates cleanup across multiple system components (OPC UA, hardware, cache)</li>
</ul>
<h5 id="3233-cache-management-api">******* Cache Management API </h5>
<p>The Cache Management API provides comprehensive functionality for managing CLI file caches used in multi-material printing workflows. These endpoints enable operators to monitor cache status, clear cache data, and ensure proper workflow state management.</p>
<h6 id="get-clidrum-cache-status">GET /cli/drum-cache-status </h6>
<p>Retrieves comprehensive status information about cached CLI files for all three drums in the multi-material workflow. This endpoint provides essential data for workflow validation and operator verification before starting print jobs.</p>
<p><strong>How does it work:</strong></p>
<ol>
<li>FastAPI dependency injection provides the <code>MultiMaterialJobService</code> instance</li>
<li>Iterates through all drum IDs (0, 1, 2) to check cache status</li>
<li>For each drum, calls <code>job_manager.get_cached_file_for_drum()</code> to retrieve cache data</li>
<li>Compiles comprehensive status including filenames, layer counts, and cache presence</li>
<li>Calculates maximum layers across all drums for workflow planning</li>
<li>Returns readiness indicators based on cache availability for job start validation</li>
</ol>
<p><strong>Simple Sequence Diagram:</strong></p>
<div class="mermaid">sequenceDiagram
    participant Frontend as MultiLayerJobControl
    participant CacheRouter as Multimaterial Router (multimaterial.py)
    participant JobService as MultiMaterialJobService
    participant DrumCache as Drum Cache Storage

    Frontend-&gt;&gt;CacheRouter: GET /cli/drum-cache-status
    CacheRouter-&gt;&gt;CacheRouter: get_drum_cache_status()

    loop For each drum (0, 1, 2)
        CacheRouter-&gt;&gt;JobService: get_cached_file_for_drum(drum_id)
        JobService-&gt;&gt;DrumCache: Check drum_cli_cache[drum_id]
        DrumCache--&gt;&gt;JobService: Cache data or None
        JobService--&gt;&gt;CacheRouter: Filename, layer_count, cached status
    end

    CacheRouter-&gt;&gt;JobService: has_cached_files()
    JobService--&gt;&gt;CacheRouter: Overall cache availability
    CacheRouter--&gt;&gt;Frontend: {drums: {0: {...}, 1: {...}, 2: {...}}, max_layers: 50, ready_to_start: true}
</div><p><strong>Design Patterns Used and Why:</strong></p>
<ul>
<li><strong>Comprehensive Status Pattern</strong>: Provides complete overview of all drum cache states in single API call</li>
<li><strong>Workflow Validation Pattern</strong>: Includes readiness indicators to prevent invalid job start attempts</li>
<li><strong>Iterative Collection Pattern</strong>: Systematically collects status from all drums for unified response</li>
<li><strong>Maximum Calculation Pattern</strong>: Determines workflow parameters (max layers) from cache analysis</li>
<li><strong>Cache Transparency Pattern</strong>: Exposes cache internals for operator verification and troubleshooting</li>
</ul>
<h6 id="post-cliclear-drum-cache" drum_id="true">POST /cli/clear-drum-cache/ </h6>
<p>Clears cached CLI file data for a specific drum (0, 1, or 2) in the multi-material workflow. This endpoint enables selective cache management for individual drums without affecting other drum caches or active jobs.</p>
<p><strong>How does it work:</strong></p>
<ol>
<li>Validates <code>drum_id</code> parameter using FastAPI Path constraints (0-2 range)</li>
<li>FastAPI dependency injection provides the <code>MultiMaterialJobService</code> instance</li>
<li>Calls <code>job_manager.clear_drum_cache_for_drum(drum_id)</code> to clear specific drum cache</li>
<li>Service sets the drum cache entry to None, freeing memory and resetting state</li>
<li>Returns confirmation response with success status and descriptive message</li>
<li>Does not affect hardware files, only clears backend cache memory</li>
</ol>
<p><strong>Simple Sequence Diagram:</strong></p>
<div class="mermaid">sequenceDiagram
    participant Frontend as MultiLayerJobControl
    participant ClearRouter as Multimaterial Router (multimaterial.py)
    participant JobService as MultiMaterialJobService
    participant DrumCache as Drum Cache Storage

    Note over DrumCache: Before: drum_cli_cache[1] = {filename, parsed_file, layer_count}

    Frontend-&gt;&gt;ClearRouter: POST /cli/clear-drum-cache/1
    ClearRouter-&gt;&gt;ClearRouter: clear_drum_cache_for_drum(drum_id=1)
    ClearRouter-&gt;&gt;ClearRouter: Validate drum_id (0-2)
    ClearRouter-&gt;&gt;JobService: clear_drum_cache_for_drum(1)
    JobService-&gt;&gt;DrumCache: Set drum_cli_cache[1] = None
    DrumCache--&gt;&gt;JobService: Cache cleared
    JobService--&gt;&gt;ClearRouter: Success confirmation
    ClearRouter--&gt;&gt;Frontend: {success: true, message: "Cleared cached CLI file for drum 1"}

    Note over DrumCache: After: drum_cli_cache[1] = None
</div><p><strong>Design Patterns Used and Why:</strong></p>
<ul>
<li><strong>Selective Clearing Pattern</strong>: Allows targeted cache management without affecting other drums</li>
<li><strong>Path Parameter Validation</strong>: Uses FastAPI constraints to ensure valid drum ID ranges</li>
<li><strong>Memory Management Pattern</strong>: Frees memory by setting cache entries to None</li>
<li><strong>Non-Destructive Pattern</strong>: Only affects backend cache, preserves hardware files</li>
<li><strong>Confirmation Response Pattern</strong>: Provides clear feedback about cache clearing operation</li>
</ul>
<h6 id="get-multimaterial-jobdrum-status" drum_id="true">GET /multimaterial-job/drum-status/ </h6>
<p>Retrieves detailed status information for a specific drum within the context of an active multi-material print job. This endpoint provides drum-specific monitoring data including layer progress, material status, and drum-specific error conditions.</p>
<p><strong>How does it work:</strong></p>
<ol>
<li>Validates <code>drum_id</code> parameter using FastAPI Path constraints (0-2 range)</li>
<li>FastAPI dependency injection provides the <code>MultiMaterialJobService</code> instance</li>
<li>Calls <code>job_manager.get_drum_status(drum_id)</code> to retrieve drum-specific job status</li>
<li>Returns structured <code>DrumStatusResponse</code> with drum-specific progress and state data</li>
<li>Handles cases where no active job exists or drum is not found with appropriate 404 errors</li>
<li>Provides detailed drum monitoring for active job execution</li>
</ol>
<p><strong>Simple Sequence Diagram:</strong></p>
<div class="mermaid">sequenceDiagram
    participant Frontend as DrumStatusComponent
    participant StatusRouter as Multimaterial Router (multimaterial.py)
    participant JobService as MultiMaterialJobService
    participant JobState as Current Job State

    Frontend-&gt;&gt;StatusRouter: GET /multimaterial-job/drum-status/2
    StatusRouter-&gt;&gt;StatusRouter: get_drum_status(drum_id=2)
    StatusRouter-&gt;&gt;StatusRouter: Validate drum_id (0-2)
    StatusRouter-&gt;&gt;JobService: get_drum_status(2)
    JobService-&gt;&gt;JobState: Get drum-specific status from job

    alt Active job with drum 2
        JobState--&gt;&gt;JobService: Drum status data
        JobService--&gt;&gt;StatusRouter: DrumStatusResponse
        StatusRouter--&gt;&gt;Frontend: {drum_id: 2, status: "active", current_layer: 15, ...}
    else No active job or drum not found
        JobService--&gt;&gt;StatusRouter: None
        StatusRouter--&gt;&gt;Frontend: HTTP 404 - No active job or drum not found
    end
</div><p><strong>Design Patterns Used and Why:</strong></p>
<ul>
<li><strong>Drum-Specific Monitoring Pattern</strong>: Provides targeted status information for individual drum operations</li>
<li><strong>Context-Aware Status Pattern</strong>: Returns status within context of active job execution</li>
<li><strong>Error Handling Pattern</strong>: Uses appropriate HTTP 404 for missing job/drum conditions</li>
<li><strong>Structured Response Pattern</strong>: Uses typed <code>DrumStatusResponse</code> for consistent drum status data</li>
<li><strong>Job Context Pattern</strong>: Requires active job context for meaningful drum status information</li>
</ul>
<p><strong>Note on Missing Endpoint:</strong><br>
The service layer includes a <code>clear_drum_cache()</code> method for clearing all drum caches simultaneously, but the corresponding <code>POST /cli/clear-drum-cache</code> endpoint (without drum_id) is not implemented in the API router. This represents a gap between service capabilities and API surface area that may be addressed in future releases.</p>
<h5 id="3234-drum-management-apis">******* Drum Management APIs </h5>
<h6 id="get-drumsdrum_idgeometry">GET /drums/{drum_id}/geometry </h6>
<p>Retrieves the current geometry file from the specified drum directly from the recoater hardware as a PNG image. Useful for verifying what is currently loaded on a drum.</p>
<p><strong>How does it work:</strong></p>
<ol>
<li>Validates <code>drum_id</code> with FastAPI <code>Path</code> constraints (0-2 range)</li>
<li>FastAPI dependency injection provides a <code>RecoaterClient</code> instance</li>
<li>Calls the client method to download the drum geometry from hardware</li>
<li>Returns binary PNG data with <code>image/png</code> media type and inline filename header</li>
<li>Translates hardware/API errors to HTTP 400/503/500 as appropriate</li>
<li>Operates directly on hardware (does not use cache)</li>
</ol>
<p><strong>Simple Sequence Diagram:</strong></p>
<div class="mermaid">sequenceDiagram
    participant Frontend as DebugView/Tools
    participant DrumRouter as Drum Router (drum.py)
    participant Client as RecoaterClient
    participant Hardware as Recoater Hardware

    Frontend-&gt;&gt;DrumRouter: GET /drums/{id}/geometry
    DrumRouter-&gt;&gt;DrumRouter: download_drum_geometry(drum_id)
    DrumRouter-&gt;&gt;Client: download geometry
    Client-&gt;&gt;Hardware: HTTP GET /drums/{id}/geometry
    Hardware--&gt;&gt;Client: PNG bytes
    Client--&gt;&gt;DrumRouter: PNG bytes
    DrumRouter--&gt;&gt;Frontend: image/png
</div><p><strong>Design Patterns Used and Why:</strong></p>
<ul>
<li><strong>Path Parameter Validation</strong>: Ensures only valid drum IDs (0–2)</li>
<li><strong>Hardware Abstraction</strong>: <code>RecoaterClient</code> hides HTTP details from the API layer</li>
<li><strong>Binary Response Pattern</strong>: Returns PNG with proper headers for browser rendering</li>
<li><strong>Error Translation</strong>: Converts hardware errors into HTTP exceptions</li>
<li><strong>Dependency Injection</strong>: Testable and swappable real vs. mock clients</li>
</ul>
<h6 id="get-drumsdrum_idgeometrypreview">GET /drums/{drum_id}/geometry/preview </h6>
<p>Generates a PNG preview for a specific layer from the cached CLI associated with the specified drum. This helps validate per‑drum content without hitting hardware.</p>
<p><strong>How does it work:</strong></p>
<ol>
<li>Validates <code>drum_id</code> and <code>layer</code> (1-based, default 1)</li>
<li>Uses <code>MultiMaterialJobService</code> to fetch cached, parsed CLI for the drum</li>
<li>Converts user <code>layer</code> to 0-based index and selects the layer</li>
<li>Renders that layer to PNG with <code>cli_parser.render_layer_to_png(..., drum_id)</code></li>
<li>Returns binary PNG with inline filename</li>
<li>Returns HTTP 404 if no CLI is cached for that drum</li>
</ol>
<p><strong>Simple Sequence Diagram:</strong></p>
<div class="mermaid">sequenceDiagram
    participant Frontend as PrintView (Preview)
    participant DrumRouter as Drum Router (drum.py)
    participant JobService as MultiMaterialJobService
    participant Parser as CLI Parser

    Frontend-&gt;&gt;DrumRouter: GET /drums/{id}/geometry/preview
    DrumRouter-&gt;&gt;JobService: get_cached_file_for_drum(id)
    JobService--&gt;&gt;DrumRouter: Parsed file + metadata
    DrumRouter-&gt;&gt;DrumRouter: Get specified layer for preview
    DrumRouter-&gt;&gt;DrumRouter: Validate layer number
    DrumRouter-&gt;&gt;Parser: render_layer_to_png(layer-1, drum_id)
    Parser--&gt;&gt;DrumRouter: PNG bytes
    DrumRouter--&gt;&gt;Frontend: image/png
</div><p><strong>Design Patterns Used and Why:</strong></p>
<ul>
<li><strong>Cache Retrieval Pattern</strong>: Uses drum‑specific cache, not hardware</li>
<li><strong>Layer Index Conversion</strong>: Converts 1-based input to 0-based internal indexing</li>
<li><strong>Binary Response Pattern</strong>: Returns rendered PNG</li>
<li><strong>Dependency Injection</strong>: Injected <code>MultiMaterialJobService</code></li>
<li><strong>Error Handling Pattern</strong>: 404 when no cached file exists for the drum</li>
</ul>
<h6 id="post-drumsdrum_idgeometry">POST /drums/{drum_id}/geometry </h6>
<p>Uploads a geometry file (CLI/PNG) to the specified drum on the hardware. Intended for direct hardware testing or maintenance.</p>
<p><strong>How does it work:</strong></p>
<ol>
<li>Validates <code>drum_id</code> (0–2) and accepts <code>UploadFile</code></li>
<li>Reads file bytes and determines content type (defaults to <code>application/octet-stream</code>)</li>
<li>Calls <code>recoater_client.upload_drum_geometry(drum_id, file_data, content_type)</code></li>
<li>Hardware saves/activates the uploaded geometry for that drum</li>
<li>Returns <code>FileUploadResponse</code> on success; maps errors to HTTP codes</li>
<li>Does not update cache automatically (hardware‑only operation)</li>
</ol>
<p><strong>Simple Sequence Diagram:</strong></p>
<div class="mermaid">sequenceDiagram
    participant Frontend as DebugView/Tools
    participant DrumRouter as Drum Router (drum.py)
    participant Client as RecoaterClient
    participant Hardware as Recoater Hardware

    Frontend-&gt;&gt;DrumRouter: POST /drums/{id}/geometry
    DrumRouter-&gt;&gt;DrumRouter: Read file with await file.read()
    DrumRouter-&gt;&gt;Client: upload_drum_geometry(id, bytes, type)
    Client-&gt;&gt;Hardware: HTTP POST /drums/{id}/geometry
    Hardware--&gt;&gt;Client: OK
    Client--&gt;&gt;DrumRouter: OK
    DrumRouter--&gt;&gt;Frontend: { success: true, drum_id }
</div><p><strong>Design Patterns Used and Why:</strong></p>
<ul>
<li><strong>Command Pattern</strong>: Encapsulates upload as a single operation</li>
<li><strong>Content-Type Handling</strong>: Preserves/infers content type for hardware compatibility</li>
<li><strong>Dependency Injection</strong>: Testable client wiring</li>
<li><strong>Path Parameter Validation</strong>: Enforces 3‑drum limit</li>
<li><strong>Error Translation</strong>: Maps connection/API errors to HTTP status</li>
</ul>
<h6 id="delete-drumsdrum_idgeometry">DELETE /drums/{drum_id}/geometry </h6>
<p>Deletes the geometry file currently stored on the specified drum on the hardware.</p>
<p><strong>How does it work:</strong></p>
<ol>
<li>Validates <code>drum_id</code> (0–2)</li>
<li>Uses injected <code>RecoaterClient</code> to call <code>delete_drum_geometry(drum_id)</code></li>
<li>Hardware removes the geometry associated with the drum</li>
<li>Returns <code>FileDeleteResponse</code> with success message</li>
<li>Translates connection/API errors to proper HTTP responses</li>
<li>Does not alter cached CLI files</li>
</ol>
<p><strong>Simple Sequence Diagram:</strong></p>
<div class="mermaid">sequenceDiagram
    participant Frontend as DebugView/Tools
    participant DrumRouter as Drum Router (drum.py)
    participant Client as RecoaterClient
    participant Hardware as Recoater Hardware

    Frontend-&gt;&gt;DrumRouter: DELETE /drums/{id}/geometry
    DrumRouter-&gt;&gt;Client: delete_drum_geometry(id)
    Client-&gt;&gt;Hardware: HTTP DELETE /drums/{id}/geometry
    Hardware--&gt;&gt;Client: OK
    Client--&gt;&gt;DrumRouter: OK
    DrumRouter--&gt;&gt;Frontend: { success: true, message: "deleted" }
</div><p><strong>Design Patterns Used and Why:</strong></p>
<ul>
<li><strong>Command Pattern</strong>: Single, side‑effecting delete action</li>
<li><strong>Dependency Injection</strong>: Client abstraction for hardware control</li>
<li><strong>Error Translation</strong>: Consistent HTTP error mapping</li>
<li><strong>Separation of Concerns</strong>: Hardware state changes do not mutate preview caches</li>
</ul>
<h5 id="3235-layer-parameter--preview-apis">******* Layer Parameter &amp; Preview APIs </h5>
<h6 id="get-layerparameters">GET /layer/parameters </h6>
<p>Retrieves the current layer printing parameters from the recoater hardware (e.g., filling_id, speed, x_offset, powder_saving). Useful for confirming or initializing UI controls.</p>
<p><strong>How does it work:</strong></p>
<ol>
<li>Dependency injection provides a <code>RecoaterClient</code> instance</li>
<li>Calls <code>recoater_client.get_layer_parameters()</code></li>
<li>Maps raw hardware response into <code>LayerParametersResponse</code></li>
<li>Returns JSON with typed fields for the frontend</li>
<li>Translates connection/API issues into HTTP 503/400/500 errors</li>
<li>Keeps API thin with validation in the client/model layer</li>
</ol>
<p><strong>Simple Sequence Diagram:</strong></p>
<div class="mermaid">sequenceDiagram
    participant Frontend as Configuration/PrintView
    participant LayerRouter as Layer Router (layer.py)
    participant Client as RecoaterClient

    Frontend-&gt;&gt;LayerRouter: GET /layer/parameters
    LayerRouter-&gt;&gt;Client: get_layer_parameters()
    Client--&gt;&gt;LayerRouter: { filling_id, speed, x_offset, powder_saving }
    LayerRouter--&gt;&gt;Frontend: LayerParametersResponse
</div><p><strong>Design Patterns Used and Why:</strong></p>
<ul>
<li><strong>DTO Pattern</strong>: <code>LayerParametersResponse</code> provides typed, validated output</li>
<li><strong>Dependency Injection</strong>: Hardware client supplied via <code>Depends</code></li>
<li><strong>Error Translation</strong>: Consistent mapping to HTTP status codes</li>
<li><strong>Thin Controller</strong>: Endpoint delegates to client for logic and validation</li>
</ul>
<h6 id="put-layerparameters">PUT /layer/parameters </h6>
<p>Updates the current layer printing parameters on the hardware. Supports safe, validated updates to speed, offsets, fill modes, and powder-saving options.</p>
<p><strong>How does it work:</strong></p>
<ol>
<li>Accepts <code>LayerParametersRequest</code> JSON with typed fields and constraints</li>
<li>Logs and validates payload via Pydantic model</li>
<li>Calls <code>recoater_client.set_layer_parameters(...)</code> with provided fields</li>
<li>Hardware applies parameter changes immediately/asynchronously</li>
<li>Returns simple success JSON with the parameters echoed back</li>
<li>Maps any client/hardware failures to appropriate HTTP responses</li>
</ol>
<p><strong>Simple Sequence Diagram:</strong></p>
<div class="mermaid">sequenceDiagram
    participant Frontend as Configuration/PrintView
    participant LayerRouter as Layer Router (layer.py)
    participant Client as RecoaterClient

    Frontend-&gt;&gt;LayerRouter: PUT /layer/parameters { speed, x_offset, ... }
    LayerRouter-&gt;&gt;LayerRouter: Validate LayerParametersRequest
    LayerRouter-&gt;&gt;Client: set_layer_parameters(...)
    Client--&gt;&gt;LayerRouter: OK
    LayerRouter--&gt;&gt;Frontend: { success: true, parameters: {...} }
</div><p><strong>Design Patterns Used and Why:</strong></p>
<ul>
<li><strong>DTO Pattern</strong>: Strongly-typed request/response models</li>
<li><strong>Command Pattern</strong>: Encapsulates a parameter-update operation</li>
<li><strong>Input Validation</strong>: Pydantic enforces ranges and types</li>
<li><strong>Dependency Injection</strong>: Testable client boundary</li>
</ul>
<h6 id="get-layerpreview">GET /layer/preview </h6>
<p>Generates a composite PNG preview for a specific layer, overlaying all cached drum CLIs (drums 0/1/2). Helpful to visually verify multi-material composition before printing.</p>
<p><strong>How does it work:</strong></p>
<ol>
<li>Accepts <code>layer</code> query param (1-based, default 1)</li>
<li>Uses <code>MultiMaterialJobService</code> to inspect caches and compute max layers</li>
<li>Returns 404 if no drum CLIs are cached</li>
<li>For the requested layer, collects each drums layer (if present) and overlays</li>
<li>Uses CLI parser to render the composite PNG (with drum color coding)</li>
<li>Returns PNG bytes with inline filename and <code>image/png</code> content type</li>
</ol>
<p><strong>Simple Sequence Diagram:</strong></p>
<div class="mermaid">sequenceDiagram
    participant Frontend as LayerPreview
    participant LayerRouter as Layer Router (layer.py)
    participant JobService as MultiMaterialJobService
    participant Parser as CLI Parser

    Frontend-&gt;&gt;LayerRouter: GET /layer/preview, {layer=5}
    LayerRouter-&gt;&gt;JobService: get_max_layers()
    JobService--&gt;&gt;LayerRouter: max_layers=n

    alt No cached CLIs (n == 0)
        LayerRouter--&gt;&gt;Frontend: HTTP 404 "No CLI files cached"
    else Layer out of range (5 &gt; n)
        LayerRouter--&gt;&gt;Frontend: HTTP 400 "Layer exceeds maximum"
    else Valid request (1 &lt;= 5 &lt;= n)
        LayerRouter-&gt;&gt;LayerRouter: drum_layers = {}
        Note over LayerRouter: Initialize drum_layers = {0: None, 1: None, 2: None}
        loop For each drum_id in [0,1,2]
            LayerRouter-&gt;&gt;JobService: get_cached_file_for_drum(drum_id)
            JobService--&gt;&gt;LayerRouter: drum_data or None

            alt Drum has requested layer
                LayerRouter-&gt;&gt;LayerRouter: target_layer = parsed_file.layers[5-1]
                Note over LayerRouter: Convert 1-based to 0-based index
                LayerRouter-&gt;&gt;LayerRouter: drum_layers[drum_id] = target_layer
            else Missing or too few layers
                LayerRouter-&gt;&gt;LayerRouter: drum_layers[drum_id] = None
            end
        end

        LayerRouter-&gt;&gt;Parser: render_composite_layer_to_png(drum_layers)
        Parser--&gt;&gt;LayerRouter: PNG bytes
        LayerRouter--&gt;&gt;Frontend: 200 image/png (inline filename)
    end
</div><p><strong>Design Patterns Used and Why:</strong></p>
<ul>
<li><strong>Composite Rendering Pattern</strong>: Overlays layers from multiple drums into a single image</li>
<li><strong>Layer Index Conversion</strong>: User-friendly 1-based inputs, internal 0-based indexing</li>
<li><strong>Binary Response Pattern</strong>: PNG output optimized for browser display</li>
<li><strong>Dependency Injection</strong>: Injected job service and parser enable testing</li>
<li><strong>Graceful Errors</strong>: Clear 404 when caches are empty</li>
</ul>
<h5 id="3236-job-management-apis">******* Job Management APIs </h5>
<p>The Job Management APIs provide direct control over print job operations on the recoater hardware. These endpoints allow starting, cancelling, and querying the status of print jobs. While deprecated in favor of the Multi-Material Job Orchestration APIs for normal operations, they remain available for debugging and direct hardware control.</p>
<p><strong>IMPORTANT</strong> Any use of these endpoints requires manual operation and coordination with the PLC system. They do not implement the full multi-material workflow and will only print the bottom-most layer of any uploaded CLI file.</p>
<h6 id="post-job">POST /job </h6>
<p>Starts a new print job on the recoater hardware without automated layer iteration. Crucially, this means that only the bottom-most layer of any CLI file will ever be printed. Deprecated in favor of the Multi-Material Job Orchestration APIs for normal print operations, but still available in DebugView for direct control/testing.</p>
<p><strong>How does it work:</strong></p>
<ol>
<li>Dependency injection provides <code>RecoaterClient</code></li>
<li>Calls <code>recoater_client.start_print_job()</code></li>
<li>Hardware/server creates a new job and returns a <code>job_id</code></li>
<li>Endpoint wraps the result as <code>PrintJobResponse</code> with status "started"</li>
<li>Returns HTTP 409 if a job is already active or cannot start</li>
<li>Maps connection/API errors to 503/400 as appropriate</li>
</ol>
<p><strong>Simple Sequence Diagram:</strong></p>
<div class="mermaid">sequenceDiagram
    participant Frontend as PrintView/Tools
    participant JobRouter as Job Router (job.py)
    participant Client as RecoaterClient
    participant Hardware as Recoater Hardware

    Frontend-&gt;&gt;JobRouter: POST /job
    JobRouter-&gt;&gt;Client: start_print_job()
    Client-&gt;&gt;Client: _make_request("POST", "/print/job")
    Client-&gt;&gt;Hardware: HTTP POST /job
    Hardware--&gt;&gt;Client: { job_id }
    Client--&gt;&gt;JobRouter: { job_id }
    JobRouter--&gt;&gt;Frontend: { success: true, status: "started", job_id }
</div><p><strong>Design Patterns Used and Why:</strong></p>
<ul>
<li><strong>Dependency Injection</strong>: Clean hardware abstraction via client</li>
<li><strong>Command Pattern</strong>: Initiates a single start operation</li>
<li><strong>Response Model Pattern</strong>: Typed <code>PrintJobResponse</code></li>
<li><strong>Error Translation</strong>: Proper HTTP mapping, including 409 conflict</li>
</ul>
<h6 id="delete-job">DELETE /job </h6>
<p>Cancels the currently active print job on the hardware. Deprecated in favor of Multi-Material Job cancellation.</p>
<p><strong>How does it work:</strong></p>
<ol>
<li>Injects <code>RecoaterClient</code></li>
<li>Calls <code>recoater_client.cancel_print_job()</code></li>
<li>Hardware cancels/clears the active job state</li>
<li>Returns <code>PrintJobResponse</code> with status "cancelled"</li>
<li>Maps connection/API errors to 503/400/500 accordingly</li>
</ol>
<p><strong>Simple Sequence Diagram:</strong></p>
<div class="mermaid">sequenceDiagram
    participant Frontend as PrintView/Tools
    participant JobRouter as Job Router (job.py)
    participant Client as RecoaterClient
    participant Hardware as Recoater Hardware

    Frontend-&gt;&gt;JobRouter: DELETE /job
    JobRouter-&gt;&gt;Client: cancel_print_job()
    Client-&gt;&gt;Hardware: HTTP DELETE /job
    Hardware--&gt;&gt;Client: OK
    Client--&gt;&gt;JobRouter: OK
    JobRouter--&gt;&gt;Frontend: { success: true, status: "cancelled" }
</div><p><strong>Design Patterns Used and Why:</strong></p>
<ul>
<li><strong>Command Pattern</strong>: Single, idempotent cancel operation</li>
<li><strong>Dependency Injection</strong>: Swappable client for testing</li>
<li><strong>Error Translation</strong>: Consistent HTTP error mapping</li>
<li><strong>Response Model Pattern</strong>: Structured confirmation</li>
</ul>
<h6 id="get-jobstatus">GET /job/status </h6>
<p>Retrieves the current job status from the recoater hardware. Deprecated in favor of the Multi-Material Job status endpoints.</p>
<p><strong>How does it work:</strong></p>
<ol>
<li>Injects <code>RecoaterClient</code></li>
<li>Calls <code>recoater_client.get_print_job_status()</code></li>
<li>Maps hardware response to <code>PrintJobStatusResponse</code></li>
<li>Returns typed JSON with relevant job fields</li>
<li>Maps connection/API errors to 503/400/500 accordingly</li>
<li>Read-only and safe to poll at low frequency</li>
</ol>
<p><strong>Simple Sequence Diagram:</strong></p>
<div class="mermaid">sequenceDiagram
    participant Frontend as PrintView/Tools
    participant JobRouter as Job Router (job.py)
    participant Client as RecoaterClient

    Frontend-&gt;&gt;JobRouter: GET /job/status
    JobRouter-&gt;&gt;Client: get_print_job_status()
    Client--&gt;&gt;JobRouter: { status, progress, ... }
    JobRouter--&gt;&gt;Frontend: PrintJobStatusResponse
</div><p><strong>Design Patterns Used and Why:</strong></p>
<ul>
<li><strong>DTO Pattern</strong>: <code>PrintJobStatusResponse</code> for typed outputs</li>
<li><strong>Dependency Injection</strong>: Client boundary for easy mocking</li>
<li><strong>Error Translation</strong>: Predictable HTTP status codes on failure</li>
<li><strong>Thin Controller</strong>: Delegates to client for business logic</li>
</ul>
<h4 id="324-recoater-hardware-control-apis">3.2.4 Recoater Hardware Control APIs </h4>
<p>The Recoater Hardware Control APIs provide direct control interfaces for the physical components of the Aerosint Recoater system. These APIs enable precise manipulation of drum movement, pressure systems, blade positioning, and leveler operations. They serve as the bridge between the web interface and the industrial hardware, allowing operators to control the recoater through intuitive frontend components that translate to precise hardware commands.</p>
<h5 id="3241-drum-control-api">******* Drum Control API </h5>
<p>The Drum Control API manages the three critical aspects of drum operations: rotational motion, ejection pressure for powder distribution, and suction pressure for powder retention. These APIs enable precise control of material handling during the multi-material printing process.</p>
<h6 id="get-drumsdrum_idmotion">GET /drums/{drum_id}/motion </h6>
<p>Retrieves the current motion status and parameters for a specific drum. This endpoint provides real-time information about drum position, movement state, and active motion commands to support operator monitoring and system coordination.</p>
<p><strong>How does it work:</strong></p>
<ol>
<li>FastAPI path validation ensures <code>drum_id</code> is within valid range (0-2)</li>
<li>Dependency injection provides an initialized <code>RecoaterClient</code> instance</li>
<li>Client calls <code>get_drum_motion(drum_id)</code> to fetch current motion status from hardware</li>
<li>Response includes motion state, position, speed, and movement mode information</li>
<li>Returns structured response with connection status and motion details</li>
<li>Error handling converts hardware exceptions to appropriate HTTP status codes</li>
</ol>
<p><strong>Simple Sequence Diagram:</strong></p>
<div class="mermaid">sequenceDiagram
    participant Frontend as RecoaterView
    participant DrumRouter as Drum Router (drum.py)
    participant Client as RecoaterClient (client.py)
    participant Hardware as Recoater Hardware

    Frontend-&gt;&gt;DrumRouter: GET /drums/0/motion
    DrumRouter-&gt;&gt;DrumRouter: get_drum_motion(drum_id=0)
    DrumRouter-&gt;&gt;DrumRouter: Validate drum_id (0-2)
    DrumRouter-&gt;&gt;Client: get_drum_motion(0)
    Client-&gt;&gt;Hardware: HTTP GET /drums/0/motion
    Hardware--&gt;&gt;Client: Motion status JSON
    Client--&gt;&gt;DrumRouter: Motion data
    DrumRouter--&gt;&gt;Frontend: {drum_id: 0, motion: {...}, connected: true}
</div><p><strong>Design Patterns Used and Why:</strong></p>
<ul>
<li><strong>Path Parameter Validation Pattern</strong>: Uses FastAPI <code>Path</code> constraints to validate drum ID ranges (0-2) at the API layer</li>
<li><strong>Hardware Abstraction Pattern</strong>: <code>RecoaterClient</code> abstracts hardware communication details from the API endpoint</li>
<li><strong>Connection Status Reporting Pattern</strong>: Always includes connection status in responses for frontend health monitoring</li>
<li><strong>Error Translation Pattern</strong>: Converts hardware-specific exceptions to standardized HTTP status codes (503 for connection errors, 400 for API errors)</li>
<li><strong>Dependency Injection Pattern</strong>: Enables testable hardware client management and easy mock substitution</li>
</ul>
<h6 id="post-drumsdrum_idmotion">POST /drums/{drum_id}/motion </h6>
<p>Executes motion commands for drum rotation with support for multiple movement modes. This endpoint accepts motion parameters and translates them into hardware-specific commands for precise drum positioning and movement control.</p>
<p><strong>How does it work:</strong></p>
<ol>
<li>Validates <code>drum_id</code> parameter and <code>DrumMotionRequest</code> payload using Pydantic</li>
<li>Motion request includes mode (absolute, relative, turns, speed, homing), speed, and optional distance/turns</li>
<li>Calls <code>client.set_drum_motion()</code> with validated parameters</li>
<li>Hardware executes the motion command based on specified mode and parameters</li>
<li>Returns command acknowledgment with motion parameters and hardware response</li>
<li>Supports multiple motion modes for different operational requirements</li>
</ol>
<p><strong>Simple Sequence Diagram:</strong></p>
<div class="mermaid">sequenceDiagram
    participant Frontend as RecoaterView
    participant DrumRouter as Drum Router (drum.py)
    participant Client as RecoaterClient (client.py)
    participant Hardware as Recoater Hardware

    Frontend-&gt;&gt;DrumRouter: POST /drums/1/motion {mode: "relative", speed: 10, distance: 50}
    DrumRouter-&gt;&gt;DrumRouter: set_drum_motion(motion_request, drum_id=1)
    DrumRouter-&gt;&gt;DrumRouter: Validate DrumMotionRequest
    DrumRouter-&gt;&gt;Client: set_drum_motion(1, "relative", 10, 50)
    Client-&gt;&gt;Hardware: HTTP POST /drums/1/motion
    Hardware--&gt;&gt;Client: Command acknowledgment
    Client--&gt;&gt;DrumRouter: Motion response
    DrumRouter--&gt;&gt;Frontend: {drum_id: 1, motion_command: {...}, response: {...}}
</div><p><strong>Design Patterns Used and Why:</strong></p>
<ul>
<li><strong>Command Pattern</strong>: Encapsulates motion commands with mode, speed, and distance parameters for flexible operation</li>
<li><strong>Multi-Mode Operation Pattern</strong>: Supports different motion modes (absolute, relative, turns, speed, homing) through single endpoint</li>
<li><strong>Parameter Validation Pattern</strong>: Uses Pydantic models to validate motion parameters and constraints before hardware execution</li>
<li><strong>Response Echo Pattern</strong>: Returns both the original command parameters and hardware response for confirmation</li>
<li><strong>Speed Constraint Pattern</strong>: Validates speed parameters to ensure safe hardware operation</li>
</ul>
<h6 id="delete-drumsdrum_idmotion">DELETE /drums/{drum_id}/motion </h6>
<p>Provides emergency stop functionality for drum motion commands. This endpoint immediately cancels any active motion to ensure operator safety and prevent hardware damage during emergency situations.</p>
<p><strong>How does it work:</strong></p>
<ol>
<li>Validates <code>drum_id</code> parameter for target drum identification</li>
<li>Calls <code>client.cancel_drum_motion(drum_id)</code> to send stop command</li>
<li>Hardware immediately halts current motion and clears motion queue</li>
<li>Returns cancellation confirmation with action type and hardware response</li>
<li>Ensures immediate response for safety-critical operations</li>
<li>Maintains connection status for frontend error handling</li>
</ol>
<p><strong>Simple Sequence Diagram:</strong></p>
<div class="mermaid">sequenceDiagram
    participant Frontend as RecoaterView
    participant DrumRouter as Drum Router (drum.py)
    participant Client as RecoaterClient (client.py)
    participant Hardware as Recoater Hardware

    Frontend-&gt;&gt;DrumRouter: DELETE /drums/2/motion (Emergency Stop)
    DrumRouter-&gt;&gt;DrumRouter: cancel_drum_motion(drum_id=2)
    DrumRouter-&gt;&gt;Client: cancel_drum_motion(2)
    Client-&gt;&gt;Hardware: HTTP DELETE /drums/2/motion
    Hardware--&gt;&gt;Client: Motion cancelled immediately
    Client--&gt;&gt;DrumRouter: Cancellation confirmation
    DrumRouter--&gt;&gt;Frontend: {drum_id: 2, action: "motion_cancelled", response: {...}}
</div><p><strong>Design Patterns Used and Why:</strong></p>
<ul>
<li><strong>Emergency Stop Pattern</strong>: Provides immediate motion cancellation for safety-critical situations</li>
<li><strong>Action Confirmation Pattern</strong>: Returns explicit action type ("motion_cancelled") for clear operation feedback</li>
<li><strong>Immediate Response Pattern</strong>: Prioritizes fast response time for emergency operations</li>
<li><strong>Safety First Pattern</strong>: No validation delays - immediately forwards cancellation to hardware</li>
</ul>
<h6 id="get-drumsdrum_idejection">GET /drums/{drum_id}/ejection </h6>
<p>Monitors ejection pressure status for powder distribution control. This endpoint provides current and target pressure readings in configurable units to support different operational requirements and operator preferences.</p>
<p><strong>How does it work:</strong></p>
<ol>
<li>Validates <code>drum_id</code> parameter and optional <code>unit</code> query parameter (pascal/bar)</li>
<li>Calls <code>client.get_drum_ejection(drum_id, unit)</code> to fetch pressure data</li>
<li>Hardware returns current pressure, target pressure, and pressure limits</li>
<li>Response includes pressure values in requested units with conversion handling</li>
<li>Supports both Pascal and Bar units for international compatibility</li>
<li>Provides pressure status for powder flow monitoring and control</li>
</ol>
<p><strong>Simple Sequence Diagram:</strong></p>
<div class="mermaid">sequenceDiagram
    participant Frontend as RecoaterView
    participant DrumRouter as Drum Router (drum.py)
    participant Client as RecoaterClient (client.py)
    participant Hardware as Recoater Hardware

    Frontend-&gt;&gt;DrumRouter: GET /drums/0/ejection?unit=bar
    DrumRouter-&gt;&gt;DrumRouter: get_drum_ejection(drum_id=0, unit="bar")
    DrumRouter-&gt;&gt;Client: get_drum_ejection(0, "bar")
    Client-&gt;&gt;Hardware: HTTP GET /drums/0/ejection
    Hardware--&gt;&gt;Client: Pressure data (converted to bar)
    Client--&gt;&gt;DrumRouter: Ejection pressure info
    DrumRouter--&gt;&gt;Frontend: {drum_id: 0, ejection: {current: 2.5, target: 3.0, unit: "bar"}}
</div><p><strong>Design Patterns Used and Why:</strong></p>
<ul>
<li><strong>Unit Conversion Pattern</strong>: Supports multiple pressure units (Pascal, Bar) for international compatibility</li>
<li><strong>Query Parameter Pattern</strong>: Uses optional query parameters for unit specification with sensible defaults</li>
<li><strong>Pressure Monitoring Pattern</strong>: Provides comprehensive pressure status including current, target, and limits</li>
<li><strong>Hardware Abstraction Pattern</strong>: Client handles unit conversion logic, keeping API interface clean</li>
</ul>
<h6 id="put-drumsdrum_idejection">PUT /drums/{drum_id}/ejection </h6>
<p>Controls ejection pressure settings for powder distribution during printing operations. This endpoint accepts pressure targets with unit specifications and applies them to the hardware for precise material flow control.</p>
<p><strong>How does it work:</strong></p>
<ol>
<li>Validates <code>DrumEjectionRequest</code> payload with target pressure and unit specification</li>
<li>Pydantic validation ensures non-negative pressure values and valid units</li>
<li>Calls <code>client.set_drum_ejection()</code> with target pressure and unit parameters</li>
<li>Hardware adjusts ejection pressure system to reach target value</li>
<li>Returns command echo and hardware response for confirmation</li>
<li>Supports pressure range validation for safe operation</li>
</ol>
<p><strong>Simple Sequence Diagram:</strong></p>
<div class="mermaid">sequenceDiagram
    participant Frontend as RecoaterView
    participant DrumRouter as Drum Router (drum.py)
    participant Client as RecoaterClient (client.py)
    participant Hardware as Recoater Hardware

    Frontend-&gt;&gt;DrumRouter: PUT /drums/1/ejection {target: 25000, unit: "pascal"}
    DrumRouter-&gt;&gt;DrumRouter: set_drum_ejection(ejection_request, drum_id=1)
    DrumRouter-&gt;&gt;DrumRouter: Validate DrumEjectionRequest
    DrumRouter-&gt;&gt;Client: set_drum_ejection(1, 25000, "pascal")
    Client-&gt;&gt;Hardware: HTTP PUT /drums/1/ejection
    Hardware--&gt;&gt;Client: Pressure adjustment initiated
    Client--&gt;&gt;DrumRouter: Ejection response
    DrumRouter--&gt;&gt;Frontend: {drum_id: 1, ejection_command: {...}, response: {...}}
</div><p><strong>Design Patterns Used and Why:</strong></p>
<ul>
<li><strong>Pressure Control Pattern</strong>: Provides precise pressure adjustment for material flow control</li>
<li><strong>Unit Specification Pattern</strong>: Allows pressure specification in multiple units for operational flexibility</li>
<li><strong>Safety Validation Pattern</strong>: Validates pressure ranges to prevent dangerous operating conditions</li>
<li><strong>Command Echo Pattern</strong>: Returns both command parameters and hardware response for operation verification</li>
</ul>
<h6 id="get-drumsdrum_idsuction">GET /drums/{drum_id}/suction </h6>
<p>Retrieves the current suction pressure information for a specific drum. Suction helps retain powder during recoating by applying negative pressure.</p>
<p><strong>How does it work:</strong></p>
<ol>
<li>Validates <code>drum_id</code> using FastAPI <code>Path</code> constraints (0-2 range)</li>
<li>Dependency injection provides the <code>RecoaterClient</code> instance</li>
<li>Calls <code>client.get_drum_suction(drum_id)</code> to fetch suction data</li>
<li>Returns structured JSON containing current suction pressure and connection status</li>
<li>Translates connection/API errors to 503/400 and unexpected issues to 500</li>
<li>Read-only, safe for periodic monitoring</li>
</ol>
<p><strong>Simple Sequence Diagram:</strong></p>
<div class="mermaid">sequenceDiagram
    participant Frontend as RecoaterView
    participant DrumRouter as Drum Router (drum.py)
    participant Client as RecoaterClient (client.py)
    participant Hardware as Recoater Hardware

    Frontend-&gt;&gt;DrumRouter: GET /drums/0/suction
    DrumRouter-&gt;&gt;DrumRouter: get_drum_suction(drum_id=0)
    DrumRouter-&gt;&gt;Client: get_drum_suction(0)
    Client-&gt;&gt;Hardware: HTTP GET /drums/0/suction
    Hardware--&gt;&gt;Client: Suction pressure JSON
    Client--&gt;&gt;DrumRouter: Suction data
    DrumRouter--&gt;&gt;Frontend: {drum_id: 0, suction: {...}, connected: true}
</div><p><strong>Design Patterns Used and Why:</strong></p>
<ul>
<li><strong>Path Parameter Validation Pattern</strong>: Ensures valid drum IDs (0-2)</li>
<li><strong>Hardware Abstraction Pattern</strong>: <code>RecoaterClient</code> isolates hardware specifics</li>
<li><strong>Error Translation Pattern</strong>: Consistent HTTP status codes on failures</li>
<li><strong>Monitoring Pattern</strong>: Safe, read-only status retrieval</li>
</ul>
<h6 id="put-drumsdrum_idsuction">PUT /drums/{drum_id}/suction </h6>
<p>Sets the target suction pressure for a specific drum. This controls powder retention during the coating process.</p>
<p><strong>How does it work:</strong></p>
<ol>
<li>Validates <code>DrumSuctionRequest</code> payload (target pressure)</li>
<li>Injects <code>RecoaterClient</code> and calls <code>client.set_drum_suction(drum_id, target)</code></li>
<li>Hardware adjusts suction system toward the target value</li>
<li>Returns echoed command parameters and hardware response</li>
<li>Maps connection/API errors to 503/400 and unexpected issues to 500</li>
<li>Enforces drum ID range (0-2) via <code>Path</code> constraints</li>
</ol>
<p><strong>Simple Sequence Diagram:</strong></p>
<div class="mermaid">sequenceDiagram
    participant Frontend as RecoaterView
    participant DrumRouter as Drum Router (drum.py)
    participant Client as RecoaterClient (client.py)
    participant Hardware as Recoater Hardware

    Frontend-&gt;&gt;DrumRouter: PUT /drums/1/suction {target: 12000}
    DrumRouter-&gt;&gt;DrumRouter: set_drum_suction(suction_request, drum_id=1)
    DrumRouter-&gt;&gt;DrumRouter: Validate DrumSuctionRequest
    DrumRouter-&gt;&gt;Client: set_drum_suction(1, 12000)
    Client-&gt;&gt;Hardware: HTTP PUT /drums/1/suction
    Hardware--&gt;&gt;Client: Adjustment initiated
    Client--&gt;&gt;DrumRouter: Suction response
    DrumRouter--&gt;&gt;Frontend: {drum_id: 1, suction_command: {target: 12000}, response: {...}}
</div><p><strong>Design Patterns Used and Why:</strong></p>
<ul>
<li><strong>Command Pattern</strong>: Encapsulates suction adjustment operation</li>
<li><strong>Input Validation Pattern</strong>: Pydantic enforces safe ranges/types</li>
<li><strong>Dependency Injection Pattern</strong>: Testable client boundary</li>
<li><strong>Action Confirmation Pattern</strong>: Returns both command and response</li>
</ul>
<h5 id="3242-leveler-control-api">3.2.4.2 Leveler Control API </h5>
<p>The Leveler Control API manages the powder spreading mechanism that ensures uniform layer thickness across the build platform. This system controls pressure application and sensor monitoring for consistent powder distribution quality.</p>
<h6 id="get-levelerpressure">GET /leveler/pressure </h6>
<p>Retrieves comprehensive leveler pressure information including maximum, target, and current pressure values. This endpoint provides essential data for powder surface quality control and leveler system monitoring.</p>
<p><strong>How does it work:</strong></p>
<ol>
<li>Dependency injection provides initialized <code>RecoaterClient</code> instance</li>
<li>Calls <code>client.get_leveler_pressure()</code> to fetch comprehensive pressure data</li>
<li>Hardware returns maximum pressure capability, target pressure, and current pressure</li>
<li>Response includes all pressure values for complete system status</li>
<li>Provides data for powder spreading quality assessment</li>
<li>Supports leveler system health monitoring</li>
</ol>
<p><strong>Simple Sequence Diagram:</strong></p>
<div class="mermaid">sequenceDiagram
    participant Frontend as RecoaterView
    participant LevelerRouter as Leveler Router (leveler.py)
    participant Client as RecoaterClient (client.py)
    participant Hardware as Recoater Hardware

    Frontend-&gt;&gt;LevelerRouter: GET /leveler/pressure
    LevelerRouter-&gt;&gt;LevelerRouter: get_leveler_pressure()
    LevelerRouter-&gt;&gt;Client: get_leveler_pressure()
    Client-&gt;&gt;Hardware: HTTP GET /leveler/pressure
    Hardware--&gt;&gt;Client: Pressure data (max, target, current)
    Client--&gt;&gt;LevelerRouter: Leveler pressure info
    LevelerRouter--&gt;&gt;Frontend: {leveler_pressure: {maximum: 50000, target: 30000, value: 29500}}
</div><p><strong>Design Patterns Used and Why:</strong></p>
<ul>
<li><strong>Comprehensive Status Pattern</strong>: Returns maximum, target, and current values for complete system visibility</li>
<li><strong>Quality Control Pattern</strong>: Provides pressure data essential for powder spreading quality assessment</li>
<li><strong>System Health Pattern</strong>: Enables monitoring of leveler system performance and status</li>
<li><strong>Pascal Standard Pattern</strong>: Uses consistent Pascal units for all pressure measurements</li>
</ul>
<h6 id="put-levelerpressure">PUT /leveler/pressure </h6>
<p>Sets target leveling pressure for powder spreading operations. This endpoint adjusts the pressure applied during powder distribution to achieve uniform layer thickness and surface quality.</p>
<p><strong>How does it work:</strong></p>
<ol>
<li>Validates <code>LevelerPressureRequest</code> payload with target pressure in Pascal</li>
<li>Pydantic validation ensures non-negative pressure values within safe ranges</li>
<li>Calls <code>client.set_leveler_pressure()</code> with target pressure parameter</li>
<li>Hardware adjusts leveler pressure system to reach specified target</li>
<li>Returns command parameters and hardware response for verification</li>
<li>Affects powder spreading uniformity and layer quality</li>
</ol>
<p><strong>Simple Sequence Diagram:</strong></p>
<div class="mermaid">sequenceDiagram
    participant Frontend as RecoaterView
    participant LevelerRouter as Leveler Router (leveler.py)
    participant Client as RecoaterClient (client.py)
    participant Hardware as Recoater Hardware

    Frontend-&gt;&gt;LevelerRouter: PUT /leveler/pressure {target: 35000}
    LevelerRouter-&gt;&gt;LevelerRouter: set_leveler_pressure(pressure_request)
    LevelerRouter-&gt;&gt;LevelerRouter: Validate LevelerPressureRequest
    LevelerRouter-&gt;&gt;Client: set_leveler_pressure(35000)
    Client-&gt;&gt;Hardware: HTTP PUT /leveler/pressure
    Hardware--&gt;&gt;Client: Pressure adjustment initiated
    Client--&gt;&gt;LevelerRouter: Leveler response
    LevelerRouter--&gt;&gt;Frontend: {leveler_command: {target: 35000}, response: {...}}
</div><p><strong>Design Patterns Used and Why:</strong></p>
<ul>
<li><strong>Quality Control Pattern</strong>: Enables precise pressure adjustment for optimal powder spreading</li>
<li><strong>Safety Validation Pattern</strong>: Validates pressure ranges to prevent equipment damage</li>
<li><strong>Command Verification Pattern</strong>: Returns both command and response for operation confirmation</li>
<li><strong>Uniform Distribution Pattern</strong>: Controls pressure for consistent layer thickness</li>
</ul>
<h6 id="get-levelersensor">GET /leveler/sensor </h6>
<p>Retrieves the current state of the leveler’s magnetic sensor. This indicates whether the leveler is engaged/detected, supporting diagnostics and alignment checks.</p>
<p><strong>How does it work:</strong></p>
<ol>
<li>Dependency injection provides <code>RecoaterClient</code></li>
<li>Calls <code>client.get_leveler_sensor()</code> to read sensor state</li>
<li>Returns JSON containing sensor status and connection flag</li>
<li>Maps connection/API errors to 503/400 and unexpected issues to 500</li>
<li>Read-only, safe to poll periodically</li>
<li>Complements pressure endpoints for full leveler monitoring</li>
</ol>
<p><strong>Simple Sequence Diagram:</strong></p>
<div class="mermaid">sequenceDiagram
    participant Frontend as RecoaterView
    participant LevelerRouter as Leveler Router (leveler.py)
    participant Client as RecoaterClient (client.py)

    Frontend-&gt;&gt;LevelerRouter: GET /leveler/sensor
    LevelerRouter-&gt;&gt;Client: get_leveler_sensor()
    Client--&gt;&gt;LevelerRouter: { state: ... }
    LevelerRouter--&gt;&gt;Frontend: { leveler_sensor: {...}, connected: true }
</div><p><strong>Design Patterns Used and Why:</strong></p>
<ul>
<li><strong>Hardware Abstraction Pattern</strong>: Client shields API from hardware details</li>
<li><strong>Error Translation Pattern</strong>: Predictable HTTP codes on failure</li>
<li><strong>Monitoring Pattern</strong>: Lightweight, read-only status retrieval</li>
</ul>
<h5 id="3243-blade-control-api">******* Blade Control API </h5>
<p>The Blade Control API manages the scraping blade system that removes excess powder from the drum surface after each layer. This system provides both collective control for synchronized operations and individual screw control for precise blade angle adjustment.</p>
<h6 id="get-drumsdrum_idbladescrews">GET /drums/{drum_id}/blade/screws </h6>
<p>Retrieves comprehensive information about both screws of the specified drum's scraping blade. This endpoint provides position data, operational limits, and status information for complete blade system monitoring.</p>
<p><strong>How does it work:</strong></p>
<ol>
<li>Validates <code>drum_id</code> parameter for target drum identification</li>
<li>Calls <code>client.get_blade_screws_info(drum_id)</code> to fetch comprehensive screw data</li>
<li>Hardware returns information for both screws including position, limits, and status</li>
<li>Response includes data for both screws of the blade system</li>
<li>Provides complete blade system status for operational monitoring</li>
<li>Supports blade positioning and limit verification</li>
</ol>
<p><strong>Simple Sequence Diagram:</strong></p>
<div class="mermaid">sequenceDiagram
    participant Frontend as RecoaterView
    participant BladeRouter as Blade Router (blade.py)
    participant Client as RecoaterClient (client.py)
    participant Hardware as Recoater Hardware

    Frontend-&gt;&gt;BladeRouter: GET /drums/0/blade/screws
    BladeRouter-&gt;&gt;BladeRouter: get_blade_screws_info(drum_id=0)
    BladeRouter-&gt;&gt;Client: get_blade_screws_info(0)
    Client-&gt;&gt;Hardware: HTTP GET /drums/0/blade/screws
    Hardware--&gt;&gt;Client: Both screws data (position, limits, status)
    Client--&gt;&gt;BladeRouter: Blade screws info
    BladeRouter--&gt;&gt;Frontend: {drum_id: 0, blade_screws: [{screw_0: {...}}, {screw_1: {...}}]}
</div><p><strong>Design Patterns Used and Why:</strong></p>
<ul>
<li><strong>Collective Information Pattern</strong>: Returns data for both screws in a single response for efficiency</li>
<li><strong>Comprehensive Status Pattern</strong>: Provides position, limits, and status for complete system visibility</li>
<li><strong>Blade System Pattern</strong>: Treats both screws as components of unified blade system</li>
<li><strong>Hardware Abstraction Pattern</strong>: Client handles complex screw data aggregation</li>
</ul>
<h6 id="get-drumsdrum_idbladescrewsmotion">GET /drums/{drum_id}/blade/screws/motion </h6>
<p>Retrieves the current collective motion command/status for the blade screws on a drum. Useful for checking if a synchronous move is underway.</p>
<p><strong>How does it work:</strong></p>
<ol>
<li>Validates <code>drum_id</code> (0-2) via <code>Path</code> constraints</li>
<li>Injects <code>RecoaterClient</code></li>
<li>Calls <code>client.get_blade_screws_motion(drum_id)</code></li>
<li>Returns motion parameters/status and connection flag</li>
<li>Maps connection/API errors to 503/400; unexpected to 500</li>
</ol>
<p><strong>Simple Sequence Diagram:</strong></p>
<div class="mermaid">sequenceDiagram
    participant Frontend as RecoaterView
    participant BladeRouter as Blade Router (blade.py)
    participant Client as RecoaterClient
    Frontend-&gt;&gt;BladeRouter: GET /drums/0/blade/screws/motion
    BladeRouter-&gt;&gt;Client: get_blade_screws_motion(0)
    Client--&gt;&gt;BladeRouter: { mode: ..., distance: ..., state: ... }
    BladeRouter--&gt;&gt;Frontend: {drum_id: 0, motion: {...}, connected: true}
</div><p><strong>Design Patterns Used and Why:</strong></p>
<ul>
<li><strong>Status Query Pattern</strong> for in-progress operations</li>
<li><strong>Error Translation Pattern</strong> for consistent failures</li>
</ul>
<h6 id="post-drumsdrum_idbladescrewsmotion">POST /drums/{drum_id}/blade/screws/motion </h6>
<p>Creates a collective motion command for both blade screws (synchronous movement).</p>
<p><strong>How does it work:</strong></p>
<ol>
<li>Validates <code>BladeMotionRequest</code> payload (<code>mode</code>, <code>distance</code>)</li>
<li>Calls <code>client.set_blade_screws_motion(drum_id, mode, distance)</code></li>
<li>Hardware starts synchronized move across both screws</li>
<li>Returns echoed command and hardware response</li>
<li>Errors translated to 503/400/500 consistently</li>
</ol>
<p><strong>Simple Sequence Diagram:</strong></p>
<div class="mermaid">sequenceDiagram
    participant Frontend as RecoaterView
    participant BladeRouter as Blade Router (blade.py)
    participant Client as RecoaterClient
    Frontend-&gt;&gt;BladeRouter: POST /drums/1/blade/screws/motion {mode: linear, distance: 2.0}
    BladeRouter-&gt;&gt;Client: set_blade_screws_motion(1, linear, 2.0)
    Client--&gt;&gt;BladeRouter: Response
    BladeRouter--&gt;&gt;Frontend: {motion_command: {...}, response: {...}}
</div><p><strong>Design Patterns Used and Why:</strong></p>
<ul>
<li><strong>Command Pattern</strong> encapsulates movement</li>
<li><strong>Action Confirmation Pattern</strong> echoes command and response</li>
</ul>
<h6 id="delete-drumsdrum_idbladescrewsmotion">DELETE /drums/{drum_id}/blade/screws/motion </h6>
<p>Cancels any active collective blade screws motion for the specified drum.</p>
<p><strong>How does it work:</strong></p>
<ol>
<li>Validates <code>drum_id</code> and injects <code>RecoaterClient</code></li>
<li>Calls <code>client.cancel_blade_screws_motion(drum_id)</code></li>
<li>Returns action indicator and hardware response</li>
<li>Errors mapped to 503/400/500</li>
</ol>
<p><strong>Simple Sequence Diagram:</strong></p>
<div class="mermaid">sequenceDiagram
    participant Frontend as RecoaterView
    participant BladeRouter as Blade Router (blade.py)
    participant Client as RecoaterClient
    Frontend-&gt;&gt;BladeRouter: DELETE /drums/2/blade/screws/motion
    BladeRouter-&gt;&gt;Client: cancel_blade_screws_motion(2)
    Client--&gt;&gt;BladeRouter: Response
    BladeRouter--&gt;&gt;Frontend: {action: motion_cancelled, response: {...}}
</div><p><strong>Design Patterns Used and Why:</strong></p>
<ul>
<li><strong>Cancellation Pattern</strong> for safe stop of in-flight commands</li>
<li><strong>Error Translation Pattern</strong> standardizes failures</li>
</ul>
<h6 id="get-drumsdrum_idbladescrews-1" screw_id="true">GET /drums/{drum_id}/blade/screws/ </h6>
<p>Retrieves detailed information (position, limits, status) for a specific blade screw.</p>
<p><strong>How does it work:</strong></p>
<ol>
<li>Validates <code>drum_id</code> and <code>screw_id</code></li>
<li>Calls <code>client.get_blade_screw_info(drum_id, screw_id)</code></li>
<li>Returns screw-specific info and connection status</li>
<li>Errors mapped to 503/400/500</li>
</ol>
<p><strong>Simple Sequence Diagram:</strong></p>
<div class="mermaid">sequenceDiagram
    participant Frontend as RecoaterView
    participant BladeRouter as Blade Router (blade.py)
    participant Client as RecoaterClient
    Frontend-&gt;&gt;BladeRouter: GET /drums/0/blade/screws/1
    BladeRouter-&gt;&gt;Client: get_blade_screw_info(0, 1)
    Client--&gt;&gt;BladeRouter: Screw data
    BladeRouter--&gt;&gt;Frontend: {screw_info: {...}}
</div><p><strong>Design Patterns Used and Why:</strong></p>
<ul>
<li><strong>Fine-Grained Control Pattern</strong> for individual component status</li>
</ul>
<h6 id="get-drumsdrum_idbladescrewsscrew_idmotion">GET /drums/{drum_id}/blade/screws/{screw_id}/motion </h6>
<p>Retrieves the current motion command/status for a single blade screw.</p>
<p><strong>How does it work:</strong></p>
<ol>
<li>Validates parameters and injects client</li>
<li>Calls <code>client.get_blade_screw_motion(drum_id, screw_id)</code></li>
<li>Returns motion status for that screw</li>
<li>Errors mapped to 503/400/500</li>
</ol>
<p><strong>Simple Sequence Diagram:</strong></p>
<div class="mermaid">sequenceDiagram
    participant Frontend as RecoaterView
    participant BladeRouter as Blade Router (blade.py)
    participant Client as RecoaterClient
    Frontend-&gt;&gt;BladeRouter: GET /drums/0/blade/screws/1/motion
    BladeRouter-&gt;&gt;Client: get_blade_screw_motion(0, 1)
    Client--&gt;&gt;BladeRouter: Motion data
    BladeRouter--&gt;&gt;Frontend: {motion: {...}}
</div><p><strong>Design Patterns Used and Why:</strong></p>
<ul>
<li><strong>Status Query Pattern</strong> for targeted component</li>
</ul>
<h6 id="post-drumsdrum_idbladescrewsscrew_idmotion">POST /drums/{drum_id}/blade/screws/{screw_id}/motion </h6>
<p>Creates a motion command for an individual blade screw (independent of its pair).</p>
<p><strong>How does it work:</strong></p>
<ol>
<li>Validates <code>BladeIndividualMotionRequest</code> (<code>distance</code>)</li>
<li>Calls <code>client.set_blade_screw_motion(drum_id, screw_id, distance)</code></li>
<li>Hardware actuates the specific screw</li>
<li>Returns echoed command and response</li>
<li>Errors mapped to 503/400/500</li>
</ol>
<p><strong>Simple Sequence Diagram:</strong></p>
<div class="mermaid">sequenceDiagram
    participant Frontend as RecoaterView
    participant BladeRouter as Blade Router (blade.py)
    participant Client as RecoaterClient
    Frontend-&gt;&gt;BladeRouter: POST /drums/2/blade/screws/0/motion {distance: -1.5}
    BladeRouter-&gt;&gt;Client: set_blade_screw_motion(2, 0, -1.5)
    Client--&gt;&gt;BladeRouter: Response
    BladeRouter--&gt;&gt;Frontend: {motion_command: {...}, response: {...}}
</div><p><strong>Design Patterns Used and Why:</strong></p>
<ul>
<li><strong>Command Pattern</strong> per-component control</li>
<li><strong>Action Confirmation Pattern</strong> echoes command</li>
</ul>
<h6 id="delete-drumsdrum_idbladescrewsscrew_idmotion">DELETE /drums/{drum_id}/blade/screws/{screw_id}/motion </h6>
<p>Cancels any active motion for a specific blade screw.</p>
<p><strong>How does it work:</strong></p>
<ol>
<li>Validates parameters and injects client</li>
<li>Calls <code>client.cancel_blade_screw_motion(drum_id, screw_id)</code></li>
<li>Returns action indicator and response</li>
<li>Errors mapped to 503/400/500</li>
</ol>
<p><strong>Simple Sequence Diagram:</strong></p>
<div class="mermaid">sequenceDiagram
    participant Frontend as RecoaterView
    participant BladeRouter as Blade Router (blade.py)
    participant Client as RecoaterClient
    Frontend-&gt;&gt;BladeRouter: DELETE /drums/1/blade/screws/1/motion
    BladeRouter-&gt;&gt;Client: cancel_blade_screw_motion(1, 1)
    Client--&gt;&gt;BladeRouter: Response
    BladeRouter--&gt;&gt;Frontend: {action: motion_cancelled, response: {...}}
</div><p><strong>Design Patterns Used and Why:</strong></p>
<ul>
<li><strong>Cancellation Pattern</strong> for safe stop</li>
<li><strong>Error Translation Pattern</strong> for predictable failures</li>
</ul>
<h3 id="33-service-layer-business-logic">3.3 Service Layer (Business Logic) </h3>
<h4 id="331-communication">3.3.1 Communication </h4>
<h4 id="332-job-management">3.3.2 Job Management </h4>
<h4 id="333-monitoring">3.3.3 Monitoring </h4>
<h3 id="334-opcua">3.3.4 OPCUA </h3>
<h3 id="34-infrastructure-layer">3.4 Infrastructure Layer </h3>
<h4 id="341-cli-editor">3.4.1 CLI Editor </h4>
<h4 id="342-recoater-client">3.4.2 Recoater Client </h4>
<h4 id="343-mock-recoater-client">3.4.3 Mock Recoater Client </h4>
<h3 id="35-overall-backend-workflow">3.5 Overall Backend Workflow </h3>
<p>This section explains how the different parts of the backend system work together, from the moment a request arrives until a response is sent back.</p>
<h4 id="351-router-registration-flow">3.5.1 Router Registration Flow </h4>
<p>When the backend application starts up, it needs to organize all the API endpoints (URLs) so that incoming requests know where to go. This is like setting up a postal system where each address (URL) has a specific mailbox (endpoint function).</p>
<p><strong>How Router Registration Works:</strong></p>
<div class="mermaid">sequenceDiagram
    participant Main as FastAPI App
    participant API as API Package
    participant Print as Print Router
    participant Hardware as Hardware Router
    participant Endpoints as Individual Endpoints

    Main-&gt;&gt;API: Import API routers
    API-&gt;&gt;Print: Include print router (/print)
    API-&gt;&gt;Hardware: Include hardware router (/recoater)

    Print-&gt;&gt;Endpoints: Register CLI endpoints
    Print-&gt;&gt;Endpoints: Register layer endpoints
    Print-&gt;&gt;Endpoints: Register multimaterial endpoints

    Hardware-&gt;&gt;Endpoints: Register drum endpoints
    Hardware-&gt;&gt;Endpoints: Register leveler endpoints
    Hardware-&gt;&gt;Endpoints: Register blade endpoints

    API-&gt;&gt;Main: Return configured routers
    Main-&gt;&gt;Main: Mount routers with prefixes
</div><p><strong>Step-by-Step Process:</strong></p>
<ol>
<li><strong>Main Application Starts</strong>: The FastAPI application begins initialization</li>
<li><strong>Import Routers</strong>: The main app imports router modules for different functional areas</li>
<li><strong>Organize by Domain</strong>:
<ul>
<li>Print Router handles all printing-related URLs (like <code>/api/print/cli/upload</code>)</li>
<li>Hardware Router handles all hardware control URLs (like <code>/api/recoater/drums/0/motion</code>)</li>
</ul>
</li>
<li><strong>Register Individual Endpoints</strong>: Each router registers its specific endpoint functions</li>
<li><strong>Mount with Prefixes</strong>: The main app combines all routers with their URL prefixes</li>
<li><strong>Ready to Handle Requests</strong>: Now when a request comes in, the system knows exactly which function should handle it</li>
</ol>
<p><strong>Why This Organization Matters:</strong></p>
<ul>
<li><strong>Clear Structure</strong>: Related endpoints are grouped together</li>
<li><strong>Easy Maintenance</strong>: Changes to print features only affect the print router</li>
<li><strong>Scalability</strong>: New features can be added by creating new routers</li>
<li><strong>Team Collaboration</strong>: Different developers can work on different routers without conflicts</li>
</ul>
<hr>
<h2 id="4-frontend-development">4. Frontend Development </h2>
<div class="mermaid">graph TB
    subgraph "Frontend Application"
        subgraph "Presentation Layer (Views)"
            STATUS_VIEW["StatusView"]
            RECOATER_VIEW["RecoaterView"]
            PRINT_VIEW["PrintView"]
            CONFIG_VIEW["ConfigurationView"]
            DEBUG_VIEW["DebugView"]
        end

        subgraph "Component Layer"
            STATUS_INDICATOR["StatusIndicator"]
            DRUM_CONTROL["DrumControl"]
            LEVELER_CONTROL["LevelerControl"]
            FILE_UPLOAD["FileUploadColumn"]
            JOB_PROGRESS["JobProgressDisplay"]
            MULTI_LAYER_JOB["MultiLayerJobControl"]
            ERROR_MODAL["CriticalErrorModal"]
            ERROR_DISPLAY["ErrorDisplayPanel"]
        end

        subgraph "Application State Layer (Pinia Stores)"
            STATUS_STORE["statusStore"]
            PRINT_JOB_STORE["printJobStore"]
        end

        subgraph "Service Layer"
            API_SERVICE["apiService"]
        end

        subgraph "Infrastructure Layer"
            ROUTER["Vue Router"]
            WEBSOCKET["WebSocket Client"]
            HTTP_CLIENT["Axios HTTP Client"]
        end
    end

    subgraph "Backend"
        subgraph "Presentation Layer"
            SYSTEM_APIS["System APIs"]
            subgraph "Print APIs"
                CLI_API["CLI API"]
                LAYER_API["Layer API"]
                MM_API["Multi-Material API"]
                JOB_API["Job API"]
                PRINT_DRUM_API["Drum API"]
            end
            subgraph "Recoater Control APIs"
                RECOATER_DRUM_API["Drum API"]
                RECOATER_BLADE_API["Blade API"]
                RECOATER_LEVELER_API["Leveler API"]
            end
        end
        subgraph "Service Layer"
            JOB_MANAGEMENT["Job Management"]
            COMMUNICATION["Communication"]
            MONITORING["Monitoring"]
            OPCUA["OPCUA"]
        end
        subgraph "Infrastructure Layer"
            CLI_EDITOR["CLI Editor"]
            RECOATER_CLIENT["Recoater Client"]
            MOCK_CLIENT["Mock Recoater Client"]
        end
    end

    subgraph "Aerosint Recoater"
        SERVER["Server"]
    end

    subgraph "TwinCAT XAR"
        PLC["PLC"]
    end

RECOATER_CLIENT &lt;--&gt; SERVER

JOB_MANAGEMENT &lt;--&gt; RECOATER_CLIENT
JOB_MANAGEMENT &lt;--&gt; OPCUA
COMMUNICATION &lt;--&gt; RECOATER_CLIENT
MONITORING &lt;--&gt; RECOATER_CLIENT
MONITORING &lt;--&gt; COMMUNICATION
OPCUA &lt;--&gt; PLC

SYSTEM_APIS &lt;--&gt; RECOATER_CLIENT
SYSTEM_APIS &lt;--&gt; OPCUA

CLI_API &lt;--&gt; JOB_MANAGEMENT
CLI_API &lt;--&gt; CLI_EDITOR
LAYER_API &lt;--&gt; JOB_MANAGEMENT
MM_API &lt;--&gt; JOB_MANAGEMENT
JOB_API &lt;--&gt; JOB_MANAGEMENT
PRINT_DRUM_API &lt;--&gt; RECOATER_CLIENT

RECOATER_DRUM_API &lt;--&gt; RECOATER_CLIENT
RECOATER_BLADE_API &lt;--&gt; RECOATER_CLIENT
RECOATER_LEVELER_API &lt;--&gt; RECOATER_CLIENT

STATUS_VIEW &lt;--&gt; STATUS_STORE
STATUS_VIEW &lt;--&gt; API_SERVICE
RECOATER_VIEW &lt;--&gt; STATUS_STORE
RECOATER_VIEW &lt;--&gt; API_SERVICE
PRINT_VIEW &lt;--&gt; PRINT_JOB_STORE
PRINT_VIEW &lt;--&gt; API_SERVICE
CONFIG_VIEW &lt;--&gt; API_SERVICE
DEBUG_VIEW &lt;--&gt; STATUS_STORE
DEBUG_VIEW &lt;--&gt; API_SERVICE

STATUS_INDICATOR &lt;--&gt; STATUS_STORE
DRUM_CONTROL &lt;--&gt; STATUS_STORE
DRUM_CONTROL &lt;--&gt; API_SERVICE
LEVELER_CONTROL &lt;--&gt; STATUS_STORE
LEVELER_CONTROL &lt;--&gt; API_SERVICE
FILE_UPLOAD &lt;--&gt; API_SERVICE
JOB_PROGRESS &lt;--&gt; PRINT_JOB_STORE
MULTI_LAYER_JOB &lt;--&gt; PRINT_JOB_STORE
MULTI_LAYER_JOB &lt;--&gt; API_SERVICE
ERROR_MODAL &lt;--&gt; PRINT_JOB_STORE
ERROR_DISPLAY &lt;--&gt; PRINT_JOB_STORE

STATUS_STORE &lt;--&gt; API_SERVICE
STATUS_STORE &lt;--&gt; WEBSOCKET
PRINT_JOB_STORE &lt;--&gt; API_SERVICE
PRINT_JOB_STORE &lt;--&gt; STATUS_STORE

API_SERVICE &lt;--&gt; HTTP_CLIENT
ROUTER &lt;--&gt; STATUS_STORE

API_SERVICE &lt;--&gt; SYSTEM_APIS
API_SERVICE &lt;--&gt; CLI_API
API_SERVICE &lt;--&gt; LAYER_API
API_SERVICE &lt;--&gt; MM_API
API_SERVICE &lt;--&gt; JOB_API
API_SERVICE &lt;--&gt; PRINT_DRUM_API
API_SERVICE &lt;--&gt; RECOATER_DRUM_API
API_SERVICE &lt;--&gt; RECOATER_BLADE_API
API_SERVICE &lt;--&gt; RECOATER_LEVELER_API

WEBSOCKET &lt;--&gt; COMMUNICATION

</div><h3 id="41-frontend-layered-architecture">4.1 Frontend Layered Architecture </h3>
<ul>
<li>Presentation Layer (Views &amp; Components)</li>
<li>Application Layer (Stores &amp; Business Logic)</li>
<li>Service Layer (API Communication)</li>
<li>Infrastructure Layer (Utilities &amp; External Integrations)</li>
<li>Component Hierarchy and Dependencies</li>
<li>State Management Architecture</li>
<li>Routing and Navigation Structure</li>
</ul>
<h3 id="42-component-development">4.2 Component Development </h3>
<ul>
<li>Vue.js 3 Composition API</li>
<li>Component Lifecycle</li>
<li>Props and Events</li>
<li>Slot Usage</li>
</ul>
<h3 id="43-state-management">4.3 State Management </h3>
<ul>
<li>Pinia Store Setup</li>
<li>State Organization</li>
<li>Actions and Getters</li>
<li>Store Composition</li>
</ul>
<h3 id="44-routing">4.4 Routing </h3>
<ul>
<li>Vue Router Configuration</li>
<li>Route Guards</li>
<li>Navigation Patterns</li>
</ul>
<h3 id="45-api-integration">4.5 API Integration </h3>
<ul>
<li>HTTP Client Setup</li>
<li>Request Interceptors</li>
<li>Error Handling</li>
<li>Loading States</li>
</ul>
<h3 id="46-real-time-features">4.6 Real-time Features </h3>
<ul>
<li>WebSocket Integration</li>
<li>Event Handling</li>
<li>State Synchronization</li>
</ul>
<h3 id="47-styling-and-ui">4.7 Styling and UI </h3>
<ul>
<li>CSS Organization</li>
<li>Component Styling</li>
<li>Responsive Design</li>
</ul>
<h2 id="5-api-documentation">5. API Documentation </h2>
<h3 id="51-api-overview">5.1 API Overview </h3>
<h3 id="52-authentication">5.2 Authentication </h3>
<h3 id="53-endpoint-reference">5.3 Endpoint Reference </h3>
<ul>
<li>Print Control APIs</li>
<li>Recoater Control APIs</li>
<li>Status APIs</li>
<li>Configuration APIs</li>
</ul>
<h3 id="54-websocket-events">5.4 WebSocket Events </h3>
<h3 id="55-error-codes">5.5 Error Codes </h3>
<h3 id="56-rate-limiting">5.6 Rate Limiting </h3>

      </div>
      
      
    
    
    <script type="module">
// TODO: If ZenUML gets integrated into mermaid in the future,
//      we can remove the following lines.


var MERMAID_CONFIG = ({"startOnLoad":false});
if (typeof MERMAID_CONFIG !== 'undefined') {
  MERMAID_CONFIG.startOnLoad = false
  MERMAID_CONFIG.cloneCssStyles = false
  MERMAID_CONFIG.theme = "default"
}

mermaid.initialize(MERMAID_CONFIG || {})
if (typeof(window['Reveal']) !== 'undefined') {
  function mermaidRevealHelper(event) {
    var currentSlide = event.currentSlide
    var diagrams = currentSlide.querySelectorAll('.mermaid')
    for (var i = 0; i < diagrams.length; i++) {
      var diagram = diagrams[i]
      if (!diagram.hasAttribute('data-processed')) {
        mermaid.init(null, diagram, ()=> {
          Reveal.slide(event.indexh, event.indexv)
        })
      }
    }
  }
  Reveal.addEventListener('slidetransitionend', mermaidRevealHelper)
  Reveal.addEventListener('ready', mermaidRevealHelper)
  await mermaid.run({
    nodes: document.querySelectorAll('.mermaid')
  })
} else {
  await mermaid.run({
    nodes: document.querySelectorAll('.mermaid')
  })
}
</script>
    
    
    
  
    </body></html>