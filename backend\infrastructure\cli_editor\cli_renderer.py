"""
CLI Renderer Module
===================

This module provides the CliRenderer mixin for rendering CLI (Common Layer Interface) layers
as PNG images. It handles autoscaling, centering, and drum-specific coloring to visualize
geometry distributions across multiple drums.

Key Features:
- Render individual layers with optional drum-specific previews.
- Generate configuration previews using sample geometry and color mapping.
- Supports polylines and hatches with part_id/group_id-based drum color assignment.
- Uses PIL for image generation and in-memory output.

Dependencies:
- PIL (Pillow) for image processing.
- Relies on cli_models for data structures and cli_exceptions for error handling.

Usage:
    class MyClass(CliRenderer):
        pass
    instance = MyClass()
    png_bytes = instance.render_layer_to_png(layer, width=800, height=600, drum_id=0)
"""
import io
from PIL import Image, ImageDraw
from .cli_models import Cli<PERSON>ay<PERSON>, Point, Polyline, Hatch
from .cli_exceptions import CliRenderingError

class CliRenderer:
    """Mixin for handling rendering of CLI layers as PNG images."""

    def render_layer_to_png(self, layer: CliLayer, width: int = 800, height: int = 600, drum_id: int = None) -> bytes:
        """
        Renders a single CliLayer object to a PNG byte string with autoscaling.

        Args:
            layer: The CliLayer object to render.
            width: The width of the output PNG image.
            height: The height of the output PNG image.
            drum_id: Optional drum ID for single-drum preview (affects coloring)

        Returns:
            A byte string containing the PNG image data.
        """
        image = Image.new("RGB", (width, height), "white")
        draw = ImageDraw.Draw(image)

        # Define drum colors matching the Legend component
        DRUM_COLORS = {
            0: "#3498db",  # Blue - Drum 0
            1: "#e67e22",  # Orange - Drum 1
            2: "#27ae60"   # Green - Drum 2
        }

        all_points = [p for poly in layer.polylines for p in poly.points]
        for hatch in layer.hatches:
            all_points.extend([h[0] for h in hatch.lines])
            all_points.extend([h[1] for h in hatch.lines])

        if not all_points:
            draw.text((10, 10), "No geometry in this layer", fill="black")
        else:
            # Determine bounding box of the geometry
            min_x = min(p.x for p in all_points)
            max_x = max(p.x for p in all_points)
            min_y = min(p.y for p in all_points)
            max_y = max(p.y for p in all_points)

            geo_width = max_x - min_x
            geo_height = max_y - min_y
            
            # Handle zero-dimension case
            if geo_width == 0 and geo_height == 0:
                 draw.point([(width / 2, height / 2)], fill="black")
            else:
                if geo_width == 0: geo_width = 1
                if geo_height == 0: geo_height = 1

                # Calculate scale factor to fit geometry, with a margin
                margin = 0.1
                scale_x = (width * (1 - 2 * margin)) / geo_width
                scale_y = (height * (1 - 2 * margin)) / geo_height
                scale = min(scale_x, scale_y)

                # Calculate offsets to center the geometry
                offset_x = (width - geo_width * scale) / 2
                offset_y = (height - geo_height * scale) / 2

                def transform(p: Point) -> tuple:
                    # Y-axis is inverted in PIL
                    px = (p.x - min_x) * scale + offset_x
                    py = (max_y - p.y) * scale + offset_y
                    return (px, py)

                # Draw polylines with drum-specific colors
                for poly in layer.polylines:
                    if len(poly.points) > 1:
                        if drum_id is not None:
                            # Single drum preview - use the specific drum color
                            color = DRUM_COLORS.get(drum_id, "#000000")
                        else:
                            # Layer configuration preview - map part_id to drum color
                            mapped_drum = poly.part_id % 3  # Map part_id to drum 0, 1, or 2
                            color = DRUM_COLORS.get(mapped_drum, "#000000")

                        draw.line([transform(p) for p in poly.points], fill=color, width=2)

                # Draw hatches with drum-specific colors
                for hatch in layer.hatches:
                    for line in hatch.lines:
                        if drum_id is not None:
                            # Single drum preview - use the specific drum color
                            color = DRUM_COLORS.get(drum_id, "#000000")
                        else:
                            # Layer configuration preview - map group_id to drum color
                            mapped_drum = hatch.group_id % 3  # Map group_id to drum 0, 1, or 2
                            color = DRUM_COLORS.get(mapped_drum, "#000000")

                        draw.line((transform(line[0]), transform(line[1])), fill=color, width=1)

        # Save to in-memory buffer
        img_buffer = io.BytesIO()
        image.save(img_buffer, format='PNG')
        return img_buffer.getvalue()

    def render_composite_layer_to_png(self, drum_layers: dict, width: int = 800, height: int = 600) -> bytes:
        """
        Renders a composite view of layers from multiple drums with drum-specific colors.
        
        Args:
            drum_layers: Dictionary with drum_id -> CliLayer or None for each drum
            width: The width of the output PNG image
            height: The height of the output PNG image
            
        Returns:
            A byte string containing the PNG image data showing overlaid geometry
        """
        image = Image.new("RGB", (width, height), "white")
        draw = ImageDraw.Draw(image)

        # Define drum colors matching the Legend component
        DRUM_COLORS = {
            0: "#3498db",  # Blue - Drum 0 (top layer)
            1: "#e67e22",  # Orange - Drum 1 (middle layer)
            2: "#27ae60"   # Green - Drum 2 (bottom layer)
        }

        # Collect all points from all drums to determine bounding box
        all_points = []
        valid_layers = []
        
        for drum_id in [0, 1, 2]:
            layer = drum_layers.get(drum_id)
            if layer is not None:
                valid_layers.append((drum_id, layer))
                # Collect points from this layer
                for poly in layer.polylines:
                    all_points.extend(poly.points)
                for hatch in layer.hatches:
                    all_points.extend([h[0] for h in hatch.lines])
                    all_points.extend([h[1] for h in hatch.lines])

        if not all_points:
            draw.text((10, 10), "No geometry in selected layer", fill="black")
        else:
            # Determine bounding box of all geometry
            min_x = min(p.x for p in all_points)
            max_x = max(p.x for p in all_points)
            min_y = min(p.y for p in all_points)
            max_y = max(p.y for p in all_points)

            geo_width = max_x - min_x
            geo_height = max_y - min_y
            
            # Handle zero-dimension case
            if geo_width == 0 and geo_height == 0:
                draw.point([(width / 2, height / 2)], fill="black")
            else:
                if geo_width == 0: geo_width = 1
                if geo_height == 0: geo_height = 1

                # Calculate scale factor to fit geometry, with a margin
                margin = 0.1
                scale_x = (width * (1 - 2 * margin)) / geo_width
                scale_y = (height * (1 - 2 * margin)) / geo_height
                scale = min(scale_x, scale_y)

                # Calculate offsets to center the geometry
                offset_x = (width - geo_width * scale) / 2
                offset_y = (height - geo_height * scale) / 2

                def transform(p: Point) -> tuple:
                    # Y-axis is inverted in PIL
                    px = (p.x - min_x) * scale + offset_x
                    py = (max_y - p.y) * scale + offset_y
                    return (px, py)

                # Draw layers in reverse order (2, 1, 0) so drum 0 appears on top
                for drum_id, layer in reversed(valid_layers):
                    color = DRUM_COLORS.get(drum_id, "#000000")
                    
                    # Draw polylines for this drum
                    for poly in layer.polylines:
                        if len(poly.points) > 1:
                            draw.line([transform(p) for p in poly.points], fill=color, width=2)

                    # Draw hatches for this drum
                    for hatch in layer.hatches:
                        for line in hatch.lines:
                            draw.line((transform(line[0]), transform(line[1])), fill=color, width=1)

        # Save to in-memory buffer
        img_buffer = io.BytesIO()
        image.save(img_buffer, format='PNG')
        return img_buffer.getvalue()

    def render_layer_configuration_preview(self, width: int = 800, height: int = 600) -> bytes:
        """
        Renders a preview of the current layer configuration showing how geometry
        would be distributed across drums using the defined color scheme.

        This creates a mock layer with sample geometry to demonstrate the color mapping.
        In a real implementation, this would use the actual current layer configuration.

        Args:
            width: The width of the output PNG image.
            height: The height of the output PNG image.

        Returns:
            A byte string containing the PNG image data.
        """
        # Create a sample layer with geometry for demonstration
        # In a real implementation, this would get the actual current layer configuration
        sample_layer = CliLayer(
            z_height=0.0,
            polylines=[
                # Sample polylines for each drum
                Polyline(part_id=0, direction=0, points=[
                    Point(x=10, y=10), Point(x=30, y=10), Point(x=30, y=30), Point(x=10, y=30), Point(x=10, y=10)
                ]),
                Polyline(part_id=1, direction=0, points=[
                    Point(x=40, y=10), Point(x=60, y=10), Point(x=60, y=30), Point(x=40, y=30), Point(x=40, y=10)
                ]),
                Polyline(part_id=2, direction=0, points=[
                    Point(x=70, y=10), Point(x=90, y=10), Point(x=90, y=30), Point(x=70, y=30), Point(x=70, y=10)
                ])
            ],
            hatches=[
                # Sample hatches for each drum
                Hatch(group_id=0, lines=[
                    (Point(x=15, y=15), Point(x=25, y=15)),
                    (Point(x=15, y=20), Point(x=25, y=20)),
                    (Point(x=15, y=25), Point(x=25, y=25))
                ]),
                Hatch(group_id=1, lines=[
                    (Point(x=45, y=15), Point(x=55, y=15)),
                    (Point(x=45, y=20), Point(x=55, y=20)),
                    (Point(x=45, y=25), Point(x=55, y=25))
                ]),
                Hatch(group_id=2, lines=[
                    (Point(x=75, y=15), Point(x=85, y=15)),
                    (Point(x=75, y=20), Point(x=85, y=20)),
                    (Point(x=75, y=25), Point(x=85, y=25))
                ])
            ]
        )

        # Render the sample layer without specifying a drum_id (uses color mapping)
        return self.render_layer_to_png(sample_layer, width, height, drum_id=None)    