# Chapter 16: Vue.js Design Patterns and Architectural Principles

```
╔══════════════════════════════════════════════════════════════════════════════════════╗
║                        CHAPTER 16: VUE.JS DESIGN PATTERNS                            ║
║                   Professional Patterns for Scalable Applications                   ║
╚══════════════════════════════════════════════════════════════════════════════════════╝
```

## Learning Objectives
By the end of this chapter, you will understand:
- Essential design patterns for Vue.js applications
- SOLID principles applied to component architecture
- Advanced composition patterns using the Composition API
- State management patterns for complex industrial applications
- Testing patterns and maintainable code structures
- Performance optimization through architectural patterns

## Core Design Principles

### 1. **Single Responsibility Principle (SRP)**

Each component should have one reason to change.

```vue
<!-- ❌ BAD: Component doing too many things -->
<template>
  <div class="drum-dashboard">
    <!-- Drum controls -->
    <div class="drum-controls">
      <button @click="startDrum">Start</button>
      <button @click="stopDrum">Stop</button>
    </div>
    
    <!-- Status display -->
    <div class="status-display">
      <div>Temperature: {{ temperature }}°C</div>
      <div>Status: {{ status }}</div>
    </div>
    
    <!-- Settings form -->
    <form @submit="saveSettings">
      <input v-model="settings.speed" type="number">
      <input v-model="settings.material" type="text">
      <button type="submit">Save</button>
    </form>
    
    <!-- Data table -->
    <table>
      <tr v-for="record in records" :key="record.id">
        <td>{{ record.timestamp }}</td>
        <td>{{ record.value }}</td>
      </tr>
    </table>
  </div>
</template>
```

```vue
<!-- ✅ GOOD: Separated into focused components -->
<template>
  <div class="drum-dashboard">
    <DrumControls 
      :status="drumStatus" 
      @start="handleStart" 
      @stop="handleStop" 
    />
    <StatusDisplay 
      :temperature="temperature" 
      :status="status" 
    />
    <DrumSettings 
      v-model="settings" 
      @save="handleSaveSettings" 
    />
    <DataTable 
      :records="records" 
      :columns="tableColumns" 
    />
  </div>
</template>

<script>
// Each component has a single responsibility:
// - DrumControls: Drum operation buttons
// - StatusDisplay: Status information display
// - DrumSettings: Settings form management
// - DataTable: Data presentation
</script>
```

### 2. **Open/Closed Principle (OCP)**

Components should be open for extension, closed for modification.

```javascript
// ✅ GOOD: Extensible notification system
// Base notification composable
export function useNotification() {
  const notifications = ref([])
  
  const addNotification = (notification) => {
    notifications.value.push({
      id: Date.now(),
      timestamp: new Date(),
      ...notification
    })
  }
  
  const removeNotification = (id) => {
    const index = notifications.value.findIndex(n => n.id === id)
    if (index > -1) {
      notifications.value.splice(index, 1)
    }
  }
  
  return {
    notifications: readonly(notifications),
    addNotification,
    removeNotification
  }
}

// Extended for different notification types
export function useErrorNotification() {
  const { addNotification, ...base } = useNotification()
  
  const addError = (message, details) => {
    addNotification({
      type: 'error',
      message,
      details,
      persistent: true,
      actions: [
        { label: 'Retry', action: () => retryOperation() },
        { label: 'Report', action: () => reportError(details) }
      ]
    })
  }
  
  return { ...base, addError }
}

export function useSuccessNotification() {
  const { addNotification, ...base } = useNotification()
  
  const addSuccess = (message) => {
    addNotification({
      type: 'success',
      message,
      autoRemove: true,
      duration: 3000
    })
  }
  
  return { ...base, addSuccess }
}
```

### 3. **Dependency Inversion Principle (DIP)**

Depend on abstractions, not concretions.

```javascript
// ❌ BAD: Direct dependency on specific API implementation
export default {
  setup() {
    const loadDrumData = async () => {
      // Tightly coupled to specific API implementation
      const response = await fetch('/api/drums/1')
      const data = await response.json()
      drumData.value = data
    }
  }
}

// ✅ GOOD: Depend on abstraction (injected service)
export default {
  setup(props, { inject }) {
    // Inject abstraction
    const apiService = inject('apiService')
    const logger = inject('logger')
    
    const loadDrumData = async (drumId) => {
      try {
        // Use abstraction
        const data = await apiService.getDrumData(drumId)
        drumData.value = data
        logger.info('Drum data loaded successfully')
      } catch (error) {
        logger.error('Failed to load drum data', error)
      }
    }
  }
}

// Provide implementations at app level
app.provide('apiService', new ApiService())
app.provide('logger', new Logger())
```

## Component Design Patterns

### 1. **Container/Presentational Pattern**

Separate logic from presentation.

```vue
<!-- Container Component (Logic) -->
<template>
  <DrumControlPresentation
    :drums="drums"
    :selectedDrum="selectedDrum"
    :isLoading="isLoading"
    :error="error"
    @selectDrum="handleSelectDrum"
    @startDrum="handleStartDrum"
    @stopDrum="handleStopDrum"
  />
</template>

<script>
// DrumControlContainer.vue - Handles logic
export default {
  name: 'DrumControlContainer',
  setup() {
    const drums = ref([])
    const selectedDrum = ref(null)
    const isLoading = ref(false)
    const error = ref(null)
    
    const loadDrums = async () => {
      isLoading.value = true
      try {
        drums.value = await apiService.getDrums()
      } catch (err) {
        error.value = err.message
      } finally {
        isLoading.value = false
      }
    }
    
    const handleSelectDrum = (drumId) => {
      selectedDrum.value = drumId
    }
    
    const handleStartDrum = async () => {
      try {
        await apiService.startDrum(selectedDrum.value)
        await loadDrums() // Refresh data
      } catch (err) {
        error.value = err.message
      }
    }
    
    onMounted(loadDrums)
    
    return {
      drums,
      selectedDrum,
      isLoading,
      error,
      handleSelectDrum,
      handleStartDrum,
      handleStopDrum
    }
  }
}
</script>
```

```vue
<!-- Presentational Component (Display) -->
<template>
  <div class="drum-control">
    <div v-if="isLoading" class="loading">Loading drums...</div>
    
    <div v-else-if="error" class="error">
      Error: {{ error }}
    </div>
    
    <div v-else class="drum-interface">
      <div class="drum-selector">
        <h3>Select Drum</h3>
        <button
          v-for="drum in drums"
          :key="drum.id"
          :class="{ active: selectedDrum === drum.id }"
          @click="$emit('selectDrum', drum.id)"
        >
          Drum {{ drum.id + 1 }}
        </button>
      </div>
      
      <div v-if="selectedDrum !== null" class="drum-actions">
        <button @click="$emit('startDrum')" class="start-btn">
          Start Drum
        </button>
        <button @click="$emit('stopDrum')" class="stop-btn">
          Stop Drum
        </button>
      </div>
    </div>
  </div>
</template>

<script>
// DrumControlPresentation.vue - Pure presentation
export default {
  name: 'DrumControlPresentation',
  props: {
    drums: { type: Array, required: true },
    selectedDrum: { type: Number, default: null },
    isLoading: { type: Boolean, default: false },
    error: { type: String, default: null }
  },
  emits: ['selectDrum', 'startDrum', 'stopDrum']
}
</script>
```

### 2. **Higher-Order Component (HOC) Pattern**

Add functionality to existing components.

```javascript
// withErrorHandling.js - HOC for error handling
export function withErrorHandling(WrappedComponent) {
  return {
    name: `WithErrorHandling(${WrappedComponent.name})`,
    setup(props, context) {
      const error = ref(null)
      const isLoading = ref(false)
      
      const handleError = (err) => {
        error.value = err.message || 'An error occurred'
        console.error('Component error:', err)
      }
      
      const clearError = () => {
        error.value = null
      }
      
      const withErrorWrapper = async (asyncFn) => {
        try {
          isLoading.value = true
          error.value = null
          return await asyncFn()
        } catch (err) {
          handleError(err)
          throw err
        } finally {
          isLoading.value = false
        }
      }
      
      return () => h(WrappedComponent, {
        ...props,
        error: error.value,
        isLoading: isLoading.value,
        onError: handleError,
        onClearError: clearError,
        withErrorWrapper
      })
    }
  }
}

// Usage:
const DrumControlWithErrors = withErrorHandling(DrumControl)
```

### 3. **Render Props Pattern**

Share functionality through function props.

```vue
<!-- DataProvider.vue -->
<template>
  <div>
    <slot 
      :data="data" 
      :loading="loading" 
      :error="error" 
      :refresh="refresh"
    />
  </div>
</template>

<script>
export default {
  name: 'DataProvider',
  props: {
    url: { type: String, required: true },
    pollInterval: { type: Number, default: 0 }
  },
  setup(props) {
    const data = ref(null)
    const loading = ref(false)
    const error = ref(null)
    
    const fetchData = async () => {
      loading.value = true
      error.value = null
      
      try {
        const response = await fetch(props.url)
        data.value = await response.json()
      } catch (err) {
        error.value = err.message
      } finally {
        loading.value = false
      }
    }
    
    const refresh = () => fetchData()
    
    onMounted(fetchData)
    
    // Setup polling if interval specified
    if (props.pollInterval > 0) {
      const intervalId = setInterval(fetchData, props.pollInterval)
      onBeforeUnmount(() => clearInterval(intervalId))
    }
    
    return { data, loading, error, refresh }
  }
}
</script>
```

```vue
<!-- Usage -->
<template>
  <DataProvider url="/api/drums" :poll-interval="5000">
    <template #default="{ data, loading, error, refresh }">
      <div v-if="loading">Loading...</div>
      <div v-else-if="error">Error: {{ error }}</div>
      <div v-else>
        <DrumList :drums="data" />
        <button @click="refresh">Refresh</button>
      </div>
    </template>
  </DataProvider>
</template>
```

## State Management Patterns

### 1. **Flux/Redux Pattern with Pinia**

Unidirectional data flow for predictable state management.

```javascript
// stores/drumStore.js
import { defineStore } from 'pinia'

export const useDrumStore = defineStore('drum', {
  state: () => ({
    drums: [],
    selectedDrumId: null,
    loading: false,
    error: null
  }),
  
  getters: {
    selectedDrum: (state) => {
      return state.drums.find(drum => drum.id === state.selectedDrumId)
    },
    
    availableDrums: (state) => {
      return state.drums.filter(drum => drum.status !== 'error')
    },
    
    drumCount: (state) => state.drums.length
  },
  
  actions: {
    // Action: Load drums from API
    async loadDrums() {
      this.loading = true
      this.error = null
      
      try {
        const response = await apiService.getDrums()
        this.drums = response.data
      } catch (error) {
        this.error = error.message
      } finally {
        this.loading = false
      }
    },
    
    // Action: Select drum
    selectDrum(drumId) {
      if (this.drums.some(drum => drum.id === drumId)) {
        this.selectedDrumId = drumId
      }
    },
    
    // Action: Update drum status
    updateDrumStatus(drumId, status) {
      const drum = this.drums.find(d => d.id === drumId)
      if (drum) {
        drum.status = status
        drum.lastUpdated = new Date().toISOString()
      }
    },
    
    // Action: Start drum operation
    async startDrum(drumId) {
      const drum = this.drums.find(d => d.id === drumId)
      if (!drum) return
      
      try {
        drum.status = 'starting'
        await apiService.startDrum(drumId)
        drum.status = 'running'
      } catch (error) {
        drum.status = 'error'
        drum.error = error.message
        throw error
      }
    }
  }
})
```

### 2. **Command Pattern**

Encapsulate operations as objects.

```javascript
// commands/DrumCommands.js
export class DrumCommand {
  constructor(drumStore, drumId) {
    this.drumStore = drumStore
    this.drumId = drumId
    this.timestamp = new Date()
  }
  
  async execute() {
    throw new Error('Execute method must be implemented')
  }
  
  async undo() {
    throw new Error('Undo method must be implemented')
  }
}

export class StartDrumCommand extends DrumCommand {
  async execute() {
    this.previousStatus = this.drumStore.getDrumStatus(this.drumId)
    await this.drumStore.startDrum(this.drumId)
  }
  
  async undo() {
    await this.drumStore.setDrumStatus(this.drumId, this.previousStatus)
  }
}

export class StopDrumCommand extends DrumCommand {
  async execute() {
    this.previousStatus = this.drumStore.getDrumStatus(this.drumId)
    await this.drumStore.stopDrum(this.drumId)
  }
  
  async undo() {
    if (this.previousStatus === 'running') {
      await this.drumStore.startDrum(this.drumId)
    }
  }
}

// Command Manager
export class CommandManager {
  constructor() {
    this.history = []
    this.currentIndex = -1
  }
  
  async execute(command) {
    try {
      await command.execute()
      
      // Add to history (remove any commands after current index)
      this.history = this.history.slice(0, this.currentIndex + 1)
      this.history.push(command)
      this.currentIndex++
      
      return true
    } catch (error) {
      console.error('Command execution failed:', error)
      return false
    }
  }
  
  async undo() {
    if (this.currentIndex >= 0) {
      const command = this.history[this.currentIndex]
      try {
        await command.undo()
        this.currentIndex--
        return true
      } catch (error) {
        console.error('Command undo failed:', error)
        return false
      }
    }
    return false
  }
  
  async redo() {
    if (this.currentIndex < this.history.length - 1) {
      this.currentIndex++
      const command = this.history[this.currentIndex]
      try {
        await command.execute()
        return true
      } catch (error) {
        console.error('Command redo failed:', error)
        this.currentIndex--
        return false
      }
    }
    return false
  }
  
  canUndo() {
    return this.currentIndex >= 0
  }
  
  canRedo() {
    return this.currentIndex < this.history.length - 1
  }
}
```

### 3. **Observer Pattern**

Reactive event system for component communication.

```javascript
// EventBus.js - Observer pattern implementation
class EventBus {
  constructor() {
    this.events = new Map()
  }
  
  on(event, callback) {
    if (!this.events.has(event)) {
      this.events.set(event, new Set())
    }
    this.events.get(event).add(callback)
    
    // Return unsubscribe function
    return () => {
      this.off(event, callback)
    }
  }
  
  off(event, callback) {
    if (this.events.has(event)) {
      this.events.get(event).delete(callback)
    }
  }
  
  emit(event, ...args) {
    if (this.events.has(event)) {
      this.events.get(event).forEach(callback => {
        try {
          callback(...args)
        } catch (error) {
          console.error(`Error in event listener for ${event}:`, error)
        }
      })
    }
  }
  
  once(event, callback) {
    const unsubscribe = this.on(event, (...args) => {
      unsubscribe()
      callback(...args)
    })
    return unsubscribe
  }
  
  clear(event) {
    if (event) {
      this.events.delete(event)
    } else {
      this.events.clear()
    }
  }
}

// Global event bus
export const eventBus = new EventBus()

// Composable for event handling
export function useEventBus() {
  const subscriptions = ref(new Set())
  
  const subscribe = (event, callback) => {
    const unsubscribe = eventBus.on(event, callback)
    subscriptions.value.add(unsubscribe)
    return unsubscribe
  }
  
  const emit = (event, ...args) => {
    eventBus.emit(event, ...args)
  }
  
  // Cleanup on unmount
  onBeforeUnmount(() => {
    subscriptions.value.forEach(unsubscribe => unsubscribe())
    subscriptions.value.clear()
  })
  
  return { subscribe, emit }
}

// Usage in components
export default {
  setup() {
    const { subscribe, emit } = useEventBus()
    
    // Listen for drum status changes
    subscribe('drum:statusChanged', (drumId, status) => {
      console.log(`Drum ${drumId} status changed to ${status}`)
    })
    
    // Emit events
    const handleDrumStart = (drumId) => {
      emit('drum:statusChanged', drumId, 'starting')
    }
    
    return { handleDrumStart }
  }
}
```

## Performance Optimization Patterns

### 1. **Lazy Loading Pattern**

```javascript
// Lazy load components
const LazyDrumSettings = defineAsyncComponent({
  loader: () => import('./components/DrumSettings.vue'),
  loadingComponent: LoadingSpinner,
  errorComponent: ErrorComponent,
  delay: 200,
  timeout: 3000
})

// Lazy load routes
const routes = [
  {
    path: '/drums',
    component: () => import('./views/DrumsView.vue')
  },
  {
    path: '/settings',
    component: () => import('./views/SettingsView.vue')
  }
]

// Lazy load heavy computations
export function useLazyComputation(computeFn, dependencies) {
  const result = ref(null)
  const loading = ref(false)
  const error = ref(null)
  
  const compute = async () => {
    if (loading.value) return
    
    loading.value = true
    error.value = null
    
    try {
      result.value = await computeFn()
    } catch (err) {
      error.value = err.message
    } finally {
      loading.value = false
    }
  }
  
  // Only compute when dependencies change
  watchEffect(() => {
    const deps = dependencies()
    if (deps.some(dep => dep !== null && dep !== undefined)) {
      compute()
    }
  })
  
  return { result, loading, error, compute }
}
```

### 2. **Memoization Pattern**

```javascript
// Memoized expensive computations
function createMemoizedFunction(fn) {
  const cache = new Map()
  
  return function(...args) {
    const key = JSON.stringify(args)
    
    if (cache.has(key)) {
      return cache.get(key)
    }
    
    const result = fn.apply(this, args)
    cache.set(key, result)
    
    return result
  }
}

// Memoized computed properties
export function useMemoizedComputed(computeFn, keyFn) {
  const cache = new Map()
  
  return computed(() => {
    const key = keyFn()
    
    if (cache.has(key)) {
      return cache.get(key)
    }
    
    const result = computeFn()
    cache.set(key, result)
    
    return result
  })
}

// Usage
const expensiveComputed = useMemoizedComputed(
  () => {
    // Expensive computation
    return heavyDataProcessing(largeDataSet.value)
  },
  () => `${largeDataSet.value.length}-${lastModified.value}`
)
```

### 3. **Virtual Scrolling Pattern**

```vue
<!-- VirtualList.vue -->
<template>
  <div 
    ref="containerRef"
    class="virtual-list" 
    @scroll="handleScroll"
    :style="{ height: containerHeight + 'px' }"
  >
    <div :style="{ height: totalHeight + 'px', position: 'relative' }">
      <div
        v-for="item in visibleItems"
        :key="item.index"
        :style="{
          position: 'absolute',
          top: item.top + 'px',
          height: itemHeight + 'px',
          width: '100%'
        }"
      >
        <slot :item="item.data" :index="item.index" />
      </div>
    </div>
  </div>
</template>

<script>
export default {
  name: 'VirtualList',
  props: {
    items: { type: Array, required: true },
    itemHeight: { type: Number, default: 50 },
    containerHeight: { type: Number, default: 400 },
    overscan: { type: Number, default: 5 }
  },
  setup(props) {
    const containerRef = ref(null)
    const scrollTop = ref(0)
    
    const totalHeight = computed(() => props.items.length * props.itemHeight)
    
    const visibleStartIndex = computed(() => {
      return Math.max(0, Math.floor(scrollTop.value / props.itemHeight) - props.overscan)
    })
    
    const visibleEndIndex = computed(() => {
      const endIndex = Math.min(
        props.items.length - 1,
        Math.ceil((scrollTop.value + props.containerHeight) / props.itemHeight) + props.overscan
      )
      return endIndex
    })
    
    const visibleItems = computed(() => {
      const items = []
      for (let i = visibleStartIndex.value; i <= visibleEndIndex.value; i++) {
        items.push({
          index: i,
          data: props.items[i],
          top: i * props.itemHeight
        })
      }
      return items
    })
    
    const handleScroll = (event) => {
      scrollTop.value = event.target.scrollTop
    }
    
    return {
      containerRef,
      totalHeight,
      visibleItems,
      handleScroll
    }
  }
}
</script>
```

## Error Handling Patterns

### 1. **Error Boundary Pattern**

```javascript
// ErrorBoundary.js
export function createErrorBoundary() {
  return {
    name: 'ErrorBoundary',
    data() {
      return {
        hasError: false,
        error: null,
        errorInfo: null
      }
    },
    
    errorCaptured(error, instance, info) {
      this.hasError = true
      this.error = error
      this.errorInfo = info
      
      // Log error to monitoring service
      console.error('Error caught by boundary:', error, info)
      
      // Prevent error from propagating
      return false
    },
    
    render() {
      if (this.hasError) {
        return this.$slots.fallback?.({
          error: this.error,
          errorInfo: this.errorInfo,
          retry: this.retry
        }) || h('div', 'Something went wrong')
      }
      
      return this.$slots.default?.()
    },
    
    methods: {
      retry() {
        this.hasError = false
        this.error = null
        this.errorInfo = null
      }
    }
  }
}

// Usage
const ErrorBoundary = createErrorBoundary()
```

```vue
<template>
  <ErrorBoundary>
    <template #default>
      <DrumControl />
      <LayerPreview />
    </template>
    
    <template #fallback="{ error, retry }">
      <div class="error-boundary">
        <h3>Oops! Something went wrong</h3>
        <p>{{ error.message }}</p>
        <button @click="retry">Try Again</button>
      </div>
    </template>
  </ErrorBoundary>
</template>
```

### 2. **Graceful Degradation Pattern**

```javascript
// Feature detection and fallbacks
export function useFeatureDetection() {
  const hasWebGL = ref(false)
  const hasWorkers = ref(false)
  const hasLocalStorage = ref(false)
  
  onMounted(() => {
    // Detect WebGL support
    try {
      const canvas = document.createElement('canvas')
      hasWebGL.value = !!(canvas.getContext('webgl') || canvas.getContext('experimental-webgl'))
    } catch (e) {
      hasWebGL.value = false
    }
    
    // Detect Web Workers
    hasWorkers.value = typeof Worker !== 'undefined'
    
    // Detect Local Storage
    try {
      localStorage.setItem('test', 'test')
      localStorage.removeItem('test')
      hasLocalStorage.value = true
    } catch (e) {
      hasLocalStorage.value = false
    }
  })
  
  return {
    hasWebGL,
    hasWorkers,
    hasLocalStorage
  }
}

// Graceful degradation in components
export default {
  setup() {
    const { hasWebGL, hasWorkers } = useFeatureDetection()
    
    const renderMethod = computed(() => {
      if (hasWebGL.value) return 'webgl'
      return 'canvas'
    })
    
    const processingMethod = computed(() => {
      if (hasWorkers.value) return 'worker'
      return 'main-thread'
    })
    
    return {
      renderMethod,
      processingMethod
    }
  }
}
```

## Testing Patterns for Vue.js Applications

### 1. **Component Testing Pattern**

```javascript
// tests/components/DrumControl.test.js
import { mount } from '@vue/test-utils'
import { createPinia, setActivePinia } from 'pinia'
import DrumControl from '@/components/DrumControl.vue'
import { useDrumModule } from '@/stores/modules/drumModule'

describe('DrumControl Component', () => {
  let wrapper
  let drumStore
  
  beforeEach(() => {
    // Setup Pinia for testing
    setActivePinia(createPinia())
    drumStore = useDrumModule()
    
    // Mock API service
    const mockApiService = {
      startDrum: vi.fn().mockResolvedValue({}),
      stopDrum: vi.fn().mockResolvedValue({}),
      getDrumStatus: vi.fn().mockResolvedValue({ status: 'idle' })
    }
    
    wrapper = mount(DrumControl, {
      props: {
        drumId: 0
      },
      global: {
        provide: {
          apiService: mockApiService
        }
      }
    })
  })
  
  afterEach(() => {
    wrapper.unmount()
  })
  
  it('should render drum control interface', () => {
    expect(wrapper.find('[data-testid="drum-control"]').exists()).toBe(true)
    expect(wrapper.find('[data-testid="start-button"]').exists()).toBe(true)
    expect(wrapper.find('[data-testid="stop-button"]').exists()).toBe(true)
  })
  
  it('should start drum when start button is clicked', async () => {
    const startButton = wrapper.find('[data-testid="start-button"]')
    
    await startButton.trigger('click')
    
    expect(wrapper.vm.apiService.startDrum).toHaveBeenCalledWith(0)
  })
  
  it('should disable buttons when drum is not connected', async () => {
    // Update drum status to disconnected
    drumStore.updateDrum(0, { connectionStatus: 'disconnected' })
    
    await wrapper.vm.$nextTick()
    
    const startButton = wrapper.find('[data-testid="start-button"]')
    expect(startButton.attributes('disabled')).toBeDefined()
  })
  
  it('should show loading state during operation', async () => {
    // Mock a delayed API response
    wrapper.vm.apiService.startDrum.mockImplementation(() => 
      new Promise(resolve => setTimeout(resolve, 100))
    )
    
    const startButton = wrapper.find('[data-testid="start-button"]')
    await startButton.trigger('click')
    
    // Check loading state
    expect(wrapper.find('[data-testid="loading-spinner"]').exists()).toBe(true)
    expect(startButton.text()).toContain('Starting...')
    
    // Wait for operation to complete
    await vi.waitFor(() => {
      expect(wrapper.find('[data-testid="loading-spinner"]').exists()).toBe(false)
    })
  })
})
```

### 2. **Store Testing Pattern**

```javascript
// tests/stores/drumModule.test.js
import { createPinia, setActivePinia } from 'pinia'
import { useDrumModule } from '@/stores/modules/drumModule'

// Mock API service
vi.mock('@/services/apiService', () => ({
  default: {
    getDrums: vi.fn(),
    startDrum: vi.fn(),
    stopDrum: vi.fn()
  }
}))

describe('Drum Store Module', () => {
  let store
  
  beforeEach(() => {
    setActivePinia(createPinia())
    store = useDrumModule()
  })
  
  describe('loadDrums action', () => {
    it('should load drums from API', async () => {
      const mockDrums = [
        { id: 0, name: 'Drum 0', status: 'idle' },
        { id: 1, name: 'Drum 1', status: 'running' }
      ]
      
      apiService.getDrums.mockResolvedValue(mockDrums)
      
      await store.loadDrums()
      
      expect(store.allDrums).toEqual(mockDrums)
      expect(store.loading).toBe(false)
    })
    
    it('should handle API errors', async () => {
      const error = new Error('Network error')
      apiService.getDrums.mockRejectedValue(error)
      
      await expect(store.loadDrums()).rejects.toThrow('Network error')
      expect(store.errors.get('loadDrums')).toBe('Network error')
    })
  })
  
  describe('computed properties', () => {
    beforeEach(() => {
      // Setup test data
      store.addDrum({ id: 0, connectionStatus: 'connected', status: 'idle' })
      store.addDrum({ id: 1, connectionStatus: 'connected', status: 'running' })
      store.addDrum({ id: 2, connectionStatus: 'disconnected', status: 'idle' })
    })
    
    it('should filter connected drums', () => {
      expect(store.connectedDrums).toHaveLength(2)
      expect(store.connectedDrums.every(d => d.connectionStatus === 'connected')).toBe(true)
    })
    
    it('should group drums by status', () => {
      const grouped = store.drumsByStatus
      
      expect(grouped.idle).toHaveLength(2)
      expect(grouped.running).toHaveLength(1)
    })
  })
})
```

### 3. **Composable Testing Pattern**

```javascript
// tests/composables/useRecoaterDrum.test.js
import { useRecoaterDrum } from '@/composables/useRecoaterDrum'

describe('useRecoaterDrum composable', () => {
  it('should load drum data on mount', async () => {
    const mockDrum = { id: 0, name: 'Test Drum', status: 'idle' }
    apiService.getDrum.mockResolvedValue({ data: mockDrum })
    
    const { drum, isLoading } = useRecoaterDrum(ref(0))
    
    // Wait for mount to complete
    await vi.waitFor(() => {
      expect(isLoading.value).toBe(false)
    })
    
    expect(drum.value).toEqual(mockDrum)
  })
  
  it('should start drum operation', async () => {
    const { startDrumOperation, status, canStartOperation } = useRecoaterDrum(ref(0))
    
    // Setup drum state
    drum.value = { id: 0, status: 'connected', hasValidFile: true }
    
    expect(canStartOperation.value).toBe(true)
    
    await startDrumOperation()
    
    expect(apiService.startDrum).toHaveBeenCalledWith(0)
    expect(status.value).toBe('running')
  })
})
```

## Key Design Principles Summary

### 1. **SOLID Principles in Vue.js**

**Single Responsibility**: Each component/composable has one clear purpose
```javascript
// ❌ Bad: Component doing too much
const UserDashboard = {
  // Handles user data, notifications, settings, analytics...
}

// ✅ Good: Focused components
const UserProfile = { /* Only user data */ }
const NotificationCenter = { /* Only notifications */ }
const UserSettings = { /* Only settings */ }
```

**Open/Closed**: Extend functionality without modifying existing code
```javascript
// Base composable
export function useDataFetcher(url) { /* ... */ }

// Extended without modifying original
export function useCachedDataFetcher(url) {
  const base = useDataFetcher(url)
  // Add caching logic
  return { ...base, cache }
}
```

**Liskov Substitution**: Components should be interchangeable
```javascript
// Any notification component should work the same way
const showNotification = (type, message) => {
  const Component = notificationTypes[type] // All have same interface
  return Component
}
```

**Interface Segregation**: Don't depend on unused functionality
```javascript
// ❌ Bad: Large interface
interface DrumService {
  start(), stop(), configure(), analyze(), report()
}

// ✅ Good: Focused interfaces
interface DrumControl { start(), stop() }
interface DrumConfig { configure() }
interface DrumAnalytics { analyze(), report() }
```

**Dependency Inversion**: Depend on abstractions, not implementations
```javascript
// Component depends on abstraction (injected service)
const apiService = inject('apiService')
// Not directly on: import { HTTPClient } from './http'
```

### 2. **Vue.js Specific Best Practices**

**Composition over Inheritance**
```javascript
// ✅ Good: Compose functionality
export default {
  setup() {
    const { data, loading } = useAsyncData()
    const { errors, handleError } = useErrorHandling()
    const { cache, clearCache } = useCaching()
    
    return { data, loading, errors, handleError, cache, clearCache }
  }
}
```

**Reactive State Management**
```javascript
// ✅ Good: Reactive state patterns
const state = reactive({
  drums: new Map(),
  selectedId: null,
  // Computed properties automatically update
  selectedDrum: computed(() => state.drums.get(state.selectedId))
})
```

**Event-Driven Architecture**
```javascript
// ✅ Good: Loose coupling through events
emitter.emit('drum:statusChanged', { drumId: 0, status: 'running' })
// Rather than direct method calls between components
```

### 3. **Industrial Application Patterns**

**Safety-First Design**
```javascript
// Always validate critical operations
const startDrum = async (drumId) => {
  // Multiple safety checks
  if (!isDrumConnected(drumId)) throw new Error('Drum not connected')
  if (!hasValidFile(drumId)) throw new Error('No valid file loaded')
  if (isEmergencyStopActive()) throw new Error('Emergency stop active')
  
  // Proceed with operation
  await apiService.startDrum(drumId)
}
```

**Graceful Degradation**
```javascript
// Provide fallbacks for critical functionality
const getPreview = async (drumId, layer) => {
  try {
    return await getHighQualityPreview(drumId, layer)
  } catch (error) {
    console.warn('High quality preview failed, using fallback')
    return await getBasicPreview(drumId, layer)
  }
}
```

**Real-time Monitoring**
```javascript
// Continuous system health monitoring
const useSystemHealth = () => {
  const health = ref({
    drums: 'green',
    network: 'green', 
    memory: 'green'
  })
  
  // Monitor and update health status
  setInterval(checkSystemHealth, 1000)
  
  return { health }
}
```

## Architecture Decision Guidelines

### When to Use Each Pattern

**Composables**: For reusable logic across components
- Data fetching and caching
- Form validation
- Device communication
- Error handling

**Pinia Stores**: For shared application state
- User authentication
- Application settings
- Real-time data (drum statuses)
- Complex business logic

**Provider/Inject**: For dependency injection
- Services (API, logging, notifications)
- Configuration objects
- Third-party integrations

**Event Bus**: For loose component coupling
- Cross-component communication
- System-wide notifications
- Workflow coordination

**Command Pattern**: For complex operations
- Undo/redo functionality
- Batch operations
- Operation logging and replay

## Performance Considerations

### Pattern Performance Impact

**Composition API**: Excellent performance due to fine-grained reactivity
**Store Patterns**: Good performance with proper structure
**Event Patterns**: Minimal overhead with proper cleanup
**Command Patterns**: Slight overhead but great for complex apps

### Memory Management

```javascript
// Always clean up resources
onBeforeUnmount(() => {
  // Clear intervals/timeouts
  clearInterval(intervalId)
  
  // Remove event listeners
  eventBus.off('event', handler)
  
  // Cleanup object URLs
  if (imageUrl.value) {
    URL.revokeObjectURL(imageUrl.value)
  }
  
  // Cancel pending requests
  abortController.abort()
})
```

## Key Takeaways

1. **Design Patterns** provide proven solutions to common problems
2. **SOLID Principles** guide architectural decisions for maintainable code
3. **Vue.js Patterns** leverage the framework's reactive system effectively
4. **Composition API** enables powerful composition and reuse patterns
5. **Testing Patterns** ensure code quality and reliability
6. **Industrial Patterns** prioritize safety and system reliability

## Best Practices Summary

### Component Architecture
- Keep components focused and single-purpose
- Use composition over inheritance
- Design clear interfaces (props/events)
- Implement proper error boundaries

### State Management
- Choose the right state solution for each use case
- Keep state mutations predictable
- Use computed properties for derived state
- Implement proper error handling

### Performance & Scalability
- Use appropriate patterns for your scale
- Monitor and optimize critical paths
- Implement proper resource cleanup
- Design for growth and change

### Code Quality
- Write testable code from the start
- Use TypeScript for better tooling
- Follow consistent naming conventions
- Document complex patterns and decisions

## Moving Forward

These design patterns form the foundation of professional Vue.js development. As you build more complex applications:

1. **Start Simple**: Begin with basic patterns and evolve as needed
2. **Measure Impact**: Monitor how patterns affect performance and maintainability
3. **Stay Consistent**: Establish team conventions and stick to them
4. **Keep Learning**: Patterns evolve with the ecosystem

**Remember**: Patterns are tools, not rules. Choose the right pattern for your specific problem, team, and constraints.

---

**🎉 Congratulations!** You've completed the comprehensive Frontend Textbook. You now have the knowledge to build professional, maintainable, and scalable Vue.js applications for industrial and enterprise environments.

*Master these patterns, and you'll be well-equipped to tackle any frontend challenge in your career!*