<!DOCTYPE html><html><head>
      <title>OperatorManual</title>
      <meta charset="utf-8">
      <meta name="viewport" content="width=device-width, initial-scale=1.0">
      
      <link rel="stylesheet" href="file:///c:\Users\<USER>\.vscode\extensions\shd101wyy.markdown-preview-enhanced-0.8.19\crossnote\dependencies\katex\katex.min.css">
      
      
      
      
      
      <style>
      code[class*=language-],pre[class*=language-]{color:#333;background:0 0;font-family:<PERSON><PERSON><PERSON>,"Liberation Mono",Menlo,Courier,monospace;text-align:left;white-space:pre;word-spacing:normal;word-break:normal;word-wrap:normal;line-height:1.4;-moz-tab-size:8;-o-tab-size:8;tab-size:8;-webkit-hyphens:none;-moz-hyphens:none;-ms-hyphens:none;hyphens:none}pre[class*=language-]{padding:.8em;overflow:auto;border-radius:3px;background:#f5f5f5}:not(pre)>code[class*=language-]{padding:.1em;border-radius:.3em;white-space:normal;background:#f5f5f5}.token.blockquote,.token.comment{color:#969896}.token.cdata{color:#183691}.token.doctype,.token.macro.property,.token.punctuation,.token.variable{color:#333}.token.builtin,.token.important,.token.keyword,.token.operator,.token.rule{color:#a71d5d}.token.attr-value,.token.regex,.token.string,.token.url{color:#183691}.token.atrule,.token.boolean,.token.code,.token.command,.token.constant,.token.entity,.token.number,.token.property,.token.symbol{color:#0086b3}.token.prolog,.token.selector,.token.tag{color:#63a35c}.token.attr-name,.token.class,.token.class-name,.token.function,.token.id,.token.namespace,.token.pseudo-class,.token.pseudo-element,.token.url-reference .token.variable{color:#795da3}.token.entity{cursor:help}.token.title,.token.title .token.punctuation{font-weight:700;color:#1d3e81}.token.list{color:#ed6a43}.token.inserted{background-color:#eaffea;color:#55a532}.token.deleted{background-color:#ffecec;color:#bd2c00}.token.bold{font-weight:700}.token.italic{font-style:italic}.language-json .token.property{color:#183691}.language-markup .token.tag .token.punctuation{color:#333}.language-css .token.function,code.language-css{color:#0086b3}.language-yaml .token.atrule{color:#63a35c}code.language-yaml{color:#183691}.language-ruby .token.function{color:#333}.language-markdown .token.url{color:#795da3}.language-makefile .token.symbol{color:#795da3}.language-makefile .token.variable{color:#183691}.language-makefile .token.builtin{color:#0086b3}.language-bash .token.keyword{color:#0086b3}pre[data-line]{position:relative;padding:1em 0 1em 3em}pre[data-line] .line-highlight-wrapper{position:absolute;top:0;left:0;background-color:transparent;display:block;width:100%}pre[data-line] .line-highlight{position:absolute;left:0;right:0;padding:inherit 0;margin-top:1em;background:hsla(24,20%,50%,.08);background:linear-gradient(to right,hsla(24,20%,50%,.1) 70%,hsla(24,20%,50%,0));pointer-events:none;line-height:inherit;white-space:pre}pre[data-line] .line-highlight:before,pre[data-line] .line-highlight[data-end]:after{content:attr(data-start);position:absolute;top:.4em;left:.6em;min-width:1em;padding:0 .5em;background-color:hsla(24,20%,50%,.4);color:#f4f1ef;font:bold 65%/1.5 sans-serif;text-align:center;vertical-align:.3em;border-radius:999px;text-shadow:none;box-shadow:0 1px #fff}pre[data-line] .line-highlight[data-end]:after{content:attr(data-end);top:auto;bottom:.4em}html body{font-family:'Helvetica Neue',Helvetica,'Segoe UI',Arial,freesans,sans-serif;font-size:16px;line-height:1.6;color:#333;background-color:#fff;overflow:initial;box-sizing:border-box;word-wrap:break-word}html body>:first-child{margin-top:0}html body h1,html body h2,html body h3,html body h4,html body h5,html body h6{line-height:1.2;margin-top:1em;margin-bottom:16px;color:#000}html body h1{font-size:2.25em;font-weight:300;padding-bottom:.3em}html body h2{font-size:1.75em;font-weight:400;padding-bottom:.3em}html body h3{font-size:1.5em;font-weight:500}html body h4{font-size:1.25em;font-weight:600}html body h5{font-size:1.1em;font-weight:600}html body h6{font-size:1em;font-weight:600}html body h1,html body h2,html body h3,html body h4,html body h5{font-weight:600}html body h5{font-size:1em}html body h6{color:#5c5c5c}html body strong{color:#000}html body del{color:#5c5c5c}html body a:not([href]){color:inherit;text-decoration:none}html body a{color:#08c;text-decoration:none}html body a:hover{color:#00a3f5;text-decoration:none}html body img{max-width:100%}html body>p{margin-top:0;margin-bottom:16px;word-wrap:break-word}html body>ol,html body>ul{margin-bottom:16px}html body ol,html body ul{padding-left:2em}html body ol.no-list,html body ul.no-list{padding:0;list-style-type:none}html body ol ol,html body ol ul,html body ul ol,html body ul ul{margin-top:0;margin-bottom:0}html body li{margin-bottom:0}html body li.task-list-item{list-style:none}html body li>p{margin-top:0;margin-bottom:0}html body .task-list-item-checkbox{margin:0 .2em .25em -1.8em;vertical-align:middle}html body .task-list-item-checkbox:hover{cursor:pointer}html body blockquote{margin:16px 0;font-size:inherit;padding:0 15px;color:#5c5c5c;background-color:#f0f0f0;border-left:4px solid #d6d6d6}html body blockquote>:first-child{margin-top:0}html body blockquote>:last-child{margin-bottom:0}html body hr{height:4px;margin:32px 0;background-color:#d6d6d6;border:0 none}html body table{margin:10px 0 15px 0;border-collapse:collapse;border-spacing:0;display:block;width:100%;overflow:auto;word-break:normal;word-break:keep-all}html body table th{font-weight:700;color:#000}html body table td,html body table th{border:1px solid #d6d6d6;padding:6px 13px}html body dl{padding:0}html body dl dt{padding:0;margin-top:16px;font-size:1em;font-style:italic;font-weight:700}html body dl dd{padding:0 16px;margin-bottom:16px}html body code{font-family:Menlo,Monaco,Consolas,'Courier New',monospace;font-size:.85em;color:#000;background-color:#f0f0f0;border-radius:3px;padding:.2em 0}html body code::after,html body code::before{letter-spacing:-.2em;content:'\00a0'}html body pre>code{padding:0;margin:0;word-break:normal;white-space:pre;background:0 0;border:0}html body .highlight{margin-bottom:16px}html body .highlight pre,html body pre{padding:1em;overflow:auto;line-height:1.45;border:#d6d6d6;border-radius:3px}html body .highlight pre{margin-bottom:0;word-break:normal}html body pre code,html body pre tt{display:inline;max-width:initial;padding:0;margin:0;overflow:initial;line-height:inherit;word-wrap:normal;background-color:transparent;border:0}html body pre code:after,html body pre code:before,html body pre tt:after,html body pre tt:before{content:normal}html body blockquote,html body dl,html body ol,html body p,html body pre,html body ul{margin-top:0;margin-bottom:16px}html body kbd{color:#000;border:1px solid #d6d6d6;border-bottom:2px solid #c7c7c7;padding:2px 4px;background-color:#f0f0f0;border-radius:3px}@media print{html body{background-color:#fff}html body h1,html body h2,html body h3,html body h4,html body h5,html body h6{color:#000;page-break-after:avoid}html body blockquote{color:#5c5c5c}html body pre{page-break-inside:avoid}html body table{display:table}html body img{display:block;max-width:100%;max-height:100%}html body code,html body pre{word-wrap:break-word;white-space:pre}}.markdown-preview{width:100%;height:100%;box-sizing:border-box}.markdown-preview ul{list-style:disc}.markdown-preview ul ul{list-style:circle}.markdown-preview ul ul ul{list-style:square}.markdown-preview ol{list-style:decimal}.markdown-preview ol ol,.markdown-preview ul ol{list-style-type:lower-roman}.markdown-preview ol ol ol,.markdown-preview ol ul ol,.markdown-preview ul ol ol,.markdown-preview ul ul ol{list-style-type:lower-alpha}.markdown-preview .newpage,.markdown-preview .pagebreak{page-break-before:always}.markdown-preview pre.line-numbers{position:relative;padding-left:3.8em;counter-reset:linenumber}.markdown-preview pre.line-numbers>code{position:relative}.markdown-preview pre.line-numbers .line-numbers-rows{position:absolute;pointer-events:none;top:1em;font-size:100%;left:0;width:3em;letter-spacing:-1px;border-right:1px solid #999;-webkit-user-select:none;-moz-user-select:none;-ms-user-select:none;user-select:none}.markdown-preview pre.line-numbers .line-numbers-rows>span{pointer-events:none;display:block;counter-increment:linenumber}.markdown-preview pre.line-numbers .line-numbers-rows>span:before{content:counter(linenumber);color:#999;display:block;padding-right:.8em;text-align:right}.markdown-preview .mathjax-exps .MathJax_Display{text-align:center!important}.markdown-preview:not([data-for=preview]) .code-chunk .code-chunk-btn-group{display:none}.markdown-preview:not([data-for=preview]) .code-chunk .status{display:none}.markdown-preview:not([data-for=preview]) .code-chunk .output-div{margin-bottom:16px}.markdown-preview .md-toc{padding:0}.markdown-preview .md-toc .md-toc-link-wrapper .md-toc-link{display:inline;padding:.25rem 0}.markdown-preview .md-toc .md-toc-link-wrapper .md-toc-link div,.markdown-preview .md-toc .md-toc-link-wrapper .md-toc-link p{display:inline}.markdown-preview .md-toc .md-toc-link-wrapper.highlighted .md-toc-link{font-weight:800}.scrollbar-style::-webkit-scrollbar{width:8px}.scrollbar-style::-webkit-scrollbar-track{border-radius:10px;background-color:transparent}.scrollbar-style::-webkit-scrollbar-thumb{border-radius:5px;background-color:rgba(150,150,150,.66);border:4px solid rgba(150,150,150,.66);background-clip:content-box}html body[for=html-export]:not([data-presentation-mode]){position:relative;width:100%;height:100%;top:0;left:0;margin:0;padding:0;overflow:auto}html body[for=html-export]:not([data-presentation-mode]) .markdown-preview{position:relative;top:0;min-height:100vh}@media screen and (min-width:914px){html body[for=html-export]:not([data-presentation-mode]) .markdown-preview{padding:2em calc(50% - 457px + 2em)}}@media screen and (max-width:914px){html body[for=html-export]:not([data-presentation-mode]) .markdown-preview{padding:2em}}@media screen and (max-width:450px){html body[for=html-export]:not([data-presentation-mode]) .markdown-preview{font-size:14px!important;padding:1em}}@media print{html body[for=html-export]:not([data-presentation-mode]) #sidebar-toc-btn{display:none}}html body[for=html-export]:not([data-presentation-mode]) #sidebar-toc-btn{position:fixed;bottom:8px;left:8px;font-size:28px;cursor:pointer;color:inherit;z-index:99;width:32px;text-align:center;opacity:.4}html body[for=html-export]:not([data-presentation-mode])[html-show-sidebar-toc] #sidebar-toc-btn{opacity:1}html body[for=html-export]:not([data-presentation-mode])[html-show-sidebar-toc] .md-sidebar-toc{position:fixed;top:0;left:0;width:300px;height:100%;padding:32px 0 48px 0;font-size:14px;box-shadow:0 0 4px rgba(150,150,150,.33);box-sizing:border-box;overflow:auto;background-color:inherit}html body[for=html-export]:not([data-presentation-mode])[html-show-sidebar-toc] .md-sidebar-toc::-webkit-scrollbar{width:8px}html body[for=html-export]:not([data-presentation-mode])[html-show-sidebar-toc] .md-sidebar-toc::-webkit-scrollbar-track{border-radius:10px;background-color:transparent}html body[for=html-export]:not([data-presentation-mode])[html-show-sidebar-toc] .md-sidebar-toc::-webkit-scrollbar-thumb{border-radius:5px;background-color:rgba(150,150,150,.66);border:4px solid rgba(150,150,150,.66);background-clip:content-box}html body[for=html-export]:not([data-presentation-mode])[html-show-sidebar-toc] .md-sidebar-toc a{text-decoration:none}html body[for=html-export]:not([data-presentation-mode])[html-show-sidebar-toc] .md-sidebar-toc .md-toc{padding:0 16px}html body[for=html-export]:not([data-presentation-mode])[html-show-sidebar-toc] .md-sidebar-toc .md-toc .md-toc-link-wrapper .md-toc-link{display:inline;padding:.25rem 0}html body[for=html-export]:not([data-presentation-mode])[html-show-sidebar-toc] .md-sidebar-toc .md-toc .md-toc-link-wrapper .md-toc-link div,html body[for=html-export]:not([data-presentation-mode])[html-show-sidebar-toc] .md-sidebar-toc .md-toc .md-toc-link-wrapper .md-toc-link p{display:inline}html body[for=html-export]:not([data-presentation-mode])[html-show-sidebar-toc] .md-sidebar-toc .md-toc .md-toc-link-wrapper.highlighted .md-toc-link{font-weight:800}html body[for=html-export]:not([data-presentation-mode])[html-show-sidebar-toc] .markdown-preview{left:300px;width:calc(100% - 300px);padding:2em calc(50% - 457px - 300px / 2);margin:0;box-sizing:border-box}@media screen and (max-width:1274px){html body[for=html-export]:not([data-presentation-mode])[html-show-sidebar-toc] .markdown-preview{padding:2em}}@media screen and (max-width:450px){html body[for=html-export]:not([data-presentation-mode])[html-show-sidebar-toc] .markdown-preview{width:100%}}html body[for=html-export]:not([data-presentation-mode]):not([html-show-sidebar-toc]) .markdown-preview{left:50%;transform:translateX(-50%)}html body[for=html-export]:not([data-presentation-mode]):not([html-show-sidebar-toc]) .md-sidebar-toc{display:none}
/* Please visit the URL below for more information: */
/*   https://shd101wyy.github.io/markdown-preview-enhanced/#/customize-css */

      </style>
      <!-- The content below will be included at the end of the <head> element. --><script type="text/javascript">
  document.addEventListener("DOMContentLoaded", function () {
    // your code here
  });
</script></head><body for="html-export">
    
    
      <div class="crossnote markdown-preview  ">
      
<h1 id="recoater-hmi---operators-guide">Recoater HMI - Operator's Guide </h1>
<p><strong>Version 2.1</strong></p>
<h2 id="table-of-contents">Table of Contents </h2>
<ol>
<li><a href="#1-introduction">Introduction</a></li>
<li><a href="#2-the-main-interface">The Main Interface</a>
<ul>
<li>2.1. <a href="#21-the-connection-status-indicator">The Connection Status Indicator</a></li>
</ul>
</li>
<li><a href="#3-status-view-home-screen">Status View (Home Screen)</a></li>
<li><a href="#4-recoater-dashboard">Recoater Dashboard</a>
<ul>
<li>4.1. <a href="#41-drum-controls">Drum Controls</a></li>
<li>4.2. <a href="#42-hopper-controls-scraping-blades">Hopper Controls (Scraping Blades)</a></li>
<li>4.3. <a href="#43-leveler-control">Leveler Control</a></li>
</ul>
</li>
<li><a href="#5-print-control-view">Print Control View</a>
<ul>
<li>5.1. <a href="#51-layer-parameters">Layer Parameters</a></li>
<li>5.2. <a href="#52-layer-preview">Layer Preview</a></li>
<li>5.3. <a href="#53-file-management-drum-cache-upload">File Management (Drum Cache Upload)</a></li>
<li>5.4. <a href="#54-multi-layer-cli-file-workflow">Multi-Layer CLI File Workflow</a></li>
<li>5.5. <a href="#55-cli-layer-range-selection-batch-processing">CLI Layer Range Selection (Batch Processing)</a></li>
<li>5.6. <a href="#56-print-job-management">Print Job Management</a></li>
<li>5.7. <a href="#57-troubleshooting">Troubleshooting</a></li>
</ul>
</li>
<li><a href="#6-upload-single-layer-workflow">Upload Single Layer Workflow</a></li>
<li><a href="#7-configuration-view">Configuration View</a></li>
<li><a href="#8-debug-view---direct-hardware-control">Debug View - Direct Hardware Control</a>
<ul>
<li>8.1. <a href="#81-purpose-and-use-cases">Purpose and Use Cases</a></li>
<li>8.2. <a href="#82-key-differences-from-automated-workflow">Key Differences from Automated Workflow</a></li>
<li>8.3. <a href="#83-interface-components">Interface Components</a></li>
<li>8.4. <a href="#84-transitioning-between-manual-and-automated-operations">Transitioning Between Manual and Automated Operations</a></li>
</ul>
</li>
<li><a href="#9-technical-specifications">Technical Specifications</a></li>
</ol>
<hr>
<h2 id="1-introduction">1. Introduction </h2>
<p>Welcome to the Recoater HMI (Human-Machine Interface). This guide provides comprehensive instructions for new operators to effectively control and monitor the Aerosint SPD Recoater using this web-based application.</p>
<p>This guide is structured to follow the layout of the application, with sections corresponding to each of the main views available in the navigation sidebar.</p>
<hr>
<h2 id="2-the-main-interface">2. The Main Interface </h2>
<p><img src="images/main-interface.png" alt="Main Interface"></p>
<p>The HMI is composed of three primary areas:</p>
<ol>
<li><strong>Header:</strong> Located at the top, it displays the application title and the <strong>Connection Status Indicator</strong>.</li>
<li><strong>Navigation Sidebar:</strong> On the left, this bar allows you to switch between the HMI's main functional views.</li>
<li><strong>Content Area:</strong> The central part of the screen where the controls and information for the selected view are displayed.</li>
</ol>
<h3 id="21-the-connection-status-indicator">2.1. The Connection Status Indicator </h3>
<p>This indicator, located in the top-right corner, is the most critical element for understanding the system's state. <strong>Always check its status before attempting any operation.</strong></p>
<p><img src="images/connection-status.png" alt="Connection Status Indicator"></p>
<ul>
<li><span style="color: #27ae60;"><strong>● Connected</strong></span>: The HMI is successfully communicating with the recoater hardware. The system is operational.</li>
<li><span style="color: #f39c12;"><strong>● Error</strong></span>: The HMI is connected, but the recoater hardware is reporting an error. You may need to check the hardware for issues.</li>
<li><span style="color: #e74c3c;"><strong>● Disconnected</strong></span>: The HMI cannot communicate with the recoater backend. This could be due to a network issue, or the backend service or recoater hardware not being turned on.</li>
</ul>
<hr>
<h2 id="3-status-view-home-screen">3. Status View (Home Screen) </h2>
<p>This is the default view when you first load the application. It provides a high-level summary of the system's connection status.</p>
<p><img src="images/status-view.png" alt="Status View"></p>
<ul>
<li><strong>Backend Status:</strong> Shows if your browser is connected to the HMI's backend software.</li>
<li><strong>Recoater Status:</strong> Shows if the HMI's backend is successfully connected to the physical recoater hardware.</li>
<li><strong>Last Update:</strong> Timestamps the last successful communication.</li>
<li><strong>Error Information:</strong> If an error is present, this card will appear with a description of the problem.</li>
</ul>
<p>The <strong>Refresh Status</strong> button allows you to manually request an immediate update from the system.</p>
<hr>
<h2 id="4-recoater-dashboard">4. Recoater Dashboard </h2>
<p><img src="images/recoater-dashboard.png" alt="Recoater Dashboard"></p>
<p>This is the primary dashboard for controlling the individual components of the recoater head. It is divided into sections for Drums, Hoppers (scraping blades), and the Leveler.</p>
<h3 id="41-drum-controls">4.1. Drum Controls </h3>
<p>This section contains a control card for each of the three drums (Drum 0, Drum 1, Drum 2). Each card allows you to monitor and control its associated drum.</p>
<p><img src="images/drum-status.png" alt="Drum Status"></p>
<p><strong>Status Display:</strong></p>
<ul>
<li><strong>Position:</strong> The current rotational position of the drum in millimeters (mm).</li>
<li><strong>Circumference:</strong> The total circumference of the drum in millimeters (mm).</li>
<li><strong>Running/Stopped:</strong> A status light indicates if the drum is currently in motion.</li>
</ul>
<p><strong>Motion Control:</strong><br>
You can command the drum to move using several modes. The <code>Start Motion</code> button is disabled if the drum is already running.</p>
<p><img src="images/drum-motion.png" alt="Drum Motion"></p>
<ul>
<li><strong>Mode:</strong>
<ul>
<li><code>Relative</code>: Moves the drum by the specified <code>Distance (mm)</code>.</li>
<li><code>Absolute</code>: Moves the drum to a specific absolute <code>Position (mm)</code>.</li>
<li><code>Turns</code>: Rotates the drum by the specified number of <code>Turns</code>.</li>
<li><code>Speed</code>: Rotates the drum continuously at the specified <code>Speed (mm/s)</code>.</li>
<li><code>Homing</code>: Returns the drum to its zero-reference position.</li>
</ul>
</li>
<li><strong>Speed (mm/s):</strong> The speed of the movement.</li>
<li><strong>Buttons:</strong>
<ul>
<li><code>Start Motion</code>: Initiates the configured movement.</li>
<li><code>Cancel Motion</code>: Immediately stops any ongoing drum movement. This is only enabled when the drum is running.</li>
</ul>
</li>
</ul>
<p><strong>Pressure Controls:</strong></p>
<p><img src="images/drum-pressure.png" alt="Drum Pressure"></p>
<ul>
<li><strong>Ejection Pressure:</strong>
<ul>
<li>Displays the current and target pressure.</li>
<li>Set a new <code>Target</code> pressure and select the <code>Unit</code> (<code>Pa</code> for Pascals, <code>bar</code> for Bar). Click <code>Set</code> to apply.</li>
</ul>
</li>
<li><strong>Suction Pressure:</strong>
<ul>
<li>Displays the current and target pressure in Pascals (Pa).</li>
<li>Set a new <code>Target</code> pressure. Click <code>Set</code> to apply.</li>
</ul>
</li>
</ul>
<h3 id="42-hopper-controls-scraping-blades">4.2. Hopper Controls (Scraping Blades) </h3>
<p>This section contains a control card for each drum's hopper and scraping blade assembly.</p>
<blockquote>
<p><strong>CRITICAL SAFETY WARNING - SCRAPING BLADE OPERATION</strong></p>
<p><strong>The motion of the scraping blade can damage the drum!</strong></p>
<p><strong>Before operating scraping blades:</strong></p>
<ul>
<li><strong>Make sure the drum is not rotating</strong> when a motion of the scraping blade is performed</li>
<li><strong>Make sure, at all times, that the scraping blade is not in contact with the drum.</strong> This can be checked by sliding a 50µm feeler gauge between the mesh and the scraping blade</li>
</ul>
<p><strong>If the scraping blade gets mechanically stuck:</strong></p>
<ul>
<li>This can happen when one side of the scraping blade is considerably higher or lower than the other side</li>
<li>The blade will appear visually sideways</li>
<li><strong>To unlock it:</strong> Move one side of the blade to make it horizontal again</li>
</ul>
</blockquote>
<p><img src="images/hopper-status.png" alt="Hopper Status"></p>
<p><strong>Status Display:</strong></p>
<ul>
<li><strong>Screw Positions:</strong> Shows the current position of each of the two blade screws in micrometers (µm).</li>
<li><strong>Running/Stopped:</strong> A status light indicates if any part of the blade assembly is in motion.</li>
</ul>
<p><img src="images/hopper-motion.png" alt="Hopper Motion"></p>
<p><strong>Collective Blade Motion:</strong><br>
Controls both blade screws together.</p>
<ul>
<li><strong>Mode:</strong>
<ul>
<li><code>Relative</code>: Moves both screws by the specified <code>Distance (µm)</code>.</li>
<li><code>Absolute</code>: Moves both screws to a specific <code>Position (µm)</code>.</li>
<li><code>Homing</code>: Returns both screws to their zero-reference position. The distance input is hidden in this mode.</li>
</ul>
</li>
<li><strong>Buttons:</strong> <code>Start Motion</code> and <code>Cancel Motion</code> function similarly to the drum controls.</li>
</ul>
<p><img src="images/hopper-screw.png" alt="Hopper Screw"></p>
<p><strong>Individual Screw Control:</strong><br>
Allows for fine-tuning of each screw.</p>
<ul>
<li>Enter a relative <code>Distance (µm)</code> for the specific screw.</li>
<li>Click <code>Move</code> to start the motion for that screw only.</li>
<li>Click <code>Stop</code> to cancel motion for that screw only.</li>
</ul>
<h3 id="43-leveler-control">4.3. Leveler Control </h3>
<p>This card manages the powder leveler.</p>
<p><img src="images/leveler.png" alt="Leveler"></p>
<ul>
<li><strong>Pressure Control:</strong> Displays the <code>Current</code>, <code>Target</code>, and <code>Maximum</code> allowed pressure in Pascals (Pa). You can input a new target and click <code>Set</code> to apply it.</li>
<li><strong>Magnetic Sensor:</strong> Displays the state of the leveler's magnetic sensor as either <code>Active (Field Detected)</code> or <code>Inactive</code>.</li>
</ul>
<hr>
<h2 id="5-print-control-view">5. Print Control View </h2>
<p>This view consolidates all functions related to preparing and executing a print job.</p>
<h3 id="51-layer-parameters">5.1. Layer Parameters </h3>
<p>These parameters define how the recoater will process the current layer.</p>
<p><img src="images/layer-parameters.png" alt="Layer Parameters"></p>
<ul>
<li><strong>Filling Drum ID:</strong> The ID of the drum (0, 1, or 2) containing the filling material. Set to <strong>-1</strong> for no filling.</li>
<li><strong>Patterning Speed (mm/s):</strong> The speed at which the recoater will operate.</li>
<li><strong>X Offset (mm):</strong> Horizontal X offset applied during deposition.</li>
<li><strong>Enable Powder Saving:</strong> Toggles the use of powder-saving strategies.</li>
</ul>
<p>Use the <code>Load Current</code> button to fetch the existing parameters from the hardware, and <code>Save Parameters</code> to apply your changes.</p>
<h3 id="52-layer-preview">5.2. Layer Preview </h3>
<p>This area shows a PNG image of what the layer will look like, with color-coded geometry to distinguish between different drums.</p>
<p><img src="images/layer-preview.png" alt="Layer Preview"></p>
<p><strong>Color Legend:</strong><br>
A color legend is displayed above the preview showing the color mapping for each drum:</p>
<ul>
<li>
<p><strong>Blue (#3498db)</strong>: Drum 0 geometry</p>
</li>
<li>
<p><strong>Orange (#e67e22)</strong>: Drum 1 geometry</p>
</li>
<li>
<p><strong>Green (#27ae60)</strong>: Drum 2 geometry</p>
</li>
<li>
<p><strong>Preview Source (Dropdown):</strong></p>
<ul>
<li><code>Current Layer Configuration</code>: Generates a color-coded preview showing how geometry would be distributed across drums. Each drum's geometry is rendered in its designated color.</li>
<li><code>Drum [0-2] Geometry</code>: Loads and displays the preview of the geometry file <em>currently stored</em> on the selected drum, rendered in that drum's specific color.</li>
</ul>
</li>
<li>
<p><strong>Layer Number (Dropdown):</strong></p>
<ul>
<li><code>Layers 1 to Unknown</code>: Automatically calculates the max layer upon upload of any CLI file, remains as 'unknown' if no files are cached. Allows for viewing of combined layers across all drums or any drum for desired layer.</li>
</ul>
</li>
<li>
<p><strong>Load Preview (Button):</strong> Generates and displays the image based on the selected source.</p>
</li>
</ul>
<h3 id="53-file-management-drum-cache-upload">5.3. File Management (Drum Cache Upload) </h3>
<p>This section allows you to upload whole CLI files to the backend cache for each drum, preparing them for automated multi-material job execution. This interface provides a streamlined way to cache geometry files for each drum in the multi-material printing workflow.</p>
<p><img src="images/file-management.png" alt="File Management"></p>
<p><strong>Interface Description:</strong><br>
The File Management section displays three identical columns, one for each drum (Drum 0, Drum 1, Drum 2), allowing independent file management for each drum.</p>
<h4 id="per-drum-file-upload">Per-Drum File Upload </h4>
<p>Each drum column contains:</p>
<ul>
<li>
<p><strong>Geometry File Selection:</strong></p>
<ul>
<li><code>Choose File</code> button to select a PNG or CLI file from your computer</li>
<li>Displays "No file chosen" when no file is selected</li>
<li>Shows selected filename once a file is chosen</li>
</ul>
</li>
<li>
<p><strong>Upload Status:</strong></p>
<ul>
<li><code>LAST UPLOADED:</code> section showing name of last uploaded file</li>
<li>Displays "No file uploaded" when no file is cached for that drum</li>
<li>Shows file information when a file has been successfully cached</li>
</ul>
</li>
<li>
<p><strong>Action Buttons:</strong></p>
<ul>
<li><code>Upload</code> button (grayed out until a file is selected) - Caches the selected file for this drum</li>
<li><code>Delete</code> button (red) - Removes the cached file for this drum</li>
</ul>
</li>
</ul>
<h4 id="important-notes">Important Notes </h4>
<p><strong>Cache-Based Workflow:</strong></p>
<ul>
<li>Files are uploaded to the HMI's backend cache, not directly to hardware</li>
<li>Cached files are used during automated multi-material job execution</li>
<li>Each drum maintains its own cached file independently</li>
</ul>
<p><strong>File Persistence:</strong></p>
<ul>
<li>Cached files remain available until manually deleted or system restart</li>
</ul>
<h3 id="54-multi-layer-cli-file-workflow">5.4. Multi-Layer CLI File Workflow </h3>
<p>This is an advanced feature for working with standard multi-layer CLI files (both ASCII and binary formats). It allows you to visualize individual layers and cache specific layer geometries in the drum system for automated multi-material printing.</p>
<p><strong>Integration with Multi-Material Jobs:</strong><br>
CLI files processed through this workflow are cached in the HMI's drum management system. Once cached, these files become available for automated multi-material job execution (Section 5.6). The system automatically uses cached drum data during layer-by-layer printing operations.</p>
<p><strong>Supported CLI Formats:</strong></p>
<ul>
<li><strong>Binary CLI</strong>: Compressed binary format (automatically detected by header)</li>
<li><strong>ASCII CLI</strong>: Text-based format with space-delimited or slash-delimited commands</li>
</ul>
<p><img src="images/cli-upload.png" alt="CLI Upload"></p>
<ul>
<li><strong>Step 1: Upload &amp; Parse</strong>
<ol>
<li>In the <strong>CLI Layer Preview</strong> section, use the <strong>"Choose File"</strong> input to select a multi-layer CLI file.</li>
<li>Click <strong>Upload &amp; Parse CLI</strong>.</li>
<li>The HMI automatically detects the file format and parses it in memory, displaying the <strong>File ID</strong> and <strong>Total Layers</strong> found.</li>
<li><strong>Note:</strong> This parsing step happens entirely on the HMI server for preview purposes only.</li>
<li><strong>Note:</strong> The system has a 5-minute timeout for CLI parsing. If the parsing takes longer than 5 minutes, the operation will be canceled. Split large files if needed.</li>
</ol>
</li>
</ul>
<p><img src="images/preview-cli-layer.png" alt="CLI Layer Preview"></p>
<ul>
<li><strong>Step 2: Preview Individual Layers</strong>
<ol>
<li>Once parsed, use the <strong>Layer Number</strong> input and click <strong>Preview Layer</strong>.</li>
<li>Only the selected layer is rendered and displayed as a PNG preview.</li>
<li><strong>Note:</strong> CLI layer previews use a different color scheme than the main layer preview. Individual CLI layers show polylines in black and hatch patterns in blue, as they represent raw geometry data rather than drum-specific assignments.</li>
</ol>
</li>
</ul>
<p><img src="images/send-cli-layer.png" alt="CLI Send to Drum"></p>
<ul>
<li><strong>Step 3: Cache Layer in Drum System</strong>
<ol>
<li>Select <strong>Layer Number</strong> and <strong>Target Drum</strong>.</li>
<li>Click <strong>Send Layer to Recoater</strong>.</li>
<li>The HMI extracts the 2D geometry data (polylines and hatches) from the selected layer and generates a new <strong>ASCII CLI file</strong> containing only that layer's geometry (regardless of the original file format). This ensures compatibility with the drum firmware which requires ASCII CLI format. The ASCII CLI file is then <strong>cached in the drum system</strong> (replacing any existing cached file on that drum).</li>
<li><strong>Note:</strong> The layer geometry is cached as ASCII CLI format for automated job processing, not sent directly to hardware. The PNG preview is only for visualization purposes. The full parsed CLI file remains in HMI/IPC memory (keyed by File ID) until cleared or the backend server restarts, and can be re-used for further previews or caching operations without re-uploading.</li>
<li><strong>Integration:</strong> Cached drum data becomes immediately available for multi-material job execution and will be automatically used during layer-by-layer printing operations.</li>
</ol>
</li>
</ul>
<h3 id="55-cli-layer-range-selection-batch-processing">5.5. CLI Layer Range Selection (Batch Processing) </h3>
<p>This advanced feature allows you to cache multiple consecutive layers from a parsed CLI file to a drum in a single operation, enabling efficient batch processing for multi-material manufacturing workflows.</p>
<p><strong>Integration with Multi-Material Jobs:</strong><br>
Layer ranges processed through this workflow are cached in the HMI's drum management system. The cached range becomes immediately available for automated multi-material job execution, where the system will automatically iterate through the cached layers during printing operations.</p>
<ul>
<li>
<p><strong>Prerequisites:</strong> You must first upload and parse a multi-layer CLI file using the steps in section 5.4.</p>
</li>
<li>
<p><strong>How Layer Range Processing Works:</strong></p>
<ul>
<li>The HMI extracts the specified layer range from the parsed CLI file</li>
<li>Multiple layers' geometry data (polylines and hatches) are combined into a single optimized <strong>ASCII CLI file</strong></li>
<li>This combined ASCII CLI file contains all the layers in the range with proper layer commands and Z-height information</li>
<li>The resulting file is <strong>cached in the drum system</strong>, replacing any existing cached geometry for that drum</li>
<li>Each drum can only store one cached geometry file at a time</li>
</ul>
</li>
</ul>
<p><strong>Generated CLI Format:</strong><br>
The system generates ASCII CLI files with a standard structure to ensure compatibility with drum firmware that requires ASCII CLI formatting.</p>
<ul>
<li><strong>Default Values:</strong> When a CLI file is uploaded, the End Layer Number automatically defaults to the total number of layers in the file for convenience.</li>
</ul>
<p><img src="images/send-cli-range.png" alt="CLI Layer Range"></p>
<ul>
<li>
<p><strong>Layer Range Selection:</strong></p>
<ol>
<li>In the <strong>CLI Layer Selection</strong> section, specify the <strong>Start Layer Number</strong> and <strong>End Layer Number</strong> (both 1-indexed).</li>
<li>Select the <strong>Target Drum</strong> (0, 1, or 2) where the layer range should be cached.</li>
<li>Ensure the range is valid: start layer ≤ end layer, and both within the total number of layers.</li>
<li>The interface validates your range and displays the number of layers to be processed.</li>
</ol>
</li>
<li>
<p><strong>Batch Cache Operation:</strong></p>
<ol>
<li>Click <strong>Send Layer Range</strong> to initiate the batch caching operation.</li>
<li>The system processes layers sequentially and provides progress feedback.</li>
<li>Upon completion, you'll receive confirmation that the range was cached successfully.</li>
<li><strong>Note:</strong> The original parsed CLI file remains available in memory for additional operations.</li>
<li><strong>Integration:</strong> The cached drum data is immediately available for multi-material job execution.</li>
</ol>
</li>
<li>
<p><strong>Important Considerations:</strong></p>
<ul>
<li><strong>Processing time</strong>: Layer range operations may take time for large ranges</li>
<li><strong>Cache limitation</strong>: Each drum overwrites its previous cached content when receiving a new layer range</li>
<li><strong>Memory efficiency</strong>: The original CLI file structure and metadata are preserved in cache until manually cleared or new CLI file uploaded</li>
<li><strong>Validation</strong>: The system validates layer numbers and drum availability before processing</li>
</ul>
</li>
</ul>
<h3 id="56-print-job-management">5.6. Print Job Management </h3>
<p>This section displays the current status and controls for managing multi-material print jobs. The interface provides real-time monitoring and control of automated layer-by-layer printing operations.</p>
<p><img src="images/print-job-management.png" alt="Print Job Management"></p>
<h4 id="print-job-status-display">Print Job Status Display </h4>
<p>The top section shows the current state of the print job system:</p>
<p><img src="images/print-job-state.png" alt="Print Job State"></p>
<ul>
<li>
<p><strong>Current State</strong>: Displays the overall system status:</p>
<ul>
<li><code>Ready</code>: System is prepared and available to start a new job</li>
<li><code>Printing</code>: A multi-material job is currently executing</li>
</ul>
</li>
<li>
<p><strong>Print Active</strong>: Shows whether a print job is currently running:</p>
<ul>
<li><code>Yes</code>: A job is actively processing layers</li>
<li><code>No</code>: No job is currently active</li>
</ul>
</li>
</ul>
<h4 id="print-job-controls">Print Job Controls </h4>
<ul>
<li>
<p><strong>Start Print Job</strong>: Initiates a new multi-material print job using cached CLI files from the drums. This button is only enabled when:</p>
<ul>
<li>The system status shows <code>Ready</code></li>
<li>At least one drum has cached CLI data</li>
<li>No other job is currently active</li>
</ul>
</li>
<li>
<p><strong>Cancel Print Job</strong>: Immediately stops the current print job. This button is only enabled during active printing and will:</p>
<ul>
<li>Require confirmation before proceeding</li>
<li>Stop all layer processing operations</li>
<li>Clear cached drum files</li>
<li>Reset the system to <code>Ready</code> state</li>
<li>Note: Error flags, if any, requires manual clearing by operator</li>
</ul>
</li>
<li>
<p><strong>Refresh Status</strong>: Manually updates the job status information by querying the hardware for the latest state</p>
</li>
</ul>
<h4 id="job-progress-section">Job Progress Section </h4>
<p>The bottom section shows the current progress of the print job:</p>
<p><img src="images/print-job-progress.png" alt="Print Job Progress"></p>
<ul>
<li><strong>Overall Progress</strong>: Shows the completion percentage and current layer status
<ul>
<li>Format: <code>Layer X of Y</code> with percentage completion</li>
<li>Progress bar provides visual indication of job completion</li>
<li>Real-time updates as layers are processed</li>
</ul>
</li>
</ul>
<h4 id="drum-status-display">Drum Status Display </h4>
<p>The drum status section shows the current state of each drum:</p>
<ul>
<li><strong>Drum 0, Drum 1, Drum 2</strong>: Individual status indicators for each drum showing:
<ul>
<li><code>File</code>: Name of file uploaded with layer range</li>
<li><code>Layers</code>: Total number of layers in the uploaded file</li>
</ul>
</li>
</ul>
<h4 id="no-active-job-state">No Active Job State </h4>
<p>When no job is running, the interface displays:</p>
<ul>
<li><strong>"No Active Job"</strong> message</li>
<li><strong>Instruction text</strong>: "Upload CLI files and start a multi-material job to see progress here"</li>
<li>This guides operators to use the CLI file workflows (Sections 5.4 and 5.5) before starting a job</li>
</ul>
<h4 id="error-handling-and-system-status">Error Handling and System Status </h4>
<p>When errors occur during job execution, the system provides comprehensive error information and recovery tools:</p>
<p><img src="images/critical-error-modal.png" alt="Critical System Error"></p>
<p><strong>Critical Error Pop-up</strong></p>
<p>When critical errors are detected, a pop-up appears with detailed information:</p>
<ul>
<li><strong>Error Type</strong>: Identifies if error is from the recoater HMI program on the IPC (backend error) or from the PLC within TwinCAT XAR (plc error)</li>
<li><strong>Error Details</strong>: Shows specific technical information about the error if identified via recoater HMI program. Does not specify plc errors.</li>
<li><strong>Impact Assessment</strong>: Explains how the error affects operations:
<ul>
<li>"All print operations have been paused"</li>
<li>"New jobs cannot be started until errors are resolved"</li>
<li>"Operator intervention is required"</li>
</ul>
</li>
</ul>
<p><strong>Required Actions for Error Recovery</strong></p>
<p>The modal provides a step-by-step recovery process:</p>
<ol>
<li><strong>Investigate the Error</strong>: Check system logs and hardware status to identify the root cause</li>
<li><strong>Resolve the Issue</strong>: Take appropriate corrective action based on error type and diagnostics</li>
<li><strong>Clear Error Flags</strong>: Use the "Clear Error Flags" button to reset the system state</li>
</ol>
<p><strong>Closing the Error Pop-up</strong></p>
<p>Operators have one of two possible paths forward:</p>
<p><img src="images/error-paths.png" alt="Error Paths"></p>
<ul>
<li><strong>Clear Error Flags</strong>: Immediately clears any error flagged on the recoater HMI and resumes printing. Recommended if obstacle is minor and easily removed.</li>
<li><strong>Acknowledge</strong>: Closes the pop-up and remain on print page with errors enabled. Operators can then decide to cancel the job or maintain the error message on screen for reference or to block off usage of the recoater. Error flags remain in-place indefinitely until manually cleared.</li>
</ul>
<p><strong>System Error Status Display</strong></p>
<p>Operator is returned back to the print page with the following view if "Acknowledge" was selected to close the error pop-up:</p>
<p><img src="images/system-error-status-display.png" alt="System Error Status Display"></p>
<ul>
<li>
<p><strong>System Error Status</strong>:</p>
<ul>
<li>Red indicator with "Errors Detected" when issues are present</li>
<li>Shows specific error types (Backend Error, PLC Error)</li>
<li>Error status remains "Active" until manually cleared</li>
</ul>
</li>
<li>
<p><strong>Clear Errors Button</strong>: Red button to reset system to error-free state and resume print jobs.</p>
</li>
</ul>
<p><strong>Recent Errors Log</strong></p>
<p>The system maintains a history of recent errors for troubleshooting:</p>
<ul>
<li><strong>Timestamp</strong>: Shows when each error occurred</li>
<li><strong>Error Source</strong>: Identifies whether error came from Backend, or Hardware</li>
<li><strong>Error Message</strong>: Displays the specific error description</li>
<li><strong>Frequency Indicator</strong>: Shows how many times the same error has occurred</li>
<li><strong>Clear History</strong>: Option to clear the error log for a fresh start</li>
</ul>
<p><strong>Coordination Status</strong></p>
<p>PLC programmers may also scroll down to the bottom of the print page to view the status of the OPC UA error variable exposed to the PLC:</p>
<p><img src="images/coordination-status-error.png" alt="Coordination Status Error"></p>
<p><strong>Important Notes</strong>:</p>
<ul>
<li>Clearing errors does not restart the entire job - it resumes from the current layer</li>
<li>Multiple error types may be active simultaneously and require individual attention</li>
<li>The system preserves job progress and drum cache during error recovery</li>
<li>Some errors may require hardware intervention before they can be cleared</li>
<li>Cancelling job does not reset error flags</li>
</ul>
<h3 id="57-troubleshooting">5.7. Troubleshooting </h3>
<p><strong>CLI File Issues:</strong></p>
<ul>
<li><strong>"Unrecognized directive" warnings</strong>: In rare cases, some CLI files may contain non-standard directives that are safely ignored during parsing.</li>
<li><strong>Upload failures</strong>: Check that the CLI file is not corrupted and is in ASCII or binary CLI format.</li>
<li><strong>Layer range errors</strong>: Ensure start layer ≤ end layer and both are within the total layer count.</li>
</ul>
<p><strong>Preview Issues:</strong></p>
<ul>
<li><strong>Color-coded previews</strong>: The main layer preview uses drum-specific colors (blue, orange, green) while CLI layer previews use black/blue coloring. This is normal and expected behavior.</li>
<li><strong>Preview generation failures</strong>: If color-coded previews fail to generate, the system will fall back to simple placeholder images.</li>
</ul>
<p><strong>Hardware Communication:</strong></p>
<ul>
<li><strong>Connection issues</strong>: Check the connection status indicator in the header before attempting operations.</li>
<li><strong>Upload timeouts</strong>: Large layer ranges or files may take time to process - wait for completion confirmation.</li>
</ul>
<hr>
<h2 id="6-sample-workflow">6. Sample Workflow </h2>
<ol>
<li>Navigate to Print Tab</li>
<li>Choose [your_cli_file].cli file in CLI Layer Preview</li>
<li>Press 'Upload &amp; Parse CLI'</li>
<li>In CLI layer preview, set Layer Number=2, Target Drum=Drum 1, then press 'Send Layer to Recoater'</li>
<li>Check that Drum 1 has a file with the correct layer uploaded under Job Progress &gt; Drum Status</li>
<li>Press 'Start Print Job'</li>
<li>Operator should see Layer 1 of 1 100%</li>
<li>Wait for the layer to finish printing</li>
<li>Upon job completion, overall Progress resets to layer 0 of 0 0%, Drums and cache are cleared and back to idle. Print Job Management automatically returns back to "Current State: Ready, Print Active: No"</li>
</ol>
<hr>
<h2 id="7-configuration-view">7. Configuration View </h2>
<p>This view is for setting persistent, system-wide hardware parameters. <strong>Changes made here affect the machine's core configuration and should be done with care.</strong></p>
<p><img src="images/configuration-view.png" alt="Configuration View"></p>
<ul>
<li><strong>Build Space Configuration:</strong> Set the machine's physical build volume dimensions (diameter, length, width).</li>
<li><strong>System Configuration:</strong> Set the <code>Resolution (µm)</code> and <code>Ejection Matrix Size</code>.</li>
<li><strong>Drum Gap Configuration:</strong>
<ul>
<li>Displays a list of the gaps between the drums in millimeters (mm).</li>
<li>Use the <code>Add Gap</code> and <code>Remove Gap</code> buttons to modify the list to match your hardware setup.</li>
</ul>
</li>
<li><strong>Action Buttons:</strong>
<ul>
<li><code>Save Configuration</code>: Applies all changes and saves them to the recoater hardware.</li>
</ul>
</li>
</ul>
<hr>
<h2 id="8-debug-view---direct-hardware-control">8. Debug View - Direct Hardware Control </h2>
<p>The Debug View provides direct, low-level access to recoater hardware functions for testing, troubleshooting, and manual operations. This view is designed for advanced users and contrasts significantly with the automated multi-material job workflow described in previous sections despite a similar interface to the Print page.</p>
<p><img src="images/debug-view.png" alt="Debug View"></p>
<p><strong>Important Note</strong>: The Debug View bypasses the automated job management system and provides direct hardware control. Use this view <strong>only</strong> for testing, troubleshooting, or when automated workflows are not suitable for your specific needs. This function is <strong>not</strong> meant for print jobs.</p>
<h3 id="81-purpose-and-use-cases">8.1. Purpose and Use Cases </h3>
<p>The Debug View serves several critical purposes:</p>
<p><strong>Testing and Validation</strong>:</p>
<ul>
<li>Test individual hardware components before running automated jobs</li>
<li>Validate drum functionality and file uploads</li>
<li>Verify layer parameter configurations</li>
<li>Debug hardware communication issues</li>
</ul>
<p><strong>Manual Operations</strong>:</p>
<ul>
<li>Direct file upload to specific drums when automated caching is not desired</li>
<li>Manual layer parameter adjustment for experimental setups</li>
<li>Individual drum geometry management</li>
<li>Direct print job control for simple, single-layer operations</li>
</ul>
<p><strong>Troubleshooting</strong>:</p>
<ul>
<li>Isolate hardware issues by testing components individually</li>
<li>Verify file formats and compatibility</li>
<li>Test network connectivity and API responses</li>
<li>Debug CLI file parsing and layer extraction</li>
</ul>
<h3 id="82-key-differences-from-automated-workflow">8.2. Key Differences from Automated Workflow </h3>
<pre data-role="codeBlock" data-info="" class="language-text"><code>Debug View (Manual Upload)              Multi-Material Jobs (Cache Upload)
┌─────────────────────────┐           ┌─────────────────────────────┐
│     Operator            │           │        Operator             │
│        │                │           │           │                 │
│        ▼                │           │           ▼                 │
│   Upload File           │           │    Upload File              │
│        │                │           │           │                 │
│        ▼                │           │           ▼                 │
│ ╔═══════════════════╗   │           │  ┌─────────────────────┐    │
│ ║    RECOATER       ║   │           │  │    HMI CACHE        │    │
│ ║   ┌─────────────┐ ║   │           │  │  ┌───────────────┐  │    │
│ ║   │   Drum 0    │ ║   │           │  │  │  drum0.cli    │  │    │
│ ║   │   Drum 1    │ ║   │           │  │  │  drum1.cli    │  │    │
│ ║   │   Drum 2    │ ║   │           │  │  │  drum2.cli    │  │    │
│ ║   └─────────────┘ ║   │           │  │  └───────────────┘  │    │
│ ║    [Hardware]     ║   │           │  │   [File System]     │    │
│ ╚═══════════════════╝   │           │  └─────────────────────┘    │
│                         │           │           │                 │
│   Direct to Machine     │           │           ▼                 │
└─────────────────────────┘           │  Automated Job sends to:    │
                                      │ ╔═══════════════════╗       │
                                      │ ║    RECOATER       ║       │
                                      │ ║   ┌─────────────┐ ║       │
                                      │ ║   │   Drum 0    │ ║       │
                                      │ ║   │   Drum 1    │ ║       │
                                      │ ║   │   Drum 2    │ ║       │
                                      │ ║   └─────────────┘ ║       │
                                      │ ║    [Hardware]     ║       │
                                      │ ╚═══════════════════╝       │
                                      └─────────────────────────────┘
</code></pre><table>
<thead>
<tr>
<th>Feature</th>
<th>Debug View (Manual)</th>
<th>Multi-Material Jobs (Automated)</th>
</tr>
</thead>
<tbody>
<tr>
<td><strong>File Management</strong></td>
<td>Direct upload to hardware drums</td>
<td>Cached in HMI drum management system</td>
</tr>
<tr>
<td><strong>Job Coordination</strong></td>
<td>Manual start/stop of simple print jobs</td>
<td>Automated layer-by-layer processing with OPC UA</td>
</tr>
<tr>
<td><strong>Layer Processing</strong></td>
<td>Manual layer parameter setting</td>
<td>Automatic layer iteration through cached data</td>
</tr>
<tr>
<td><strong>Error Handling</strong></td>
<td>Manual operator intervention required</td>
<td>Automatic pause-and-retry with guided recovery</td>
</tr>
<tr>
<td><strong>Multi-Drum Coordination</strong></td>
<td>Manual coordination between drums</td>
<td>Automatic synchronized processing across all drums</td>
</tr>
<tr>
<td><strong>Progress Tracking</strong></td>
<td>Basic job status only</td>
<td>Comprehensive progress tracking with layer counts</td>
</tr>
</tbody>
</table>
<h3 id="83-interface-components">8.3. Interface Components </h3>
<h4 id="connection-status">Connection Status </h4>
<p>Displays real-time connection status identical to other views, showing Connected/Disconnected state with visual indicators.</p>
<h4 id="layer-parameters-manual-configuration">Layer Parameters (Manual Configuration) </h4>
<p>Direct hardware parameter control:</p>
<ul>
<li><strong>Filling Drum ID</strong>: Specify which drum contains filling material (-1 for none)</li>
<li><strong>Patterning Speed (mm/s)</strong>: Set drum movement speed for current operation</li>
<li><strong>X Offset (mm)</strong>: Horizontal offset for material deposition</li>
<li><strong>Enable Powder Saving</strong>: Toggle powder conservation strategies</li>
</ul>
<p><strong>Controls</strong>:</p>
<ul>
<li><code>Load Current</code>: Retrieve current hardware parameter settings</li>
<li><code>Save Parameters</code>: Apply parameter changes directly to hardware</li>
</ul>
<h4 id="layer-preview-direct-hardware-preview">Layer Preview (Direct Hardware Preview) </h4>
<p>Real-time preview generation from current hardware state:</p>
<ul>
<li><strong>Preview Sources</strong>:
<ul>
<li><code>Current Layer Configuration</code>: Shows preview based on currently loaded parameters</li>
<li><code>Drum [0-2] Geometry</code>: Displays geometry currently stored on specific drum hardware</li>
</ul>
</li>
</ul>
<p><strong>Key Difference</strong>: Previews show the actual hardware state, not cached or planned configurations like in automated workflows.</p>
<h4 id="file-management-direct-hardware-upload">File Management (Direct Hardware Upload) </h4>
<p>Direct drum-to-hardware file operations:</p>
<p><strong>Per-Drum Upload Columns</strong>:</p>
<ul>
<li><strong>Drum 0, 1, 2</strong>: Individual file management for each drum</li>
<li><strong>Direct Upload</strong>: Files sent immediately to drum hardware (no caching)</li>
<li><strong>Download</strong>: Retrieve files directly from drum storage</li>
<li><strong>Delete</strong>: Remove files immediately from drum hardware</li>
</ul>
<p><strong>Important Notes</strong>:</p>
<ul>
<li>Files uploaded here are sent directly to hardware, bypassing the HMI caching system</li>
<li>No integration with automated multi-material job workflows</li>
<li>Each drum operation is independent - no coordination between drums</li>
<li>File operations take effect immediately</li>
</ul>
<h4 id="cli-layer-preview-manual-layer-operations">CLI Layer Preview (Manual Layer Operations) </h4>
<p>Advanced CLI file handling for testing and manual operations:</p>
<p><strong>Upload &amp; Parse</strong>:</p>
<ul>
<li>Upload multi-layer CLI files for parsing and individual layer access</li>
<li>Provides <code>File ID</code> and <code>Total Layers</code> information</li>
<li>Files remain in HMI memory for manual layer operations</li>
</ul>
<p><strong>Layer Preview</strong>:</p>
<ul>
<li>Select specific layer numbers for individual preview</li>
<li>Generate PNG previews of individual layers</li>
<li>Test CLI file integrity and layer content</li>
</ul>
<p><strong>Send Layer to Recoater</strong>:</p>
<ul>
<li>Extract individual layers and send directly to specific drums</li>
<li>Bypasses automated job caching system</li>
<li>Immediate hardware upload with no job coordination</li>
</ul>
<p><strong>Key Difference</strong>: Unlike automated workflows that cache entire ranges and process layers sequentially, this allows manual selection and immediate upload of individual layers.</p>
<h4 id="print-job-management-direct-hardware-control">Print Job Management (Direct Hardware Control) </h4>
<p>Basic print job operations without automation:</p>
<p><strong>Status Display</strong>:</p>
<ul>
<li><code>Current State</code>: Shows hardware state (Ready/Printing/Error)</li>
<li><code>Print Active</code>: Indicates if hardware is currently printing</li>
<li><code>Has Errors</code>: Shows error status requiring manual intervention</li>
</ul>
<p><strong>Job Controls</strong>:</p>
<ul>
<li><code>Start Print Job</code>: Initiates immediate printing with current hardware configuration</li>
<li><code>Cancel Print Job</code>: Stops current operation immediately</li>
<li><code>Refresh Status</code>: Manual status update from hardware</li>
</ul>
<p><strong>Key Differences</strong>:</p>
<ul>
<li>No automated layer progression</li>
<li>No multi-drum coordination</li>
<li>No automatic error recovery</li>
<li>No progress tracking through multiple layers</li>
<li>Simple start/stop operation only</li>
</ul>
<h3 id="84-transitioning-between-manual-and-automated-operations">8.4. Transitioning Between Manual and Automated Operations </h3>
<p><strong>From Debug to Automated</strong>:</p>
<ol>
<li>Complete all manual operations</li>
<li>Verify hardware status is Ready</li>
<li>Clear any temporary files if necessary</li>
<li>Return to Print Control View for automated operations</li>
</ol>
<p><strong>From Automated to Debug</strong>:</p>
<ol>
<li>Ensure automated jobs are completed or properly cancelled</li>
<li>Wait for system to return to Ready state</li>
<li>Clear error flags if necessary</li>
<li>Proceed with manual operations</li>
</ol>
<p><strong>Important</strong>: Never attempt to run automated jobs while manual operations are in progress, as this can cause hardware conflicts and unpredictable behavior.</p>
<hr>
<h2 id="9-technical-specifications">9. Technical Specifications </h2>
<p><strong>Hardware Limitations:</strong></p>
<ul>
<li><strong>Drum Count</strong>: System supports exactly 3 drums (IDs 0, 1, 2)</li>
<li><strong>CLI Format</strong>: Hardware requires ASCII CLI format for geometry data</li>
<li><strong>Cache Storage</strong>: Each drum can store only one cached geometry file at a time</li>
</ul>
<p><strong>Supported File Formats:</strong></p>
<ul>
<li><strong>PNG</strong>: Direct upload to drums (section 5.3)</li>
<li><strong>ASCII CLI</strong>: Direct upload or generated from parsed multi-layer files</li>
<li><strong>Binary CLI</strong>: Parsed and converted to ASCII CLI for hardware compatibility</li>
</ul>
<p><strong>Preview System:</strong></p>
<ul>
<li><strong>Color-Coded Rendering</strong>: Layer previews use drum-specific colors for visual distinction</li>
<li><strong>Drum Color Mapping</strong>: Drum 0 (Blue #3498db), Drum 1 (Orange #e67e22), Drum 2 (Green #27ae60)</li>
<li><strong>Fallback Support</strong>: System includes fallback mechanisms for preview generation failures</li>
</ul>
<p><strong>System Requirements:</strong></p>
<ul>
<li><strong>Network Connection</strong>: Required for communication between HMI and recoater hardware</li>
<li><strong>Browser Compatibility</strong>: Modern web browsers with WebSocket support</li>
<li><strong>File Size Limits</strong>: CLI files should be reasonable size for processing and transmission</li>
</ul>
<p><strong>Operator-Configurable System Settings:</strong></p>
<p>The HMI system behavior can be adjusted through configuration settings in the backend <code>.env</code> file. Operators may need to modify these settings based on specific hardware requirements or operational preferences:</p>
<p><em>Connection and Network Settings:</em></p>
<ul>
<li><strong>RECOATER_API_HOST</strong>: IP address of the recoater hardware (default: localhost)</li>
<li><strong>RECOATER_API_PORT</strong>: Port number for recoater communication (default: 8080)</li>
<li><strong>RECOATER_API_BASE_URL</strong>: Complete URL for recoater API access</li>
</ul>
<p><em>Polling and Update Intervals (in seconds):</em></p>
<ul>
<li><strong>WEBSOCKET_POLL_INTERVAL</strong>: How often the system checks for real-time updates (default: 2.0)</li>
<li><strong>LAYER_COMPLETION_POLL_INTERVAL</strong>: Frequency of layer completion status checks (default: 2.5)</li>
<li><strong>JOB_STATUS_POLL_INTERVAL_SECONDS</strong>: How often job status is refreshed (default: 2.5)</li>
</ul>
<p><em>Job Execution Timing:</em></p>
<ul>
<li><strong>JOB_DRUM_UPLOAD_DELAY_SECONDS</strong>: Delay between uploading to different drums to prevent hardware overload (default: 2.0)</li>
<li><strong>JOB_READY_TIMEOUT_SECONDS</strong>: Maximum wait time for recoater ready signal (default: 300)</li>
<li><strong>JOB_COMPLETION_TIMEOUT_SECONDS</strong>: Maximum wait time for layer completion (default: 1800)</li>
</ul>
<p><em>Hardware Configuration:</em></p>
<ul>
<li><strong>JOB_MAX_DRUMS</strong>: Number of drums available for multi-material printing (default: 3)</li>
<li><strong>JOB_EMPTY_LAYER_TEMPLATE_PATH</strong>: Path to blank CLI template for empty layers</li>
</ul>
<p><em>Development and Troubleshooting:</em></p>
<ul>
<li><strong>DEVELOPMENT_MODE</strong>: Enable mock hardware mode for testing (true/false)</li>
<li><strong>LOG_LEVEL</strong>: System logging detail level (INFO, DEBUG, WARNING, ERROR)</li>
</ul>
<p><em>OPC UA Coordination:</em></p>
<ul>
<li><strong>OPCUA_SERVER_ENDPOINT</strong>: OPC UA server connection string for PLC coordination</li>
<li><strong>OPCUA_NAMESPACE_URI</strong>: Namespace identifier for OPC UA variables</li>
</ul>
<p><em>Error Handling:</em></p>
<ul>
<li><strong>ERROR_STATE_DEBOUNCE_MS</strong>: Delay to prevent error message flickering (default: 500ms)</li>
<li><strong>ERROR_CLEARING_TIMEOUT_MS</strong>: Maximum time for error clearing operations (default: 5000ms)</li>
</ul>
<p><strong>Configuration Change Procedure:</strong></p>
<ol>
<li><strong>Access the Configuration File</strong>: Locate the <code>.env</code> file in the backend directory</li>
<li><strong>Edit Settings</strong>: Modify the desired values using a text editor</li>
<li><strong>Restart Services</strong>: Restart the HMI backend service for changes to take effect</li>
<li><strong>Verify Changes</strong>: Check the Status View to confirm the system is operating with new settings</li>
<li><strong>Test Operations</strong>: Perform basic operations to ensure the new settings work correctly</li>
</ol>
<p><strong>Important Notes:</strong></p>
<ul>
<li>Configuration changes require backend service restart to take effect</li>
<li>Incorrect network settings may prevent hardware communication</li>
<li>Timing values should be adjusted carefully to match hardware capabilities</li>
<li>Keep backup copies of working configurations before making changes</li>
<li>Contact system administrator for guidance on complex configuration changes</li>
</ul>

      </div>
      
      
    
    
    
    
    
    
  
    </body></html>