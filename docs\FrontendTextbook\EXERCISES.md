# Frontend Textbook - Practical Exercises and Coding Challenges

```
╔══════════════════════════════════════════════════════════════════════════════════════╗
║                           FRONTEND TEXTBOOK EXERCISES                                ║
║                    Hands-on Practice for Real-World Skills                          ║
╚══════════════════════════════════════════════════════════════════════════════════════╝
```

## How to Use This Exercise Guide

### Exercise Structure
Each exercise includes:
- **Learning Objectives**: What you'll practice
- **Requirements**: Specific features to implement
- **Starter Code**: Basic structure to begin with
- **Acceptance Criteria**: How to validate your solution
- **Extension Challenges**: Advanced features to explore
- **Real-World Context**: How this applies to industrial applications

### Difficulty Levels
- 🟢 **Beginner**: Basic concepts and syntax
- 🟡 **Intermediate**: Component composition and state management
- 🔴 **Advanced**: Complex patterns and optimization

### Time Estimates
- **Short**: 30-60 minutes
- **Medium**: 1-2 hours  
- **Long**: 2-4 hours
- **Project**: Multiple sessions

---

## Part I: Web Fundamentals Exercises

### Exercise 1.1: Industrial Control Panel Layout 🟢 (Short)

**Learning Objectives**: HTML semantics, CSS Grid, responsive design

**Scenario**: Create a control panel layout for the RecoaterHMI system that works on both desktop and tablet devices.

**Requirements**:
1. Create semantic HTML structure for a control panel
2. Use CSS Grid for the main layout
3. Include areas for: header, navigation, drum controls, status display, footer
4. Make it responsive for tablet (768px) and desktop (1024px+) screens
5. Use CSS custom properties for theming

**Starter Code**:
```html
<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>RecoaterHMI Control Panel</title>
    <link rel="stylesheet" href="control-panel.css">
</head>
<body>
    <!-- Your implementation here -->
</body>
</html>
```

**Acceptance Criteria**:
- [ ] Uses semantic HTML5 elements (`header`, `nav`, `main`, `section`, `aside`, `footer`)
- [ ] Implements CSS Grid with named grid areas
- [ ] Responsive design with proper breakpoints
- [ ] CSS custom properties for colors and spacing
- [ ] Accessible with proper ARIA labels
- [ ] Professional industrial design appearance

**Extension Challenges**:
- Add dark/light theme toggle
- Implement CSS animations for status indicators
- Add print styles for documentation
- Create a mobile-first responsive design

---

### Exercise 1.2: Interactive Drum Status Cards 🟡 (Medium)

**Learning Objectives**: CSS animations, JavaScript DOM manipulation, event handling

**Scenario**: Create interactive status cards for multiple recoater drums with real-time visual feedback.

**Requirements**:
1. Create 4 drum status cards using CSS Flexbox
2. Each card shows: drum ID, temperature, status, last update time
3. Implement smooth CSS transitions for status changes
4. Add JavaScript to simulate status updates every 3 seconds
5. Use different colors for different statuses (idle, running, error)
6. Add hover effects and click interactions

**Starter Code**:
```javascript
// Drum data structure
const drumData = [
    { id: 0, temperature: 23.5, status: 'idle', lastUpdate: new Date() },
    { id: 1, temperature: 45.2, status: 'running', lastUpdate: new Date() },
    { id: 2, temperature: 78.9, status: 'running', lastUpdate: new Date() },
    { id: 3, temperature: 0, status: 'error', lastUpdate: new Date() }
];

// Your implementation here
function createDrumCard(drum) {
    // Create and return DOM element
}

function updateDrumStatus() {
    // Simulate status changes
}
```

**Acceptance Criteria**:
- [ ] 4 visually appealing drum cards
- [ ] Smooth CSS transitions between states
- [ ] JavaScript updates every 3 seconds
- [ ] Different visual styles for each status
- [ ] Proper timestamp formatting
- [ ] Responsive card layout

**Extension Challenges**:
- Add temperature warning thresholds
- Implement click-to-expand card details
- Add sound notifications for status changes
- Create historical status timeline

---

## Part II: Vue.js Framework Exercises

### Exercise 2.1: Reactive Drum Selector Component 🟢 (Medium)

**Learning Objectives**: Vue.js reactivity, component props, events

**Scenario**: Build a drum selector component that allows operators to choose which drum to control.

**Requirements**:
1. Create a `DrumSelector.vue` component
2. Accept `drums` array as prop
3. Emit `drumSelected` event when selection changes
4. Show drum ID, name, and connection status
5. Disable disconnected drums
6. Highlight currently selected drum

**Starter Code**:
```vue
<template>
  <!-- Your implementation here -->
</template>

<script setup>
// Define props and emits
// Implement selection logic
</script>

<style scoped>
/* Your styles here */
</style>
```

**Test Data**:
```javascript
const drums = [
  { id: 0, name: 'Primary Drum', connected: true },
  { id: 1, name: 'Secondary Drum', connected: true },
  { id: 2, name: 'Backup Drum', connected: false },
  { id: 3, name: 'Test Drum', connected: true }
]
```

**Acceptance Criteria**:
- [ ] Component accepts `drums` prop correctly
- [ ] Emits `drumSelected` event with drum ID
- [ ] Visual indication of selected drum
- [ ] Disabled state for disconnected drums
- [ ] Proper prop validation
- [ ] Scoped CSS styling

**Extension Challenges**:
- Add search/filter functionality
- Implement keyboard navigation
- Add drum status indicators
- Create drag-and-drop reordering

---

### Exercise 2.2: Layer Preview System 🟡 (Long)

**Learning Objectives**: Composition API, async operations, error handling

**Scenario**: Build a complete layer preview system that allows operators to view specific layers from uploaded files.

**Requirements**:
1. Create `LayerPreview.vue` component
2. Use Composition API with `<script setup>`
3. Implement file upload handling
4. Add layer number input with validation
5. Show loading states during API calls
6. Handle and display errors gracefully
7. Display preview image when loaded

**Starter Code**:
```vue
<template>
  <div class="layer-preview">
    <!-- File upload area -->
    <!-- Layer selection controls -->
    <!-- Preview display area -->
    <!-- Error handling -->
  </div>
</template>

<script setup>
import { ref, computed, watch } from 'vue'

// Reactive state
const selectedFile = ref(null)
const selectedLayer = ref(1)
const previewImage = ref('')
const isLoading = ref(false)
const error = ref('')

// Computed properties
const maxLayers = computed(() => {
  // Calculate max layers from file
})

// Methods
const handleFileUpload = (event) => {
  // Handle file selection
}

const loadPreview = async () => {
  // Load preview image
}

// Your implementation here
</script>

<style scoped>
/* Your styles here */
</style>
```

**Mock API Functions**:
```javascript
// Simulate API calls (replace with real API later)
const mockApi = {
  uploadFile: (file) => new Promise(resolve => 
    setTimeout(() => resolve({ layerCount: 150 }), 1000)
  ),
  
  getPreview: (fileName, layer) => new Promise((resolve, reject) => 
    setTimeout(() => {
      if (Math.random() > 0.1) { // 90% success rate
        resolve({ imageUrl: `https://picsum.photos/400/300?random=${layer}` })
      } else {
        reject(new Error('Preview generation failed'))
      }
    }, 2000)
  )
}
```

**Acceptance Criteria**:
- [ ] File upload with drag-and-drop support
- [ ] Layer input validation (1 to max layers)
- [ ] Loading spinner during operations
- [ ] Error messages for failed operations
- [ ] Preview image display
- [ ] Responsive design
- [ ] Proper cleanup of object URLs

**Extension Challenges**:
- Add zoom and pan functionality to preview
- Implement preview caching
- Add layer comparison mode
- Create preview annotation tools

---

### Exercise 2.3: Real-time Drum Monitoring Dashboard 🔴 (Project)

**Learning Objectives**: Pinia state management, WebSocket integration, component composition

**Scenario**: Create a comprehensive monitoring dashboard that shows real-time status of all drums in the system.

**Requirements**:
1. Set up Pinia store for drum state management
2. Create multiple child components for different views
3. Implement simulated real-time data updates
4. Add filtering and sorting capabilities
5. Include alarm system for critical statuses
6. Responsive design for different screen sizes

**Project Structure**:
```
src/
├── components/
│   ├── DrumCard.vue
│   ├── DrumGrid.vue
│   ├── StatusFilter.vue
│   ├── AlarmPanel.vue
│   └── DrumDetails.vue
├── stores/
│   └── drumStore.js
├── views/
│   └── MonitoringDashboard.vue
└── composables/
    └── useRealTimeData.js
```

**Phase 1 - Basic Store Setup**:
```javascript
// stores/drumStore.js
import { defineStore } from 'pinia'

export const useDrumStore = defineStore('drum', () => {
  // State
  const drums = ref(new Map())
  const filters = ref({
    status: 'all',
    temperature: 'all'
  })
  
  // Getters
  const filteredDrums = computed(() => {
    // Implement filtering logic
  })
  
  // Actions
  const updateDrum = (drumId, data) => {
    // Update drum data
  }
  
  const addAlarm = (drumId, message, severity) => {
    // Add alarm to system
  }
  
  return {
    drums,
    filters,
    filteredDrums,
    updateDrum,
    addAlarm
  }
})
```

**Phase 2 - Real-time Data Simulation**:
```javascript
// composables/useRealTimeData.js
export function useRealTimeData() {
  const drumStore = useDrumStore()
  
  const startSimulation = () => {
    setInterval(() => {
      // Simulate random drum updates
      const drumId = Math.floor(Math.random() * 4)
      const temperature = 20 + Math.random() * 60
      const status = Math.random() > 0.9 ? 'error' : 'running'
      
      drumStore.updateDrum(drumId, { temperature, status })
    }, 2000)
  }
  
  return { startSimulation }
}
```

**Acceptance Criteria**:
- [ ] Pinia store properly manages drum state
- [ ] Real-time updates every 2 seconds
- [ ] Filtering by status and temperature
- [ ] Alarm system for error states
- [ ] Responsive grid layout
- [ ] Component composition with clear interfaces
- [ ] Error handling for data updates

**Extension Challenges**:
- Add WebSocket connection for real data
- Implement historical data charting
- Create maintenance scheduling system
- Add user authentication and permissions

---

## Part III: Integration Exercises

### Exercise 3.1: Complete Layer Preview Application 🔴 (Project)

**Learning Objectives**: Full-stack integration, error handling, performance optimization

**Scenario**: Build the complete layer preview application that integrates all concepts from the textbook.

**Requirements**:
1. Implement the complete LayerPreviewSystem component from Chapter 13
2. Add proper error boundaries and fallback UI
3. Implement performance optimizations
4. Add accessibility features
5. Create comprehensive test suite
6. Add documentation

**Project Phases**:

**Phase 1 - Core Implementation**:
- Implement the LayerPreviewSystem component exactly as shown in Chapter 13
- Add proper TypeScript types
- Implement error handling with user-friendly messages

**Phase 2 - Performance Optimization**:
- Add image caching system
- Implement lazy loading for non-critical features
- Add performance monitoring

**Phase 3 - Accessibility & Testing**:
- Add WCAG 2.1 AA compliance
- Write unit tests for all components
- Add integration tests for user workflows

**Phase 4 - Documentation**:
- Create user manual
- Add developer documentation
- Create deployment guide

**Starter Template**:
```vue
<!-- src/LayerPreviewApp.vue -->
<template>
  <div id="app">
    <AppHeader />
    <ErrorBoundary>
      <LayerPreviewSystem />
    </ErrorBoundary>
    <AppFooter />
  </div>
</template>

<script setup>
import { provide } from 'vue'
import { apiService } from './services/apiService'
import LayerPreviewSystem from './components/LayerPreviewSystem.vue'
import AppHeader from './components/AppHeader.vue'
import AppFooter from './components/AppFooter.vue'
import ErrorBoundary from './components/ErrorBoundary.vue'

// Provide services
provide('apiService', apiService)
</script>
```

**Acceptance Criteria**:
- [ ] Complete layer preview functionality
- [ ] Error boundaries handle all edge cases
- [ ] Performance metrics under 3s load time
- [ ] WCAG 2.1 AA accessibility compliance
- [ ] 90%+ test coverage
- [ ] Complete documentation

---

### Exercise 3.2: Industrial Design System 🟡 (Long)

**Learning Objectives**: Design systems, CSS architecture, component libraries

**Scenario**: Create a comprehensive design system for industrial applications that can be reused across multiple projects.

**Requirements**:
1. Define design tokens (colors, spacing, typography)
2. Create base components (buttons, inputs, cards)
3. Build complex components (data tables, forms, modals)
4. Add dark/light theme support
5. Create Storybook documentation
6. Implement accessibility standards

**Design System Structure**:
```
design-system/
├── tokens/
│   ├── colors.css
│   ├── spacing.css
│   ├── typography.css
│   └── shadows.css
├── components/
│   ├── base/
│   │   ├── Button.vue
│   │   ├── Input.vue
│   │   ├── Card.vue
│   │   └── Badge.vue
│   ├── complex/
│   │   ├── DataTable.vue
│   │   ├── Modal.vue
│   │   ├── Form.vue
│   │   └── Navigation.vue
│   └── layout/
│       ├── Container.vue
│       ├── Grid.vue
│       └── Stack.vue
├── themes/
│   ├── light.css
│   └── dark.css
└── docs/
    └── guidelines.md
```

**Phase 1 - Design Tokens**:
```css
/* tokens/colors.css */
:root {
  /* Primary colors for industrial applications */
  --primary-50: #eff6ff;
  --primary-500: #3b82f6;
  --primary-900: #1e3a8a;
  
  /* Status colors */
  --success-500: #10b981;
  --warning-500: #f59e0b;
  --error-500: #ef4444;
  
  /* Neutral colors */
  --neutral-50: #f9fafb;
  --neutral-500: #6b7280;
  --neutral-900: #111827;
}
```

**Phase 2 - Base Components**:
```vue
<!-- components/base/Button.vue -->
<template>
  <button 
    :class="buttonClasses"
    :disabled="disabled"
    @click="$emit('click', $event)"
  >
    <slot />
  </button>
</template>

<script setup>
import { computed } from 'vue'

const props = defineProps({
  variant: {
    type: String,
    default: 'primary',
    validator: (value) => ['primary', 'secondary', 'danger'].includes(value)
  },
  size: {
    type: String,
    default: 'medium',
    validator: (value) => ['small', 'medium', 'large'].includes(value)
  },
  disabled: {
    type: Boolean,
    default: false
  }
})

const emit = defineEmits(['click'])

const buttonClasses = computed(() => [
  'btn',
  `btn--${props.variant}`,
  `btn--${props.size}`,
  { 'btn--disabled': props.disabled }
])
</script>

<style scoped>
.btn {
  /* Base button styles using design tokens */
  font-family: var(--font-family-base);
  font-weight: var(--font-weight-medium);
  border-radius: var(--border-radius-md);
  transition: all var(--transition-fast);
  cursor: pointer;
  border: none;
}

.btn--primary {
  background-color: var(--primary-500);
  color: white;
}

.btn--medium {
  padding: var(--spacing-3) var(--spacing-4);
  font-size: var(--font-size-base);
}

/* Add other variants... */
</style>
```

**Acceptance Criteria**:
- [ ] Complete design token system
- [ ] 15+ reusable components
- [ ] Dark/light theme support
- [ ] Storybook documentation
- [ ] Accessibility testing passed
- [ ] Usage examples and guidelines

---

## Assessment Exercises

### Final Project: RecoaterHMI Complete Application 🔴 (Project)

**Learning Objectives**: Integration of all concepts, professional development practices

**Scenario**: Build a complete production-ready RecoaterHMI application that demonstrates mastery of all textbook concepts.

**Requirements**:
1. Multi-page application with Vue Router
2. Complete state management with Pinia
3. Real-time monitoring and control
4. File upload and management
5. User authentication and permissions
6. Responsive design for all devices
7. Comprehensive testing
8. Production deployment

**Application Features**:

**Core Features**:
- [ ] User authentication (login/logout)
- [ ] Dashboard with system overview
- [ ] Drum management and control
- [ ] File upload and layer preview
- [ ] Real-time status monitoring
- [ ] Alarm and notification system

**Advanced Features**:
- [ ] Historical data and reporting
- [ ] User management and permissions
- [ ] System settings and configuration
- [ ] Maintenance scheduling
- [ ] API documentation
- [ ] Mobile app companion

**Technical Requirements**:
- [ ] Vue 3 with Composition API
- [ ] TypeScript throughout
- [ ] Pinia for state management
- [ ] Vue Router with guards
- [ ] Vite build system
- [ ] Vitest for testing
- [ ] ESLint and Prettier
- [ ] Docker deployment

**Project Structure**:
```
recoater-hmi/
├── src/
│   ├── components/
│   ├── views/
│   ├── stores/
│   ├── services/
│   ├── composables/
│   ├── utils/
│   ├── types/
│   └── assets/
├── tests/
│   ├── unit/
│   ├── integration/
│   └── e2e/
├── docs/
├── docker/
└── deployment/
```

**Assessment Criteria**:

**Code Quality (25%)**:
- [ ] Clean, readable code with proper comments
- [ ] Consistent naming conventions
- [ ] Proper TypeScript usage
- [ ] No console errors or warnings

**Architecture (25%)**:
- [ ] Proper component composition
- [ ] Effective state management
- [ ] Clear separation of concerns
- [ ] Scalable folder structure

**Functionality (25%)**:
- [ ] All required features implemented
- [ ] Error handling and edge cases
- [ ] Performance optimizations
- [ ] User experience quality

**Professional Practices (25%)**:
- [ ] Comprehensive testing
- [ ] Documentation quality
- [ ] Git workflow and commits
- [ ] Deployment readiness

---

## Exercise Solutions and Code Reviews

### How to Submit Exercises

1. **Create a Git Repository** for each exercise
2. **Document Your Process** in README files
3. **Include Screenshots** of working features
4. **Write Reflection Notes** on challenges and solutions
5. **Ask for Code Reviews** from peers or mentors

### Self-Assessment Questions

After each exercise, ask yourself:

1. **Understanding**: Do I understand why this solution works?
2. **Alternatives**: Could I solve this problem differently?
3. **Quality**: Is my code clean and maintainable?
4. **Performance**: How would this scale with more data/users?
5. **Accessibility**: Can all users interact with this interface?
6. **Real-World**: How would this work in a production environment?

### Getting Help

**When Stuck**:
1. Review the relevant textbook chapter
2. Check Vue.js and Pinia documentation
3. Search for similar examples online
4. Ask specific questions in developer communities
5. Break the problem into smaller parts

**Code Review Checklist**:
- [ ] Code follows Vue.js best practices
- [ ] Components are properly composed
- [ ] State management is appropriate
- [ ] Error handling is comprehensive
- [ ] Code is accessible and semantic
- [ ] Performance considerations are addressed

---

## Additional Practice Resources

### Daily Coding Challenges
- **30 Days of Vue**: Build one small Vue.js component each day
- **Weekly Industrial UI**: Create industry-specific interfaces
- **Performance Friday**: Optimize an existing component each week

### Community Challenges
- **Vue.js Challenge**: Monthly community coding challenges
- **Frontend Mentor**: Real-world design challenges
- **DevChallenges**: Frontend developer skill challenges

### Open Source Contributions
- **Vue.js Ecosystem**: Contribute to Vue.js related projects
- **Industrial Tools**: Build tools for manufacturing or industrial use
- **Documentation**: Help improve Vue.js documentation

### Continuous Learning
- **Vue.js Conferences**: Watch talks and presentations
- **Technical Blogs**: Follow Vue.js and frontend development blogs
- **Code Reviews**: Participate in code review sessions
- **Mentoring**: Teach others what you've learned

---

**🎯 Ready to Build Amazing Applications!**

These exercises will take you from beginner to professional Vue.js developer. Start with the fundamentals and work your way up to the complex projects. Remember: the goal is not just to complete the exercises, but to understand the principles and patterns that make great applications.

**Happy coding, and welcome to the world of professional frontend development!**