# Recoater HMI Developer Guide

Recoater HMI is a full-stack web application designed to manage and control the multi-material Aerosint Recoater system. This guide offers a technical breakdown of the project's architecture and APIs for developers who wish to contribute to the project.

## Table of Contents

- [1. Introduction](#1-introduction)
  - [1.1 Project Overview](#11-project-overview)
  - [1.2 Target Audience](#12-target-audience)
  - [1.3 Prerequisites](#13-prerequisites)
  - [1.4 Technology Stack](#14-technology-stack)
- [2. System Architecture](#2-system-architecture)
  - [2.1 Architecture Diagram](#21-architecture-diagram)
  - [2.2 Architectural Pattern Analysis](#22-architectural-pattern-analysis)
- [3. Backend Development](#3-backend-development)
  - [3.1 3-Layer Clean Architecture Overview](#31-3-layer-clean-architecture-overview)
    - [3.1.1 Architecture Diagram](#311-architecture-diagram)
    - [3.1.2 Architectural Pattern Analysis](#312-architectural-pattern-analysis)
  - [3.2 Presentation Layer](#32-presentation-layer)
    - [3.2.1 API Structure and Organization](#321-api-structure-and-organization)
      - [******* API Architecture Overview](#3211-api-architecture-overview)
      - [******* API Design Principles](#3212-api-design-principles)
    - [3.2.2 System APIs](#322-system-apis)
      - [******* Configuration Management API](#3221-configuration-management-api)
        - [GET /config](#get-config)
        - [PUT /config](#put-config)
      - [******* System Status API](#3222-system-status-api)
        - [GET /status](#get-status)
        - [GET /status/health](#get-statushealth)
        - [POST /status/state](#post-statusstate)
      - [******* Error Management API](#3223-error-management-api)
        - [POST /errors/clear](#post-errorsclear)
    - [3.2.3 Print Job Management APIs](#323-print-job-management-apis)
      - [******* CLI File Processing API](#3231-cli-file-processing-api)
        - [POST /cli/upload](#post-cliupload)
        - [POST /cli/upload/drum_id](#post-cliuploaddrum_id)
        - [GET /cli/{file_id}/layer/{layer_num}/preview](#get-clifile_idlayerlayer_numpreview)
        - [POST /cli/{file_id}/layer/{layer_num}/send/drum_id](#post-clifile_idlayerlayer_numsenddrum_id)
        - [POST /cli/{file_id}/layers/send/{drum_id}](#post-clifile_idlayerssendddrum_id)
      - [******* Multi-Material Job Orchestration API](#3232-multi-material-job-orchestration-api)
        - [POST /cli/start-multimaterial-job](#post-clistart-multimaterial-job)
        - [GET /multimaterial-job/status](#get-multimaterial-jobstatus)
        - [POST /multimaterial-job/cancel](#post-multimaterial-jobcancel)
      - [******* Cache Management API](#3233-cache-management-api)
        - [GET /cli/drum-cache-status](#get-clidrum-cache-status)
        - [POST /cli/clear-drum-cache/{drum_id}](#post-cliclear-drum-cachedrum_id)
        - [GET /multimaterial-job/drum-status/{drum_id}](#get-multimaterial-jobdrum-statusdrum_id)
      - [******* Drum Management APIs](#3234-drum-management-apis)
        - [GET /drums/{drum_id}/geometry](#get-drumsdrum_idgeometry)
        - [GET /drums/{drum_id}/geometry/preview](#get-drumsdrum_idgeometrypreview)
        - [POST /drums/{drum_id}/geometry](#post-drumsdrum_idgeometry)
        - [DELETE /drums/{drum_id}/geometry](#delete-drumsdrum_idgeometry)
      - [******* Layer Parameter & Preview APIs](#3235-layer-parameter--preview-apis)
        - [GET /layer/parameters](#get-layerparameters)
        - [PUT /layer/parameters](#put-layerparameters)
        - [GET /layer/preview](#get-layerpreview)
      - [******* Job Management APIs](#3236-job-management-apis)
        - [POST /job](#post-job)
        - [DELETE /job](#delete-job)
        - [GET /job/status](#get-jobstatus)
    - [3.2.4 Recoater Hardware Control APIs](#324-recoater-hardware-control-apis)
      - [******* Drum Control API](#3241-drum-control-api)
        - [GET /drums/{drum_id}/motion](#get-drumsdrum_idmotion)
        - [POST /drums/{drum_id}/motion](#post-drumsdrum_idmotion)
        - [DELETE /drums/{drum_id}/motion](#delete-drumsdrum_idmotion)
        - [GET /drums/{drum_id}/ejection](#get-drumsdrum_idejection)
        - [PUT /drums/{drum_id}/ejection](#put-drumsdrum_idejection)
        - [GET /drums/{drum_id}/suction](#get-drumsdrum_idsuction)
        - [PUT /drums/{drum_id}/suction](#put-drumsdrum_idsuction)
      - [******* Leveler Control API](#3242-leveler-control-api)
        - [GET /leveler/pressure](#get-levelerpressure)
        - [PUT /leveler/pressure](#put-levelerpressure)
        - [GET /leveler/sensor](#get-levelersensor)
      - [******* Blade Control API](#3243-blade-control-api)
        - [GET /drums/{drum_id}/blade/screws](#get-drumsdrum_idbladescrews)
        - [GET /drums/{drum_id}/blade/screws/motion](#get-drumsdrum_idbladescrewsmotion)
        - [POST /drums/{drum_id}/blade/screws/motion](#post-drumsdrum_idbladescrewsmotion)
        - [DELETE /drums/{drum_id}/blade/screws/motion](#delete-drumsdrum_idbladescrewsmotion)
        - [GET /drums/{drum_id}/blade/screws/{screw_id}](#get-drumsdrum_idbladescrewsscrew_id)
        - [GET /drums/{drum_id}/blade/screws/{screw_id}/motion](#get-drumsdrum_idbladescrewsscrew_idmotion)
        - [POST /drums/{drum_id}/blade/screws/{screw_id}/motion](#post-drumsdrum_idbladescrewsscrew_idmotion)
        - [DELETE /drums/{drum_id}/blade/screws/{screw_id}/motion](#delete-drumsdrum_idbladescrewsscrew_idmotion)
  - [3.3 Service Layer (Business Logic)](#33-service-layer-business-logic)
  - [3.4 Infrastructure Layer](#34-infrastructure-layer)
  - [3.5 Overall Backend Workflow](#35-overall-backend-workflow)
    - [3.5.1 Router Registration Flow](#351-router-registration-flow)
- [4. Frontend Development](#4-frontend-development)
  - [4.1 Frontend Layered Architecture](#41-frontend-layered-architecture)
  - [4.2 Component Development](#42-component-development)
  - [4.3 State Management](#43-state-management)
  - [4.4 Routing](#44-routing)
  - [4.5 API Integration](#45-api-integration)
  - [4.6 Real-time Features](#46-real-time-features)
  - [4.7 Styling and UI](#47-styling-and-ui)
- [5. API Documentation](#5-api-documentation)
  - [5.1 API Overview](#51-api-overview)
  - [5.2 Authentication](#52-authentication)
  - [5.3 Endpoint Reference](#53-endpoint-reference)
  - [5.4 WebSocket Events](#54-websocket-events)
  - [5.5 Error Codes](#55-error-codes)
  - [5.6 Rate Limiting](#56-rate-limiting)

---


## 1. Introduction

### 1.1 Project Overview

Recoater HMI originates from a need to replace the default SwaggerUI and API endpoints on the multi-material Aerosint Recoater system with a more user-friendly interface for users.

### 1.2 Target Audience

- Developers joining the project
- Ambitious engineers who wish to try their hands at prompt engineering the development of an industrial application
- Interns who are tasked with fixing existing bugs and expanding the program's capabilities


### 1.3 Prerequisites

- Backend: Python 3.9+, basic FastAPI and asyncio familiarity
- Frontend: Node.js v16+ LTS, npm or pnpm, basic Vue 3 and Pinia familiarity
- Optional: Knowledge of OPC UA & asyncua or alternatively a very good grasp of prompt/context engineering and spec-driven development for agentic systems or whichever frontier agentic software development technique is trending
- Hardware: Access to Aerosint Recoater system (or mock mode for development)

### 1.4 Technology Stack

- Backend: FastAPI, Pydantic, WebSocket, OPC UA client library, uvicorn, pytest
- Frontend: Vue 3 (Composition API), Pinia, Vue Router, Axios HTTP client, Vite
- Communication: REST API, WebSocket real-time updates, OPC UA industrial protocol
- Tooling: Python/JS test frameworks, ESLint, Prettier

## 2. System Architecture


### 2.1 Architecture Diagram
```mermaid
graph TB
    subgraph "Frontend Application"
        subgraph "Presentation Layer (Views)"
            STATUS_VIEW["StatusView"]
            RECOATER_VIEW["RecoaterView"]
            PRINT_VIEW["PrintView"]
            CONFIG_VIEW["ConfigurationView"]
            DEBUG_VIEW["DebugView"]
        end

        subgraph "Component Layer"
            STATUS_INDICATOR["StatusIndicator"]
            DRUM_CONTROL["DrumControl"]
            LEVELER_CONTROL["LevelerControl"]
            FILE_UPLOAD["FileUploadColumn"]
            JOB_PROGRESS["JobProgressDisplay"]
            MULTI_LAYER_JOB["MultiLayerJobControl"]
            ERROR_MODAL["CriticalErrorModal"]
            ERROR_DISPLAY["ErrorDisplayPanel"]
        end

        subgraph "Application State Layer (Pinia Stores)"
            STATUS_STORE["statusStore"]
            PRINT_JOB_STORE["printJobStore"]
        end

        subgraph "Service Layer"
            API_SERVICE["apiService"]
        end

        subgraph "Infrastructure Layer"
            ROUTER["Vue Router"]
            WEBSOCKET["WebSocket Client"]
            HTTP_CLIENT["Axios HTTP Client"]
        end
    end

    subgraph "Backend"
        subgraph "Presentation Layer"
            SYSTEM_APIS["System APIs"]
            subgraph "Print APIs"
                CLI_API["CLI API"]
                LAYER_API["Layer API"]
                MM_API["Multi-Material API"]
                JOB_API["Job API"]
                PRINT_DRUM_API["Drum API"]
            end
            subgraph "Recoater Control APIs"
                RECOATER_DRUM_API["Drum API"]
                RECOATER_BLADE_API["Blade API"]
                RECOATER_LEVELER_API["Leveler API"]
            end
        end
        subgraph "Service Layer"
            JOB_MANAGEMENT["Job Management"]
            COMMUNICATION["Communication"]
            MONITORING["Monitoring"]
            OPCUA["OPCUA"]
        end
        subgraph "Infrastructure Layer"
            CLI_EDITOR["CLI Editor"]
            RECOATER_CLIENT["Recoater Client"]
            MOCK_CLIENT["Mock Recoater Client"]
        end
    end

    subgraph "Aerosint Recoater"
        SERVER["Server"]
    end

    subgraph "TwinCAT XAR"
        PLC["PLC"]
    end

    %% Frontend Layer Connections
    STATUS_VIEW --> STATUS_INDICATOR
    RECOATER_VIEW --> DRUM_CONTROL
    RECOATER_VIEW --> LEVELER_CONTROL
    PRINT_VIEW --> FILE_UPLOAD
    PRINT_VIEW --> JOB_PROGRESS
    PRINT_VIEW --> MULTI_LAYER_JOB
    STATUS_VIEW --> ERROR_MODAL
    DEBUG_VIEW --> ERROR_DISPLAY

    %% Component to State connections
    STATUS_INDICATOR --> STATUS_STORE
    DRUM_CONTROL --> STATUS_STORE
    LEVELER_CONTROL --> STATUS_STORE
    FILE_UPLOAD --> PRINT_JOB_STORE
    JOB_PROGRESS --> PRINT_JOB_STORE
    MULTI_LAYER_JOB --> PRINT_JOB_STORE



    %% State to Service connections
    STATUS_STORE --> API_SERVICE
    PRINT_JOB_STORE --> API_SERVICE

    %% Service to Infrastructure connections
    API_SERVICE --> HTTP_CLIENT
    API_SERVICE --> WEBSOCKET

    %% Frontend to Backend connections
    HTTP_CLIENT --> SYSTEM_APIS
    HTTP_CLIENT --> CLI_API
    HTTP_CLIENT --> LAYER_API
    HTTP_CLIENT --> MM_API
    HTTP_CLIENT --> JOB_API
    HTTP_CLIENT --> PRINT_DRUM_API
    HTTP_CLIENT --> RECOATER_DRUM_API
    HTTP_CLIENT --> RECOATER_BLADE_API
    HTTP_CLIENT --> RECOATER_LEVELER_API

    WEBSOCKET <--> COMMUNICATION

    %% Backend API to Service connections
    SYSTEM_APIS --> RECOATER_CLIENT
    SYSTEM_APIS --> OPCUA
    SYSTEM_APIS --> JOB_MANAGEMENT
    CLI_API --> JOB_MANAGEMENT
    CLI_API --> CLI_EDITOR
    LAYER_API --> JOB_MANAGEMENT
    MM_API --> JOB_MANAGEMENT
    JOB_API --> JOB_MANAGEMENT
    PRINT_DRUM_API --> RECOATER_CLIENT
    RECOATER_DRUM_API --> RECOATER_CLIENT
    RECOATER_BLADE_API --> RECOATER_CLIENT
    RECOATER_LEVELER_API --> RECOATER_CLIENT

    %% Backend Service to Infrastructure connections
    JOB_MANAGEMENT --> OPCUA
    JOB_MANAGEMENT --> CLI_EDITOR
    JOB_MANAGEMENT --> RECOATER_CLIENT
    COMMUNICATION --> RECOATER_CLIENT
    COMMUNICATION --> MOCK_CLIENT
    MONITORING --> OPCUA
    MONITORING --> RECOATER_CLIENT
    MONITORING <--> COMMUNICATION



    %% External connections
    RECOATER_CLIENT <--> SERVER
    OPCUA <--> PLC

    %% Styling for Frontend layers
    classDef frontendPresentation fill:#e1f5fe,stroke:#0277bd,stroke-width:2px,color:#000
    classDef frontendComponent fill:#e8f5e8,stroke:#388e3c,stroke-width:2px,color:#000
    classDef frontendState fill:#fff3e0,stroke:#f57c00,stroke-width:2px,color:#000
    classDef frontendService fill:#fce4ec,stroke:#c2185b,stroke-width:2px,color:#000
    classDef frontendInfra fill:#f3e5f5,stroke:#7b1fa2,stroke-width:2px,color:#000

    %% Styling for Backend layers
    classDef backendPresentation fill:#e3f2fd,stroke:#1565c0,stroke-width:2px,color:#000
    classDef backendService fill:#e0f2f1,stroke:#00695c,stroke-width:2px,color:#000
    classDef backendInfra fill:#fff8e1,stroke:#ef6c00,stroke-width:2px,color:#000

    %% Styling for External systems
    classDef external fill:#ffebee,stroke:#d32f2f,stroke-width:3px,color:#000

    %% Apply styles to Frontend Presentation Layer
    class STATUS_VIEW,RECOATER_VIEW,PRINT_VIEW,CONFIG_VIEW,DEBUG_VIEW frontendPresentation

    %% Apply styles to Frontend Component Layer
    class STATUS_INDICATOR,DRUM_CONTROL,LEVELER_CONTROL,FILE_UPLOAD,JOB_PROGRESS,MULTI_LAYER_JOB,ERROR_MODAL,ERROR_DISPLAY frontendComponent

    %% Apply styles to Frontend State Layer
    class STATUS_STORE,PRINT_JOB_STORE frontendState

    %% Apply styles to Frontend Service Layer
    class API_SERVICE frontendService

    %% Apply styles to Frontend Infrastructure Layer
    class ROUTER,WEBSOCKET,HTTP_CLIENT frontendInfra

    %% Apply styles to Backend Presentation Layer
    class SYSTEM_APIS,CLI_API,LAYER_API,MM_API,JOB_API,PRINT_DRUM_API,RECOATER_DRUM_API,RECOATER_BLADE_API,RECOATER_LEVELER_API backendPresentation

    %% Apply styles to Backend Service Layer
    class JOB_MANAGEMENT,COMMUNICATION,MONITORING,OPCUA backendService

    %% Apply styles to Backend Infrastructure Layer
    class CLI_EDITOR,RECOATER_CLIENT,MOCK_CLIENT backendInfra

    %% Apply styles to External Systems
    class SERVER,PLC external

```

Note: The Vue Router is included in the Infrastructure Layer and is connected to the Views as it provides low-level routing capabilities for PrintView, RecoaterView, StatusView, ConfigurationView, and DebugView components. However, connections are not drawn in the diagram to avoid clutter.

### 2.2 Architectural Pattern Analysis

The Recoater HMI system implements a **Layered (N-tier) Architecture** with clear separation of concerns across both frontend and backend systems. This architecture provides scalability, maintainability, and testability through well-defined layer boundaries and dependencies.

**Frontend Application - 5-Layer Architecture:**
1. **Presentation Layer (Views)**: User interface components that handle user interactions and display data
2. **Component Layer**: Reusable UI components that encapsulate specific functionality
3. **Application State Layer (Pinia Stores)**: Centralized state management using reactive stores
4. **Service Layer**: API communication and business logic coordination
5. **Infrastructure Layer**: Low-level services like routing, HTTP client, and WebSocket connections

**Backend Application - 3-Layer Architecture:**
1. **Presentation Layer**: REST API endpoints that handle HTTP requests and responses
2. **Service Layer**: Business logic, job management, communication, and monitoring services
3. **Infrastructure Layer**: Hardware clients, file processors, and external system integrations

**Cross-System Communication:**
- **Frontend Service ↔ Backend Presentation**: RESTful API calls for user-initiated actions
- **Frontend Infrastructure ↔ Backend Service**: WebSocket connections for real-time updates
- **Backend Infrastructure ↔ External Systems**: RESTful API calls to the Aerosint Recoater server and OPC UA communication with the PLC

This layered approach follows Clean Architecture principles, ensuring that business logic remains independent of UI frameworks, databases, and external interfaces.


---


## 3. Backend Development


### 3.1 3-Layer Clean Architecture Overview

#### 3.1.1 Architecture Diagram
```mermaid
graph TB
    subgraph "Backend"
        subgraph "Presentation Layer"
            SYSTEM_APIS["System APIs"]
            subgraph "Print APIs"
                CLI_API["CLI API"]
                LAYER_API["Layer API"]
                MM_API["Multi-Material API"]
                JOB_API["Job API"]
                PRINT_DRUM_API["Drum API"]
            end
            subgraph "Recoater Control APIs"
                RECOATER_DRUM_API["Drum API"]
                RECOATER_BLADE_API["Blade API"]
                RECOATER_LEVELER_API["Leveler API"]
            end
        end
        subgraph "Service Layer"
            JOB_MANAGEMENT["Job Management"]
            COMMUNICATION["Communication"]
            MONITORING["Monitoring"]
            OPCUA["OPCUA"]
        end
        subgraph "Infrastructure Layer"
            CLI_EDITOR["CLI Editor"]
            RECOATER_CLIENT["Recoater Client"]
            MOCK_CLIENT["Mock Recoater Client"]
        end
    end

    subgraph "Aerosint Recoater"
        SERVER["Server"]
    end

    subgraph "TwinCAT XAR"
        PLC["PLC"]
    end

RECOATER_CLIENT <--> SERVER

JOB_MANAGEMENT <--> RECOATER_CLIENT
JOB_MANAGEMENT <--> OPCUA
COMMUNICATION <--> RECOATER_CLIENT
MONITORING <--> RECOATER_CLIENT
MONITORING <--> COMMUNICATION
OPCUA <--> PLC

SYSTEM_APIS <--> RECOATER_CLIENT
SYSTEM_APIS <--> OPCUA

CLI_API <--> JOB_MANAGEMENT
CLI_API <--> CLI_EDITOR
LAYER_API <--> JOB_MANAGEMENT
MM_API <--> JOB_MANAGEMENT
JOB_API <--> JOB_MANAGEMENT
PRINT_DRUM_API <--> RECOATER_CLIENT

RECOATER_DRUM_API <--> RECOATER_CLIENT
RECOATER_BLADE_API <--> RECOATER_CLIENT
RECOATER_LEVELER_API <--> RECOATER_CLIENT

```

#### 3.1.2 Architectural Pattern Analysis

The backend implements a **3-Layer Clean Architecture** that separates concerns and ensures business logic independence from external frameworks and systems. This pattern follows the dependency inversion principle where higher layers depend on abstractions, not concretions. Crucially, each layer of the architecture builds upon the services provided by the layer directly beneath it, creating a clear and manageable flow of dependencies.

**Layer 1: Presentation Layer (API Controllers)**
- **Purpose**: Handle HTTP requests and responses, input validation, and error formatting
- **Responsibilities**:
  - Receive and validate incoming HTTP requests
  - Route requests to appropriate services
  - Transform service responses into HTTP responses
- **Dependencies**: Depends on Service Layer interfaces

**Layer 2: Service Layer (Business Logic)**
- **Purpose**: Implement core business rules, workflows, and domain logic
- **Responsibilities**:
  - Orchestrate complex operations (multi-material job workflows)
  - Enforce business rules and validation
  - Coordinate between different domain services
  - Manage application state and transactions
- **Dependencies**: Depends on Infrastructure Layer interfaces

**Layer 3: Infrastructure Layer (External Integrations)**
- **Purpose**: Handles communication with Aerosint Recoater hardware and external CLI file processing capabilities that are not part of the core business logic
- **Responsibilities**:
  - Communicate with Aerosint Recoater hardware
  - Interface with OPC UA PLC systems
  - Process CLI files and generate previews
  - Manage WebSocket connections
- **Dependencies**: No dependencies on other layers (Sends low level requests directly to the Aerosint Recoater server and directly manipulates CLI files)

**Key Benefits:**
- **Testability**: Each layer can be tested independently with mock dependencies
- **Flexibility**: Each layer can be modified or replaced without affecting others
- **Maintainability**: Changes in external systems only affect the Infrastructure Layer
- **Independence**: Business logic remains pure and framework-agnostic


### 3.2 Presentation Layer

The Presentation Layer serves as the bridge between the Vue.js frontend and the backend business logic. When users interact with the web interface (clicking buttons, uploading files, viewing status), the frontend makes HTTP requests to these API endpoints. This layer receives those requests, validates the data, processes it through the appropriate services, and sends back responses that the frontend can display to users.

#### 3.2.1 API Structure and Organization

The backend follows RESTful design principles with clear separation of concerns. Each domain area has its own router module, making the codebase maintainable and scalable.

#### ******* API Architecture Overview:

```mermaid
graph TB
    subgraph "Backend API Endpoints"
        subgraph "System APIs"
            STATUS_GET["/api/status<br/>Get recoater system status"]
            STATUS_HEALTH["/api/status/health<br/>Perform system health check"]
            STATUS_STATE["/api/status/state<br/>Set server state (restart/shutdown)"]
            CONFIG_GET["/api/config<br/>Get recoater configuration"]
            CONFIG_SET["/api/config<br/>Update recoater configuration"]
            ERRORS_CLEAR["/api/errors/clear<br/>Clear all error flags"]
        end

        subgraph "Print APIs"
            subgraph "CLI APIs"
                CLI_UPLOAD["/api/print/cli/upload<br/>Upload CLI for preview"]
                CLI_UPLOAD_DRUM["/api/print/cli/upload/{drum_id}<br/>Upload CLI to specific drum"]
                CLI_PREVIEW["/api/print/cli/{file_id}/layer/{layer_num}/preview<br/>Get layer preview image"]
                CLI_SEND_LAYER["/api/print/cli/{file_id}/layer/{layer_num}/send/{drum_id}<br/>Send single layer to drum"]
                CLI_SEND_RANGE["/api/print/cli/{file_id}/layers/send/{drum_id}<br/>Send layer range to drum"]
            end

            subgraph "Layer APIs"
                LAYER_PARAMS_GET["/api/print/layer/parameters<br/>Get current layer parameters"]
                LAYER_PARAMS_SET["/api/print/layer/parameters<br/>Update layer parameters"]
                LAYER_PREVIEW["/api/print/layer/preview<br/>Get composite layer preview"]
            end

            subgraph "Multi-Material APIs"
                MM_START["/api/print/cli/start-multimaterial-job<br/>Start multi-material print job"]
                MM_STATUS["/api/print/multimaterial-job/status<br/>Get job status"]
                MM_CANCEL["/api/print/multimaterial-job/cancel<br/>Cancel active job"]
                MM_CACHE_STATUS["/api/print/cli/drum-cache-status<br/>Get drum cache status"]
                MM_CLEAR_CACHE["/api/print/cli/clear-drum-cache/{drum_id}<br/>Clear drum cache"]
                MM_DRUM_STATUS["/api/print/multimaterial-job/drum-status/{drum_id}<br/>Get drum status"]
            end

            subgraph "Drum APIs"
                DRUM_PREVIEW["/api/print/drums/{drum_id}/geometry/preview<br/>Get drum geometry preview"]
                DRUM_UPLOAD["/api/print/drums/{drum_id}/geometry<br/>Upload geometry to drum"]
                DRUM_DOWNLOAD["/api/print/drums/{drum_id}/geometry<br/>Download geometry from drum"]
                DRUM_DELETE["/api/print/drums/{drum_id}/geometry<br/>Delete geometry from drum"]
            end

            subgraph "Job APIs"
                JOB_START["/api/print/job<br/>Start print job"]
                JOB_CANCEL["/api/print/job<br/>Cancel print job"]
                JOB_STATUS["/api/print/job/status<br/>Get job status"]
            end
        end

        subgraph "Recoater Control APIs"
            subgraph "Drum APIs"
                DRUM_MOTION_GET["/api/recoater/drums/{drum_id}/motion<br/>Get drum motion status"]
                DRUM_MOTION_SET["/api/recoater/drums/{drum_id}/motion<br/>Set drum motion"]
                DRUM_MOTION_CANCEL["/api/recoater/drums/{drum_id}/motion<br/>Cancel drum motion"]
                DRUM_EJECTION_GET["/api/recoater/drums/{drum_id}/ejection<br/>Get ejection pressure"]
                DRUM_EJECTION_SET["/api/recoater/drums/{drum_id}/ejection<br/>Set ejection pressure"]
                DRUM_SUCTION_GET["/api/recoater/drums/{drum_id}/suction<br/>Get suction pressure"]
                DRUM_SUCTION_SET["/api/recoater/drums/{drum_id}/suction<br/>Set suction pressure"]
            end

            subgraph "Blade APIs"
                BLADE_SCREWS_INFO["/api/recoater/drums/{drum_id}/blade/screws<br/>Get blade screws info"]
                BLADE_SCREWS_MOTION_GET["/api/recoater/drums/{drum_id}/blade/screws/motion<br/>Get blade screws motion"]
                BLADE_SCREWS_MOTION_SET["/api/recoater/drums/{drum_id}/blade/screws/motion<br/>Set blade screws motion"]
                BLADE_SCREWS_MOTION_CANCEL["/api/recoater/drums/{drum_id}/blade/screws/motion<br/>Cancel blade screws motion"]
                BLADE_SCREW_INFO["/api/recoater/drums/{drum_id}/blade/screws/{screw_id}<br/>Get specific screw info"]
                BLADE_SCREW_MOTION_GET["/api/recoater/drums/{drum_id}/blade/screws/{screw_id}/motion<br/>Get specific screw motion"]
                BLADE_SCREW_MOTION_SET["/api/recoater/drums/{drum_id}/blade/screws/{screw_id}/motion<br/>Set specific screw motion"]
                BLADE_SCREW_MOTION_CANCEL["/api/recoater/drums/{drum_id}/blade/screws/{screw_id}/motion<br/>Cancel specific screw motion"]
            end

            subgraph "Leveler APIs"
                LEVELER_PRESSURE_GET["/api/recoater/leveler/pressure<br/>Get leveler pressure"]
                LEVELER_PRESSURE_SET["/api/recoater/leveler/pressure<br/>Set leveler pressure"]
                LEVELER_SENSOR["/api/recoater/leveler/sensor<br/>Get leveler sensor state"]
            end
        end
    end
```
#### ******* API Design Principles

This section explains the key principles that guide how we design and organize our API endpoints.

**1. Domain-Driven Structure (Organizing by Purpose)**

We group related functions together based on the loose categories of system operations, printing tasks, and hardware control:

- **System APIs**: Handle general system operations (checking status, configuration)
- **Print APIs**: Handle everything related to 3D printing (uploading files, managing jobs)
- **Hardware APIs**: Handle direct control of physical hardware (moving drums, adjusting pressure)


**2. RESTful Resource Hierarchy (Logical URL Patterns)**

URLs are structured like a family tree, with broader categories at the top and specific items at the bottom.

*How it works:*
- Start with the general category
- Move to specific items within that category
- End with the action you want to perform

*Example URL breakdown:*
```
/api/recoater/drums/0/blade/screws/2/motion
 │   │        │     │ │     │      │ │
 │   │        │     │ │     │      │ └─ Action: motion control
 │   │        │     │ │     │      └─── Specific screw: #2
 │   │        │     │ │     └─────────── Component: screws
 │   │        │     │ └───────────────── Sub-component: blade
 │   │        │     └─────────────────── Specific drum: #0
 │   │        └───────────────────────── Category: drums
 │   └────────────────────────────────── System: recoater
 └────────────────────────────────────── Base: api
```


**3. Consistent HTTP Verb Mapping (Standard Actions)**

We use standard "verbs" (HTTP methods) to indicate what action we want to perform.

*The four main verbs:*
- **GET**: "Please show me..." (reading/viewing data)
- **POST**: "Please create..." or "Please do..." (creating new things or triggering actions)
- **PUT**: "Please update..." (changing existing data)
- **DELETE**: "Please remove..." (deleting or canceling things)

*Examples:*
```
GET    /api/recoater/drums/0/motion     ← "Show me the drum's current motion status"
POST   /api/recoater/drums/0/motion     ← "Start moving the drum"
DELETE /api/recoater/drums/0/motion     ← "Stop the drum's motion"
PUT    /api/recoater/drums/0/pressure   ← "Change the drum's pressure setting"
```

**4. Dependency Injection (Automatic Resource Management)**

Instead of each endpoint manually connecting to hardware or services, the system automatically provides these connections.

*How it works:*
- Each endpoint declares what it needs (e.g., "I need to talk to the recoater hardware")
- The system automatically provides the right connection
- The same endpoint can work with real hardware or mock hardware for testing

*Example:*
```python
def control_drum(drum_id: int, client: RecoaterClient = Depends(get_recoater_client)):
    # Since our system requires a move_drum() function, we "inject" the client which automatically provides the connection
    return client.move_drum(drum_id)
```

*Why it's helpful:*
- **Consistency**: All endpoints get connections in the same way
- **Flexibility**: Easy to switch between real and test hardware
- **Reliability**: Fewer chances for connection errors
- **Testing**: Easy to test with fake hardware

#### 3.2.2 System APIs

##### ******* Configuration Management API

###### GET /config

Retrieves the current recoater system configuration including build space dimensions, ejection matrix size, drum gaps, and resolution settings. This endpoint provides the frontend Configuration View with all necessary parameters to display current system settings to operators.

**How does it work:**
1. FastAPI dependency injection provides an initialized `RecoaterClient` instance
2. The endpoint calls `recoater_client.get_config()` to fetch raw configuration data from hardware
3. Raw data is validated and structured using the `ConfigurationResponse` Pydantic model
4. Returns JSON response with typed configuration fields or appropriate error status codes

**Simple Sequence Diagram:**
```mermaid
sequenceDiagram
    participant Frontend as ConfigurationView
    participant ConfigRouter as Configuration Router (configuration.py)
    participant Client as RecoaterClient
    participant Hardware as Recoater Hardware

    Frontend->>ConfigRouter: GET /config
    ConfigRouter->>ConfigRouter: get_configuration()
    ConfigRouter->>Client: get_config()
    Client->>Hardware: HTTP GET /config
    Hardware-->>Client: Raw config JSON
    Client-->>ConfigRouter: Configuration data
    ConfigRouter->>ConfigRouter: Validate with ConfigurationResponse
    ConfigRouter-->>Frontend: Structured config JSON
```

**Design Patterns Used and Why:**
- **Dependency Injection Pattern**: Uses `Depends(get_recoater_client)` to automatically provide hardware client, enabling easy testing with mock implementations
- **Data Transfer Object Pattern**: `ConfigurationResponse` model ensures type safety and automatic validation of hardware responses
- **Repository Pattern**: `RecoaterClient` abstracts hardware communication details from the API layer
- **Error Handling Strategy Pattern**: Converts hardware exceptions to appropriate HTTP status codes (503 for connection errors, 502 for API errors)

###### PUT /config

Updates recoater system configuration with new parameter values. Supports partial updates where only specified fields are modified while others remain unchanged. This endpoint enables the frontend Configuration View to save operator changes to system settings.

**How does it work:**
1. Accepts `ConfigurationRequest` payload with optional configuration fields
2. Pydantic validation ensures all provided values meet field constraints (e.g., non-negative numbers)
3. Converts the request model to a dictionary, excluding None values for partial updates
4. Calls `recoater_client.set_config()` to apply changes to hardware
5. Returns success confirmation or appropriate error response

**Simple Sequence Diagram:**
```mermaid
sequenceDiagram
    participant Frontend as ConfigurationView
    participant ConfigRouter as Configuration Router (configuration.py)
    participant Client as RecoaterClient
    participant Hardware as Recoater Hardware

    Frontend->>ConfigRouter: PUT /config (ConfigurationRequest)
    ConfigRouter->>ConfigRouter: set_configuration(config)
    ConfigRouter->>ConfigRouter: Validate request with Pydantic
    ConfigRouter->>ConfigRouter: Convert to dict (exclude_none=True)
    ConfigRouter->>Client: set_config(config_dict)
    Client->>Hardware: HTTP PUT /config
    Hardware-->>Client: Success response
    Client-->>ConfigRouter: Confirmation
    ConfigRouter-->>Frontend: Success JSON response
```

**Design Patterns Used and Why:**
- **Data Transfer Object Pattern**: `ConfigurationRequest` provides type-safe input validation with field constraints (e.g., `ge=0` for non-negative values)
- **Partial Update Pattern**: Uses `model_dump(exclude_none=True)` to support partial configuration updates without overriding unchanged values
- **Model-Dictionary Conversion Pattern**: Converts Pydantic models to dictionaries for compatibility with the underlying hardware API
- **Command Pattern**: Encapsulates configuration update operations with validation, conversion, and hardware communication steps
- **Dependency Injection Pattern**: Manages hardware client lifecycle and enables testing with mock implementations

##### ******* System Status API

###### GET /status

Retrieves comprehensive system status information including recoater hardware status, backend operational state, and connection health. This endpoint provides the frontend Status View with real-time system information while gracefully handling connection failures to ensure the interface remains responsive even when hardware is offline.

**How does it work:**
1. FastAPI dependency injection provides an initialized `RecoaterClient` instance
2. The endpoint attempts to call `client.get_state()` to fetch hardware status
3. If hardware connection succeeds, combines backend and hardware status into unified response
4. If hardware connection fails, returns partial status with backend information and connection error details
5. Returns plain dictionary response with connection status, recoater status, and backend status
6. Never raises exceptions to frontend - always returns structured response with status indicators

**Simple Sequence Diagram:**
```mermaid
sequenceDiagram
    participant Frontend as StatusView
    participant StatusRouter as Status Router (status.py)
    participant Client as RecoaterClient
    participant Hardware as Recoater Hardware

    Frontend->>StatusRouter: GET /status
    StatusRouter->>StatusRouter: get_status()
    StatusRouter->>Client: get_state()

    alt Hardware Available
        Client->>Hardware: HTTP GET /status
        Hardware-->>Client: Hardware status JSON
        Client-->>StatusRouter: Combined status data
        StatusRouter-->>Frontend: Complete status response
    else Hardware Unavailable
        Client->>Hardware: HTTP GET /status
        Hardware-->>Client: Connection error
        Client-->>StatusRouter: Partial status with error
        StatusRouter-->>Frontend: Backend status + error details
    end
```

**Design Patterns Used and Why:**
- **Graceful Degradation Pattern**: Returns partial information when hardware is unavailable rather than failing completely, ensuring frontend remains functional
- **Error Boundary Pattern**: Catches and handles connection exceptions internally, converting them to structured error responses instead of propagating failures
- **Composite Status Pattern**: Combines multiple system components (backend + hardware) into a single unified status response
- **Dependency Injection Pattern**: Uses `Depends(get_recoater_client)` for testable hardware abstraction
- **Plain Dictionary Response Pattern**: Uses simple dictionary format for flexible frontend consumption without strict model constraints

###### GET /status/health

**IMPORTANT:** This endpoint is currently not is use and may be deprecated in future releases.

Provides a lightweight health check endpoint optimized for monitoring systems and automated health checks. Returns simple boolean indicators for both backend and hardware availability without detailed status information, making it safe for frequent polling by monitoring tools.

**How does it work:**
1. FastAPI dependency injection provides the `RecoaterClient` instance
2. Attempts a minimal health check call to `client.health_check()`
3. Evaluates backend health based on service availability and basic functionality
4. Combines backend and hardware health into boolean response indicators
5. Returns plain dictionary response with backend and hardware health indicators
6. Optimized for speed and minimal resource consumption

**Simple Sequence Diagram:**
```mermaid
sequenceDiagram
    participant Monitor as Monitoring System
    participant StatusRouter as Status Router (status.py)
    participant Client as RecoaterClient
    participant Hardware as Recoater Hardware

    Monitor->>StatusRouter: GET /status/health
    StatusRouter->>StatusRouter: health_check()
    StatusRouter->>Client: health_check()
    Client->>Hardware: Minimal health probe

    alt Healthy System
        Hardware-->>Client: OK response
        Client-->>StatusRouter: Health indicators
        StatusRouter-->>Monitor: {"backend": true, "hardware": true}
    else Unhealthy System
        Hardware-->>Client: Error/timeout
        Client-->>StatusRouter: Health indicators
        StatusRouter-->>Monitor: {"backend": true, "hardware": false}
    end
```

**Design Patterns Used and Why:**
- **Health Check Pattern**: Provides standardized health monitoring interface for infrastructure and monitoring tools
- **Lightweight Response Pattern**: Returns minimal data optimized for frequent polling without overwhelming the system
- **Circuit Breaker Pattern**: Quickly fails health checks when hardware is known to be unavailable, preventing resource waste
- **Boolean Indicator Pattern**: Uses simple true/false responses that monitoring systems can easily interpret and alert on
- **Timeout Protection Pattern**: Implements fast timeouts to ensure health checks don't block monitoring systems

###### POST /status/state

Enables controlled restart and shutdown operations on the recoater server system. This endpoint provides operators with the ability to perform maintenance operations and system recovery procedures through the web interface, with proper validation and safety checks.

**How does it work:**
1. Accepts simple `action` string parameter (restart/shutdown)
2. Manual validation in endpoint logic ensures only valid actions are accepted
3. Calls `client.set_state()` with the specified action
4. Hardware performs the requested state change operation
5. Returns confirmation response or appropriate error status
6. Implements proper HTTP status codes for different error conditions

**Simple Sequence Diagram:**
```mermaid
sequenceDiagram
    participant Frontend as DebugView
    participant StatusRouter as Status Router (status.py)
    participant Client as RecoaterClient
    participant Hardware as Recoater Hardware

    Frontend->>StatusRouter: POST /status/state action="restart"
    StatusRouter->>StatusRouter: set_server_state(action)
    StatusRouter->>StatusRouter: Validate action parameter
    StatusRouter->>Client: set_state("restart")
    Client->>Hardware: HTTP POST /status/state

    alt Valid Action
        Hardware-->>Client: State change initiated
        Client-->>StatusRouter: Success confirmation
        StatusRouter-->>Frontend: {"success": true, "message": "Restart initiated"}
    else Invalid Action
        Hardware-->>Client: Error response
        Client-->>StatusRouter: Error details
        StatusRouter-->>Frontend: HTTP 400 with error message
    end
```

**Design Patterns Used and Why:**
- **Command Pattern**: Encapsulates server state change operations as discrete commands with validation and execution logic
- **State Machine Pattern**: Manages valid state transitions (running → restarting → running) with proper validation
- **Parameter Validation Pattern**: Uses manual parameter validation logic instead of Pydantic models for simple string validation
- **Error Response Pattern**: Returns descriptive error messages and appropriate HTTP status codes for different failure scenarios
- **Safety Check Pattern**: Validates actions before execution to prevent invalid or dangerous state changes

##### ******* Error Management API

###### POST /errors/clear

Provides unified error clearing functionality for both backend service errors and OPC UA variable states. This endpoint enables operators to recover from error conditions through the web interface by clearing error flags across all system components, including coordinated error recovery with the multi-material job service.

**How does it work:**
1. FastAPI dependency injection provides `MultiMaterialJobService` instance
2. Calls `job_manager.clear_all_error_flags()` to clear all system error states
3. This method clears both backend job service errors and OPC UA error variables hosted by our backend
4. Updates OPC UA variables `backend_error` and `plc_error` to `False` in our hosted OPC UA server
5. Returns `MultiMaterialJobResponse` with success/failure status and descriptive message
6. Provides unified error recovery through single service method

**Simple Sequence Diagram:**
```mermaid
sequenceDiagram
    participant Frontend as ErrorModal/PrintView
    participant ErrorsRouter as Errors Router (errors.py)
    participant JobService as MultiMaterialJobService
    participant OPCUAService as Backend OPC UA Server

    Note over OPCUAService: Before: backend_error=True, plc_error=True

    Frontend->>ErrorsRouter: POST /errors/clear
    ErrorsRouter->>ErrorsRouter: clear_errors()
    ErrorsRouter->>JobService: clear_all_error_flags()
    JobService->>JobService: Clear backend job errors
    JobService->>OPCUAService: clear_error_flags()
    OPCUAService->>OPCUAService: Set backend_error = False
    OPCUAService->>OPCUAService: Set plc_error = False
    OPCUAService-->>JobService: Error flags cleared
    JobService-->>ErrorsRouter: MultiMaterialJobResponse
    ErrorsRouter-->>Frontend: {"success": true, "message": "All error flags cleared"}

    Note over OPCUAService: After: backend_error=False, plc_error=False
```

**Design Patterns Used and Why:**
- **Unified Service Pattern**: Uses single service method to handle all error clearing operations, simplifying the API interface
- **Delegation Pattern**: Delegates complex error clearing logic to specialized service layer rather than handling in API endpoint
- **Error Recovery Pattern**: Implements systematic approach to error state recovery across multiple system components
- **Dependency Injection Pattern**: Uses injected `MultiMaterialJobService` for testable error management operations
- **Response Model Pattern**: Returns structured `MultiMaterialJobResponse` for consistent API response format
- **Internal Variable Management Pattern**: Backend manages OPC UA variables internally without external PLC communication dependency

#### 3.2.3 Print Job Management APIs

The Print Job Management APIs provide comprehensive functionality for managing 3D printing operations in the Aerosint Recoater system. These APIs handle everything from CLI file upload and processing to coordinated multi-material printing workflows. The system supports both legacy single-drum operations and modern multi-material workflows with sophisticated layer-by-layer coordination.

##### ******* CLI File Processing API

The CLI File Processing API provides comprehensive functionality for uploading, parsing, caching, and managing CLI files in both preview and production workflows. This API supports both generic file uploads for preview purposes and drum-specific uploads for the multi-material printing workflow.

###### POST /cli/upload

Uploads and parses a CLI file for **preview-only** operations without associating it with any specific drum. This endpoint is designed for development, testing, and file validation scenarios where users want to examine CLI content before committing to a production workflow.

**How does it work:**
1. Accepts `.cli` file uploads through FastAPI's `UploadFile` interface with validation
2. Reads file content asynchronously and validates it's not empty
3. Uses `asyncio.to_thread()` to offload CPU-intensive CLI parsing to avoid blocking the event loop
4. Generates unique UUID-based `file_id` for caching and subsequent reference operations
5. Stores parsed CLI file in generic cache through `MultiMaterialJobService.add_cli_file()`
6. Returns comprehensive metadata including layer count, file size, and generated file ID

**Simple Sequence Diagram:**
```mermaid
sequenceDiagram
    participant Frontend as FileUploadColumn
    participant APIRouter as CLI Router (cli.py)
    participant JobService as MultiMaterialJobService
    participant Parser as CLI Editor

    Note over JobService: cli_cache = {}
    Frontend->>APIRouter: POST /cli/upload
    APIRouter->>APIRouter: upload_cli_file_for_preview(file)
    APIRouter->>APIRouter: Validate file type and size
    APIRouter->>Parser: asyncio.to_thread(parse)
    Parser-->>APIRouter: ParsedCliFile object
    APIRouter->>JobService: add_cli_file(file_id, parsed, filename)
    JobService->>JobService: Store in self.cli_cache[file_id]
    Note over JobService: cli_cache = {file_id: CliCacheEntry}
    JobService-->>APIRouter: Success
    APIRouter-->>Frontend: CliUploadResponse with metadata
```

**Design Patterns Used and Why:**
- **Asynchronous Processing Pattern**: Uses `asyncio.to_thread()` to prevent CLI parsing from blocking the event loop, ensuring responsive API performance
- **UUID Generation Pattern**: Generates unique file identifiers to prevent conflicts and enable reliable file reference
- **Generic Caching Pattern**: Stores files in generic cache separate from production drum cache, enabling flexible preview operations
- **Input Validation Pattern**: Validates file extensions and content before processing to prevent invalid data from entering the system
- **Response Model Pattern**: Returns structured `CliUploadResponse` with comprehensive metadata for frontend consumption

###### POST /cli/upload/drum_id

Uploads and caches a CLI file for a specific drum (0, 1, or 2) in the production multi-material workflow. This is the primary endpoint for production printing operations where each drum requires its own material-specific CLI file.

**How does it work:**
1. Validates `drum_id` parameter using FastAPI Path constraints (0-2 range)
2. Accepts `.cli` file upload with file type and content validation
3. Performs CPU-intensive CLI parsing in a separate thread using `asyncio.to_thread()`
4. Caches parsed file specifically for the target drum using `cache_cli_file_for_drum()`
5. Associates the file with drum hardware for subsequent printing operations
6. Returns production-ready response with simplified `file_id` format (`drum_{drum_id}`)

**Simple Sequence Diagram:**
```mermaid
sequenceDiagram
    participant Frontend as MultiLayerJobControl
    participant APIRouter as CLI Router (cli.py)
    participant JobService as MultiMaterialJobService
    participant Parser as CLI Editor

    Note over JobService: drum_cli_cache = {0: None, 1: None, 2: None}
    Frontend->>APIRouter: POST /cli/upload/0
    APIRouter->>APIRouter: upload_cli_file_to_drum(drum_id=0, file)
    APIRouter->>APIRouter: Validate drum_id (0-2) and file
    APIRouter->>Parser: asyncio.to_thread(parse)
    Parser-->>APIRouter: ParsedCliFile object
    APIRouter->>JobService: cache_cli_file_for_drum(0, parsed, filename)
    JobService->>JobService: Store in self.drum_cli_cache[0]
    Note over JobService: drum_cli_cache[0] = {parsed_file, layer_count, ...}
    JobService-->>APIRouter: Success
    APIRouter-->>Frontend: CliUploadResponse with drum reference
```

**Design Patterns Used and Why:**
- **Drum-Specific Caching Pattern**: Uses separate cache structure for production workflow, ensuring proper drum-to-file associations
- **Path Parameter Validation**: Employs FastAPI Path constraints to validate drum ID ranges at the API layer
- **Production Workflow Pattern**: Separates production caching from preview caching to prevent accidental mixing of workflows
- **Resource Association Pattern**: Creates direct association between CLI files and hardware drums for printing operations

###### GET /cli/{file_id}/layer/{layer_num}/preview

Generates and returns a PNG preview image of a specific layer from a previously uploaded CLI file. This endpoint enables operators to visually verify layer content before printing and supports both 1-based user-friendly layer numbering and internal 0-based indexing conversion. To get preview image of the composite layer (all drums combined), use the `/layer/preview` endpoint instead.

**How does it work:**
1. Retrieves cached CLI file using the provided `file_id` from generic cache
2. Validates the requested `layer_num` against available layers (1-based user input)
3. Converts 1-based layer numbering to 0-based internal indexing for array access
4. Extracts the target layer object from the parsed CLI file data structure
5. Uses CLI editor's `render_layer_to_png()` method to generate PNG image data
6. Returns binary PNG response with appropriate headers for browser display

**Simple Sequence Diagram:**
```mermaid
sequenceDiagram
    participant Frontend as Layer Preview Component
    participant APIRouter as CLI Router (cli.py)
    participant JobService as MultiMaterialJobService
    participant Parser as CLI Editor

    Note over JobService: cli_cache = {file_id: CliCacheEntry}
    Frontend->>APIRouter: GET /cli/{file_id}/layer/{layer_num}/preview
    APIRouter->>APIRouter: get_cli_layer_preview(file_id, layer_num)
    APIRouter->>JobService: get_cli_file_with_metadata(file_id)
    JobService->>JobService: Retrieve from self.cli_cache[file_id]
    Note over JobService: Returns ParsedCliFile with metadata
    JobService-->>APIRouter: Cached file data
    APIRouter->>APIRouter: Convert layer 5 to index 4
    APIRouter->>Parser: render_layer_to_png(layer[4])
    Parser-->>APIRouter: PNG binary data
    APIRouter-->>Frontend: PNG Response with headers
```

**Design Patterns Used and Why:**
- **Layer Indexing Conversion Pattern**: Handles conversion between user-friendly 1-based numbering and internal 0-based indexing
- **Binary Response Pattern**: Returns binary PNG data with appropriate media type and Content-Disposition headers
- **Cache Retrieval Pattern**: Efficiently retrieves cached files without re-parsing for performance
- **Visual Validation Pattern**: Provides visual feedback to operators for quality assurance

###### POST /cli/{file_id}/layer/{layer_num}/send/drum_id

Caches a single layer from a preview-cached CLI file to a specific drum’s cache. This is intended for selective distribution/testing before starting a multi‑material job. It does not upload to hardware; the multi‑material job orchestrator will handle per‑layer uploads during execution.

**How does it work:**
1. Retrieves the cached CLI file from generic cache using `file_id`
2. Validates the layer number and converts to 0‑based indexing
3. Extracts the target layer from parsed CLI
4. Generates a single‑layer ASCII CLI with proper headers
5. Parses and stores it in the drum‑specific cache (layerCount = 1)
6. Returns a response with layer metadata; no hardware upload occurs

**Simple Sequence Diagram:**
```mermaid
sequenceDiagram
    participant Frontend as PrintView.vue
    participant CLIRouter as CLI Router (cli.py)
    participant JobService as MultiMaterialJobService
    participant Parser as CLI Editor

    Frontend->>CLIRouter: POST /cli/{file_id}/layer/{layer_num}/send/{drum_id}
    CLIRouter->>CLIRouter: send_cli_layer_to_drum(file_id, layer_num, drum_id)
    CLIRouter->>JobService: get_cli_file_with_metadata(file_id)
    JobService-->>CLIRouter: Cached ParsedCliFile
    CLIRouter->>CLIRouter: Extract layer[2] (0-based)
    CLIRouter->>Parser: generate_single_layer_ascii_cli(layer, headers)
    Parser-->>CLIRouter: Single‑layer CLI bytes
    CLIRouter->>JobService: cache_cli_file_for_drum(1, parsed_single)
    CLIRouter-->>Frontend: Success with layer metadata (cache‑only)
```

**Design Patterns Used and Why:**
- **Single Layer Extraction Pattern**: Generates isolated layer CLI data while preserving header information
- **Dual Cache Strategy**: Maintains both preview cache and drum cache for workflow consistency
- **Separation of Concerns**: Hardware uploads are delegated to the multi‑material job orchestrator

###### POST /cli/{file_id}/layers/send/{drum_id}

Caches a range of consecutive layers from a preview‑cached CLI file to a specific drum’s cache. This prepares drum‑specific partial CLIs; it does not upload to hardware. The multi‑material job will upload layers to hardware during execution.

**How does it work:**
1. Accepts `LayerRangeRequest` with start and end layer numbers (1‑based)
2. Validates the range and converts to 0‑based indices
3. Extracts the layer slice from the parsed CLI
4. Generates an ASCII CLI containing only the selected range
5. Parses and stores it in the drum‑specific cache
6. Returns detailed response with range information and Z‑height data; no hardware upload occurs

**Simple Sequence Diagram:**
```mermaid
sequenceDiagram
    participant Frontend as PrintView.vue
    participant CLIRouter as CLI Router (cli.py)
    participant JobService as MultiMaterialJobService
    participant Parser as CLI Editor

    Frontend->>CLIRouter: POST /cli/{file_id}/layers/send/{drum_id}
    CLIRouter->>CLIRouter: send_cli_layer_range_to_drum(file_id, drum_id, body)
    CLIRouter->>CLIRouter: Validate range (5 <= 10, <= total_layers)
    CLIRouter->>JobService: get_cli_file_with_metadata(file_id)
    JobService-->>CLIRouter: Cached ParsedCliFile
    CLIRouter->>CLIRouter: Extract layers[4:10] (0-based slice)
    CLIRouter->>Parser: generate_ascii_cli_from_layer_range(layers, headers)
    Parser-->>CLIRouter: Multi‑layer CLI bytes
    CLIRouter->>JobService: cache_cli_file_for_drum(2, parsed_trimmed)
    CLIRouter-->>Frontend: Success with range and Z‑height info (cache‑only)
```

**Design Patterns Used and Why:**
- **Range Validation Pattern**: Ensures logical consistency of layer ranges before processing
- **Slice Extraction Pattern**: Uses Python slice notation for efficient consecutive layer extraction
- **Multi‑Layer CLI Generation**: Maintains CLI format integrity while processing layer ranges
- **Z‑Height Tracking Pattern**: Preserves Z‑height information for layer positioning verification
- **Dual Cache Strategy**: Maintains both preview cache and drum cache for workflow consistency
- **Separation of Concerns**: Hardware uploads are performed by the multi‑material job workflow

##### ******* Multi-Material Job Orchestration API

The Multi-Material Job Orchestration API provides sophisticated coordination for complex multi-drum printing operations. This API implements a comprehensive workflow that manages layer-by-layer printing coordination, OPC UA communication, and real-time status monitoring.

###### POST /cli/start-multimaterial-job

Initiates a coordinated multi-material print job using cached CLI files from multiple drums. This endpoint implements sophisticated layer-by-layer orchestration with OPC UA coordination and real-time monitoring.

**How does it work:**
1. Validates that at least one drum has cached CLI files using `has_cached_files()`
2. Creates an asynchronous background task using `asyncio.create_task()` for long-running job execution
3. Stores task reference in `_background_task` for proper cancellation management
4. Returns immediately while the job runs in the background
5. Background task executes `start_layer_by_layer_job()` which implements the full 7-variable OPC UA workflow
6. Job processes each layer sequentially: upload → print → wait for completion → advance

**Simple Sequence Diagram:**
```mermaid
sequenceDiagram
    participant Frontend as PrintView.vue
    participant MultiMaterialRouter as Multimaterial Router (multimaterial.py)
    participant JobService as MultiMaterialJobService
    participant Background as Background Task
    participant OPCUACoordinator as OPC UA System
    participant Hardware as Recoater Hardware

    Frontend->>MultiMaterialRouter: POST /cli/start-multimaterial-job
    MultiMaterialRouter->>MultiMaterialRouter: start_multimaterial_job()
    MultiMaterialRouter->>JobService: has_cached_files()
    JobService-->>MultiMaterialRouter: Validation result

    alt Cache validation successful
        MultiMaterialRouter->>JobService: asyncio.create_task(start_layer_by_layer_job)
        JobService->>Background: Background task created
        MultiMaterialRouter-->>Frontend: Immediate response (job started)

        Background->>OPCUACoordinator: Setup job (job_active=True, total_layers=N)
        Background->>Background: current_layer = 1

        loop For each layer (1 to N)
            Background->>Background: Check cancellation flag

            alt Job not cancelled
                Background->>OPCUACoordinator: Reset layer flags (ready=False, complete=False)

                par Upload to all drums with delay
                    Background->>Hardware: Upload layer to Drum 0
                    Background->>Background: Wait 2s delay
                and
                    Background->>Hardware: Upload layer to Drum 1
                    Background->>Background: Wait 2s delay
                and
                    Background->>Hardware: Upload layer to Drum 2
                end

                Background->>OPCUACoordinator: Signal ready to print (ready=True)
                Background->>Hardware: Start print job
                Background->>Background: Poll for completion

                loop Wait for layer completion
                    Background->>Background: Check cancellation + error flags

                    alt Layer completed successfully
                        Background->>OPCUACoordinator: Signal layer complete (complete=True)
                        Background->>OPCUACoordinator: Update progress (current_layer++)
                        note over Background: Break from completion loop
                    else Error detected (backend_error OR plc_error)
                        Background->>Background: Pause layer processing
                        Background->>Background: Wait for error flags to clear
                        note over Background: Retry layer from upload step
                    else Job cancelled
                        Background->>Background: Exit layer loop gracefully
                        note over Background: Break from completion loop
                    end

                    Background->>Background: Sleep polling interval
                end
            else Job cancelled
                Background->>Background: Exit layer loop
                note over Background: Break from layer loop
            end
        end

        alt Job completed successfully
            Background->>Background: Mark job completed
            Background->>JobService: Clear drum cache
            Background->>Background: Clear current_job reference
        else Job cancelled or error
            Background->>Background: Handle cancellation/error cleanup
        end

        Background->>OPCUACoordinator: Cleanup (job_active=False, reset flags)
    else Cache validation failed
        MultiMaterialRouter-->>Frontend: HTTP 400 - No CLI files cached
    end
```

**Design Patterns Used and Why:**
- **Asynchronous Task Pattern**: Uses `asyncio.create_task()` to run long-duration jobs without blocking API responses
- **Background Processing Pattern**: Enables immediate API response while complex workflows run in background
- **Task Reference Management**: Stores task references for proper cancellation and cleanup
- **OPC UA Coordination Pattern**: Implements 7-variable coordination protocol for PLC communication
- **Sequential Layer Processing**: Processes layers one at a time to ensure proper hardware coordination

###### GET /multimaterial-job/status

Retrieves comprehensive status information for the currently active multi-material print job. This endpoint provides real-time monitoring data including job progress, current layer, layer counts, and execution state for operator dashboard display.

**How does it work:**
1. FastAPI dependency injection provides the `MultiMaterialJobService` instance
2. Calls `job_manager.get_job_status()` to retrieve current job state
3. Returns structured status data including job state, progress percentage, current/total layers
4. Handles cases where no job is active by returning appropriate null/default values
5. Provides real-time data for frontend progress indicators and status displays
6. Includes job timing information and execution statistics

**Simple Sequence Diagram:**
```mermaid
sequenceDiagram
    participant Frontend as PrintView.vue
    participant StatusRouter as Multimaterial Router (multimaterial.py)
    participant JobService as MultiMaterialJobService
    participant JobState as Current Job State

    Frontend->>StatusRouter: GET /multimaterial-job/status
    StatusRouter->>StatusRouter: get_multimaterial_job_status()
    StatusRouter->>JobService: get_job_status()
    JobService->>JobState: Read current job state
    JobState-->>JobService: Job progress and status data
    JobService-->>StatusRouter: MultiMaterialJobStatusResponse
    StatusRouter-->>Frontend: {status: "running", current_layer: 15, total_layers: 50, progress: 30%}
```

**Design Patterns Used and Why:**
- **Status Polling Pattern**: Enables real-time frontend updates through periodic status polling without WebSocket complexity
- **State Snapshot Pattern**: Returns point-in-time snapshot of job state for consistent frontend display
- **Progress Tracking Pattern**: Provides both absolute (layer numbers) and relative (percentage) progress indicators
- **Null Object Pattern**: Returns safe default values when no job is active to prevent frontend errors
- **Response Model Pattern**: Uses structured `MultiMaterialJobStatusResponse` for type-safe status data

###### POST /multimaterial-job/cancel

Cancels the currently executing multi-material print job and performs proper cleanup operations. This endpoint provides operators with emergency stop capability and graceful job termination functionality through the web interface.

**How does it work:**
1. FastAPI dependency injection provides the `MultiMaterialJobService` instance
2. Calls `job_manager.cancel_job()` to initiate job cancellation process
3. Service sets job cancellation flags and waits for current layer to complete safely
4. Performs cleanup operations including OPC UA state reset and cache management
5. Returns cancellation confirmation with success/failure status and descriptive message
6. Ensures hardware is left in safe state after cancellation

**Simple Sequence Diagram:**
```mermaid
sequenceDiagram
    participant Frontend as PrintView.vue
    participant CancelRouter as Multimaterial Router (multimaterial.py)
    participant JobService as MultiMaterialJobService
    participant Background as Background Task
    participant OPCUAService as OPC UA System

    Frontend->>CancelRouter: POST /multimaterial-job/cancel
    CancelRouter->>CancelRouter: cancel_multimaterial_job()
    CancelRouter->>JobService: cancel_job()
    JobService->>JobService: Set cancellation flags
    JobService->>Background: Wait for safe cancellation point
    Background->>Background: Complete current layer safely
    Background->>OPCUAService: Cleanup OPC UA state
    Background->>Background: Clear job state
    JobService-->>CancelRouter: Cancellation result
    CancelRouter-->>Frontend: {success: true, message: "Job cancelled successfully"}
```

**Design Patterns Used and Why:**
- **Graceful Shutdown Pattern**: Allows current layer to complete before cancellation to prevent hardware damage
- **Safe State Pattern**: Ensures hardware is left in predictable, safe state after cancellation
- **Background Task Management**: Properly handles cancellation of long-running background tasks
- **Operator Safety Pattern**: Provides immediate cancellation capability for emergency situations
- **Cleanup Coordination Pattern**: Coordinates cleanup across multiple system components (OPC UA, hardware, cache)

##### ******* Cache Management API

The Cache Management API provides comprehensive functionality for managing CLI file caches used in multi-material printing workflows. These endpoints enable operators to monitor cache status, clear cache data, and ensure proper workflow state management.

###### GET /cli/drum-cache-status

Retrieves comprehensive status information about cached CLI files for all three drums in the multi-material workflow. This endpoint provides essential data for workflow validation and operator verification before starting print jobs.

**How does it work:**
1. FastAPI dependency injection provides the `MultiMaterialJobService` instance
2. Iterates through all drum IDs (0, 1, 2) to check cache status
3. For each drum, calls `job_manager.get_cached_file_for_drum()` to retrieve cache data
4. Compiles comprehensive status including filenames, layer counts, and cache presence
5. Calculates maximum layers across all drums for workflow planning
6. Returns readiness indicators based on cache availability for job start validation

**Simple Sequence Diagram:**
```mermaid
sequenceDiagram
    participant Frontend as MultiLayerJobControl
    participant CacheRouter as Multimaterial Router (multimaterial.py)
    participant JobService as MultiMaterialJobService
    participant DrumCache as Drum Cache Storage

    Frontend->>CacheRouter: GET /cli/drum-cache-status
    CacheRouter->>CacheRouter: get_drum_cache_status()

    loop For each drum (0, 1, 2)
        CacheRouter->>JobService: get_cached_file_for_drum(drum_id)
        JobService->>DrumCache: Check drum_cli_cache[drum_id]
        DrumCache-->>JobService: Cache data or None
        JobService-->>CacheRouter: Filename, layer_count, cached status
    end

    CacheRouter->>JobService: has_cached_files()
    JobService-->>CacheRouter: Overall cache availability
    CacheRouter-->>Frontend: {drums: {0: {...}, 1: {...}, 2: {...}}, max_layers: 50, ready_to_start: true}
```

**Design Patterns Used and Why:**
- **Comprehensive Status Pattern**: Provides complete overview of all drum cache states in single API call
- **Workflow Validation Pattern**: Includes readiness indicators to prevent invalid job start attempts
- **Iterative Collection Pattern**: Systematically collects status from all drums for unified response
- **Maximum Calculation Pattern**: Determines workflow parameters (max layers) from cache analysis
- **Cache Transparency Pattern**: Exposes cache internals for operator verification and troubleshooting

###### POST /cli/clear-drum-cache/{drum_id}

Clears cached CLI file data for a specific drum (0, 1, or 2) in the multi-material workflow. This endpoint enables selective cache management for individual drums without affecting other drum caches or active jobs.

**How does it work:**
1. Validates `drum_id` parameter using FastAPI Path constraints (0-2 range)
2. FastAPI dependency injection provides the `MultiMaterialJobService` instance
3. Calls `job_manager.clear_drum_cache_for_drum(drum_id)` to clear specific drum cache
4. Service sets the drum cache entry to None, freeing memory and resetting state
5. Returns confirmation response with success status and descriptive message
6. Does not affect hardware files, only clears backend cache memory

**Simple Sequence Diagram:**
```mermaid
sequenceDiagram
    participant Frontend as MultiLayerJobControl
    participant ClearRouter as Multimaterial Router (multimaterial.py)
    participant JobService as MultiMaterialJobService
    participant DrumCache as Drum Cache Storage

    Note over DrumCache: Before: drum_cli_cache[1] = {filename, parsed_file, layer_count}

    Frontend->>ClearRouter: POST /cli/clear-drum-cache/1
    ClearRouter->>ClearRouter: clear_drum_cache_for_drum(drum_id=1)
    ClearRouter->>ClearRouter: Validate drum_id (0-2)
    ClearRouter->>JobService: clear_drum_cache_for_drum(1)
    JobService->>DrumCache: Set drum_cli_cache[1] = None
    DrumCache-->>JobService: Cache cleared
    JobService-->>ClearRouter: Success confirmation
    ClearRouter-->>Frontend: {success: true, message: "Cleared cached CLI file for drum 1"}

    Note over DrumCache: After: drum_cli_cache[1] = None
```

**Design Patterns Used and Why:**
- **Selective Clearing Pattern**: Allows targeted cache management without affecting other drums
- **Path Parameter Validation**: Uses FastAPI constraints to ensure valid drum ID ranges
- **Memory Management Pattern**: Frees memory by setting cache entries to None
- **Non-Destructive Pattern**: Only affects backend cache, preserves hardware files
- **Confirmation Response Pattern**: Provides clear feedback about cache clearing operation

###### GET /multimaterial-job/drum-status/{drum_id}

Retrieves detailed status information for a specific drum within the context of an active multi-material print job. This endpoint provides drum-specific monitoring data including layer progress, material status, and drum-specific error conditions.

**How does it work:**
1. Validates `drum_id` parameter using FastAPI Path constraints (0-2 range)
2. FastAPI dependency injection provides the `MultiMaterialJobService` instance
3. Calls `job_manager.get_drum_status(drum_id)` to retrieve drum-specific job status
4. Returns structured `DrumStatusResponse` with drum-specific progress and state data
5. Handles cases where no active job exists or drum is not found with appropriate 404 errors
6. Provides detailed drum monitoring for active job execution

**Simple Sequence Diagram:**
```mermaid
sequenceDiagram
    participant Frontend as DrumStatusComponent
    participant StatusRouter as Multimaterial Router (multimaterial.py)
    participant JobService as MultiMaterialJobService
    participant JobState as Current Job State

    Frontend->>StatusRouter: GET /multimaterial-job/drum-status/2
    StatusRouter->>StatusRouter: get_drum_status(drum_id=2)
    StatusRouter->>StatusRouter: Validate drum_id (0-2)
    StatusRouter->>JobService: get_drum_status(2)
    JobService->>JobState: Get drum-specific status from job

    alt Active job with drum 2
        JobState-->>JobService: Drum status data
        JobService-->>StatusRouter: DrumStatusResponse
        StatusRouter-->>Frontend: {drum_id: 2, status: "active", current_layer: 15, ...}
    else No active job or drum not found
        JobService-->>StatusRouter: None
        StatusRouter-->>Frontend: HTTP 404 - No active job or drum not found
    end
```

**Design Patterns Used and Why:**
- **Drum-Specific Monitoring Pattern**: Provides targeted status information for individual drum operations
- **Context-Aware Status Pattern**: Returns status within context of active job execution
- **Error Handling Pattern**: Uses appropriate HTTP 404 for missing job/drum conditions
- **Structured Response Pattern**: Uses typed `DrumStatusResponse` for consistent drum status data
- **Job Context Pattern**: Requires active job context for meaningful drum status information

**Note on Missing Endpoint:**
The service layer includes a `clear_drum_cache()` method for clearing all drum caches simultaneously, but the corresponding `POST /cli/clear-drum-cache` endpoint (without drum_id) is not implemented in the API router. This represents a gap between service capabilities and API surface area that may be addressed in future releases.

##### ******* Drum Management APIs

###### GET /drums/{drum_id}/geometry
Retrieves the current geometry file from the specified drum directly from the recoater hardware as a PNG image. Useful for verifying what is currently loaded on a drum.

**How does it work:**
1. Validates `drum_id` with FastAPI `Path` constraints (0-2 range)
2. FastAPI dependency injection provides a `RecoaterClient` instance
3. Calls the client method to download the drum geometry from hardware
4. Returns binary PNG data with `image/png` media type and inline filename header
5. Translates hardware/API errors to HTTP 400/503/500 as appropriate
6. Operates directly on hardware (does not use cache)

**Simple Sequence Diagram:**
```mermaid
sequenceDiagram
    participant Frontend as DebugView/Tools
    participant DrumRouter as Drum Router (drum.py)
    participant Client as RecoaterClient
    participant Hardware as Recoater Hardware

    Frontend->>DrumRouter: GET /drums/{id}/geometry
    DrumRouter->>DrumRouter: download_drum_geometry(drum_id)
    DrumRouter->>Client: download geometry
    Client->>Hardware: HTTP GET /drums/{id}/geometry
    Hardware-->>Client: PNG bytes
    Client-->>DrumRouter: PNG bytes
    DrumRouter-->>Frontend: image/png
```

**Design Patterns Used and Why:**
- **Path Parameter Validation**: Ensures only valid drum IDs (0–2)
- **Hardware Abstraction**: `RecoaterClient` hides HTTP details from the API layer
- **Binary Response Pattern**: Returns PNG with proper headers for browser rendering
- **Error Translation**: Converts hardware errors into HTTP exceptions
- **Dependency Injection**: Testable and swappable real vs. mock clients

###### GET /drums/{drum_id}/geometry/preview
Generates a PNG preview for a specific layer from the cached CLI associated with the specified drum. This helps validate per‑drum content without hitting hardware.

**How does it work:**
1. Validates `drum_id` and `layer` (1-based, default 1)
2. Uses `MultiMaterialJobService` to fetch cached, parsed CLI for the drum
3. Converts user `layer` to 0-based index and selects the layer
4. Renders that layer to PNG with `cli_parser.render_layer_to_png(..., drum_id)`
5. Returns binary PNG with inline filename
6. Returns HTTP 404 if no CLI is cached for that drum

**Simple Sequence Diagram:**
```mermaid
sequenceDiagram
    participant Frontend as PrintView (Preview)
    participant DrumRouter as Drum Router (drum.py)
    participant JobService as MultiMaterialJobService
    participant Parser as CLI Parser

    Frontend->>DrumRouter: GET /drums/{id}/geometry/preview
    DrumRouter->>JobService: get_cached_file_for_drum(id)
    JobService-->>DrumRouter: Parsed file + metadata
    DrumRouter->>DrumRouter: Get specified layer for preview
    DrumRouter->>DrumRouter: Validate layer number
    DrumRouter->>Parser: render_layer_to_png(layer-1, drum_id)
    Parser-->>DrumRouter: PNG bytes
    DrumRouter-->>Frontend: image/png
```

**Design Patterns Used and Why:**
- **Cache Retrieval Pattern**: Uses drum‑specific cache, not hardware
- **Layer Index Conversion**: Converts 1-based input to 0-based internal indexing
- **Binary Response Pattern**: Returns rendered PNG
- **Dependency Injection**: Injected `MultiMaterialJobService`
- **Error Handling Pattern**: 404 when no cached file exists for the drum

###### POST /drums/{drum_id}/geometry
Uploads a geometry file (CLI/PNG) to the specified drum on the hardware. Intended for direct hardware testing or maintenance.

**How does it work:**
1. Validates `drum_id` (0–2) and accepts `UploadFile`
2. Reads file bytes and determines content type (defaults to `application/octet-stream`)
3. Calls `recoater_client.upload_drum_geometry(drum_id, file_data, content_type)`
4. Hardware saves/activates the uploaded geometry for that drum
5. Returns `FileUploadResponse` on success; maps errors to HTTP codes
6. Does not update cache automatically (hardware‑only operation)

**Simple Sequence Diagram:**
```mermaid
sequenceDiagram
    participant Frontend as DebugView/Tools
    participant DrumRouter as Drum Router (drum.py)
    participant Client as RecoaterClient
    participant Hardware as Recoater Hardware

    Frontend->>DrumRouter: POST /drums/{id}/geometry
    DrumRouter->>DrumRouter: Read file with await file.read()
    DrumRouter->>Client: upload_drum_geometry(id, bytes, type)
    Client->>Hardware: HTTP POST /drums/{id}/geometry
    Hardware-->>Client: OK
    Client-->>DrumRouter: OK
    DrumRouter-->>Frontend: { success: true, drum_id }
```

**Design Patterns Used and Why:**
- **Command Pattern**: Encapsulates upload as a single operation
- **Content-Type Handling**: Preserves/infers content type for hardware compatibility
- **Dependency Injection**: Testable client wiring
- **Path Parameter Validation**: Enforces 3‑drum limit
- **Error Translation**: Maps connection/API errors to HTTP status

###### DELETE /drums/{drum_id}/geometry
Deletes the geometry file currently stored on the specified drum on the hardware.

**How does it work:**
1. Validates `drum_id` (0–2)
2. Uses injected `RecoaterClient` to call `delete_drum_geometry(drum_id)`
3. Hardware removes the geometry associated with the drum
4. Returns `FileDeleteResponse` with success message
5. Translates connection/API errors to proper HTTP responses
6. Does not alter cached CLI files

**Simple Sequence Diagram:**
```mermaid
sequenceDiagram
    participant Frontend as DebugView/Tools
    participant DrumRouter as Drum Router (drum.py)
    participant Client as RecoaterClient
    participant Hardware as Recoater Hardware

    Frontend->>DrumRouter: DELETE /drums/{id}/geometry
    DrumRouter->>Client: delete_drum_geometry(id)
    Client->>Hardware: HTTP DELETE /drums/{id}/geometry
    Hardware-->>Client: OK
    Client-->>DrumRouter: OK
    DrumRouter-->>Frontend: { success: true, message: "deleted" }
```

**Design Patterns Used and Why:**
- **Command Pattern**: Single, side‑effecting delete action
- **Dependency Injection**: Client abstraction for hardware control
- **Error Translation**: Consistent HTTP error mapping
- **Separation of Concerns**: Hardware state changes do not mutate preview caches

##### ******* Layer Parameter & Preview APIs

###### GET /layer/parameters
Retrieves the current layer printing parameters from the recoater hardware (e.g., filling_id, speed, x_offset, powder_saving). Useful for confirming or initializing UI controls.

**How does it work:**
1. Dependency injection provides a `RecoaterClient` instance
2. Calls `recoater_client.get_layer_parameters()`
3. Maps raw hardware response into `LayerParametersResponse`
4. Returns JSON with typed fields for the frontend
5. Translates connection/API issues into HTTP 503/400/500 errors
6. Keeps API thin with validation in the client/model layer

**Simple Sequence Diagram:**
```mermaid
sequenceDiagram
    participant Frontend as Configuration/PrintView
    participant LayerRouter as Layer Router (layer.py)
    participant Client as RecoaterClient

    Frontend->>LayerRouter: GET /layer/parameters
    LayerRouter->>Client: get_layer_parameters()
    Client-->>LayerRouter: { filling_id, speed, x_offset, powder_saving }
    LayerRouter-->>Frontend: LayerParametersResponse
```

**Design Patterns Used and Why:**
- **DTO Pattern**: `LayerParametersResponse` provides typed, validated output
- **Dependency Injection**: Hardware client supplied via `Depends`
- **Error Translation**: Consistent mapping to HTTP status codes
- **Thin Controller**: Endpoint delegates to client for logic and validation

###### PUT /layer/parameters
Updates the current layer printing parameters on the hardware. Supports safe, validated updates to speed, offsets, fill modes, and powder-saving options.

**How does it work:**
1. Accepts `LayerParametersRequest` JSON with typed fields and constraints
2. Logs and validates payload via Pydantic model
3. Calls `recoater_client.set_layer_parameters(...)` with provided fields
4. Hardware applies parameter changes immediately/asynchronously
5. Returns simple success JSON with the parameters echoed back
6. Maps any client/hardware failures to appropriate HTTP responses

**Simple Sequence Diagram:**
```mermaid
sequenceDiagram
    participant Frontend as Configuration/PrintView
    participant LayerRouter as Layer Router (layer.py)
    participant Client as RecoaterClient

    Frontend->>LayerRouter: PUT /layer/parameters { speed, x_offset, ... }
    LayerRouter->>LayerRouter: Validate LayerParametersRequest
    LayerRouter->>Client: set_layer_parameters(...)
    Client-->>LayerRouter: OK
    LayerRouter-->>Frontend: { success: true, parameters: {...} }
```

**Design Patterns Used and Why:**
- **DTO Pattern**: Strongly-typed request/response models
- **Command Pattern**: Encapsulates a parameter-update operation
- **Input Validation**: Pydantic enforces ranges and types
- **Dependency Injection**: Testable client boundary

###### GET /layer/preview
Generates a composite PNG preview for a specific layer, overlaying all cached drum CLIs (drums 0/1/2). Helpful to visually verify multi-material composition before printing.

**How does it work:**
1. Accepts `layer` query param (1-based, default 1)
2. Uses `MultiMaterialJobService` to inspect caches and compute max layers
3. Returns 404 if no drum CLIs are cached
4. For the requested layer, collects each drums layer (if present) and overlays
5. Uses CLI parser to render the composite PNG (with drum color coding)
6. Returns PNG bytes with inline filename and `image/png` content type

**Simple Sequence Diagram:**
```mermaid
sequenceDiagram
    participant Frontend as LayerPreview
    participant LayerRouter as Layer Router (layer.py)
    participant JobService as MultiMaterialJobService
    participant Parser as CLI Parser

    Frontend->>LayerRouter: GET /layer/preview, {layer=5}
    LayerRouter->>JobService: get_max_layers()
    JobService-->>LayerRouter: max_layers=n

    alt No cached CLIs (n == 0)
        LayerRouter-->>Frontend: HTTP 404 "No CLI files cached"
    else Layer out of range (5 > n)
        LayerRouter-->>Frontend: HTTP 400 "Layer exceeds maximum"
    else Valid request (1 <= 5 <= n)
        LayerRouter->>LayerRouter: drum_layers = {}
        Note over LayerRouter: Initialize drum_layers = {0: None, 1: None, 2: None}
        loop For each drum_id in [0,1,2]
            LayerRouter->>JobService: get_cached_file_for_drum(drum_id)
            JobService-->>LayerRouter: drum_data or None

            alt Drum has requested layer
                LayerRouter->>LayerRouter: target_layer = parsed_file.layers[5-1]
                Note over LayerRouter: Convert 1-based to 0-based index
                LayerRouter->>LayerRouter: drum_layers[drum_id] = target_layer
            else Missing or too few layers
                LayerRouter->>LayerRouter: drum_layers[drum_id] = None
            end
        end

        LayerRouter->>Parser: render_composite_layer_to_png(drum_layers)
        Parser-->>LayerRouter: PNG bytes
        LayerRouter-->>Frontend: 200 image/png (inline filename)
    end
```

**Design Patterns Used and Why:**
- **Composite Rendering Pattern**: Overlays layers from multiple drums into a single image
- **Layer Index Conversion**: User-friendly 1-based inputs, internal 0-based indexing
- **Binary Response Pattern**: PNG output optimized for browser display
- **Dependency Injection**: Injected job service and parser enable testing
- **Graceful Errors**: Clear 404 when caches are empty

##### ******* Job Management APIs
The Job Management APIs provide direct control over print job operations on the recoater hardware. These endpoints allow starting, cancelling, and querying the status of print jobs. While deprecated in favor of the Multi-Material Job Orchestration APIs for normal operations, they remain available for debugging and direct hardware control.

**IMPORTANT** Any use of these endpoints requires manual operation and coordination with the PLC system. They do not implement the full multi-material workflow and will only print the bottom-most layer of any uploaded CLI file.

###### POST /job
Starts a new print job on the recoater hardware without automated layer iteration. Crucially, this means that only the bottom-most layer of any CLI file will ever be printed. Deprecated in favor of the Multi-Material Job Orchestration APIs for normal print operations, but still available in DebugView for direct control/testing.

**How does it work:**
1. Dependency injection provides `RecoaterClient`
2. Calls `recoater_client.start_print_job()`
3. Hardware/server creates a new job and returns a `job_id`
4. Endpoint wraps the result as `PrintJobResponse` with status "started"
5. Returns HTTP 409 if a job is already active or cannot start
6. Maps connection/API errors to 503/400 as appropriate

**Simple Sequence Diagram:**
```mermaid
sequenceDiagram
    participant Frontend as PrintView/Tools
    participant JobRouter as Job Router (job.py)
    participant Client as RecoaterClient
    participant Hardware as Recoater Hardware

    Frontend->>JobRouter: POST /job
    JobRouter->>Client: start_print_job()
    Client->>Client: _make_request("POST", "/print/job")
    Client->>Hardware: HTTP POST /job
    Hardware-->>Client: { job_id }
    Client-->>JobRouter: { job_id }
    JobRouter-->>Frontend: { success: true, status: "started", job_id }
```

**Design Patterns Used and Why:**
- **Dependency Injection**: Clean hardware abstraction via client
- **Command Pattern**: Initiates a single start operation
- **Response Model Pattern**: Typed `PrintJobResponse`
- **Error Translation**: Proper HTTP mapping, including 409 conflict

###### DELETE /job
Cancels the currently active print job on the hardware. Deprecated in favor of Multi-Material Job cancellation.

**How does it work:**
1. Injects `RecoaterClient`
2. Calls `recoater_client.cancel_print_job()`
3. Hardware cancels/clears the active job state
4. Returns `PrintJobResponse` with status "cancelled"
5. Maps connection/API errors to 503/400/500 accordingly

**Simple Sequence Diagram:**
```mermaid
sequenceDiagram
    participant Frontend as PrintView/Tools
    participant JobRouter as Job Router (job.py)
    participant Client as RecoaterClient
    participant Hardware as Recoater Hardware

    Frontend->>JobRouter: DELETE /job
    JobRouter->>Client: cancel_print_job()
    Client->>Hardware: HTTP DELETE /job
    Hardware-->>Client: OK
    Client-->>JobRouter: OK
    JobRouter-->>Frontend: { success: true, status: "cancelled" }
```

**Design Patterns Used and Why:**
- **Command Pattern**: Single, idempotent cancel operation
- **Dependency Injection**: Swappable client for testing
- **Error Translation**: Consistent HTTP error mapping
- **Response Model Pattern**: Structured confirmation

###### GET /job/status
Retrieves the current job status from the recoater hardware. Deprecated in favor of the Multi-Material Job status endpoints.

**How does it work:**
1. Injects `RecoaterClient`
2. Calls `recoater_client.get_print_job_status()`
3. Maps hardware response to `PrintJobStatusResponse`
4. Returns typed JSON with relevant job fields
5. Maps connection/API errors to 503/400/500 accordingly
6. Read-only and safe to poll at low frequency

**Simple Sequence Diagram:**
```mermaid
sequenceDiagram
    participant Frontend as PrintView/Tools
    participant JobRouter as Job Router (job.py)
    participant Client as RecoaterClient

    Frontend->>JobRouter: GET /job/status
    JobRouter->>Client: get_print_job_status()
    Client-->>JobRouter: { status, progress, ... }
    JobRouter-->>Frontend: PrintJobStatusResponse
```

**Design Patterns Used and Why:**
- **DTO Pattern**: `PrintJobStatusResponse` for typed outputs
- **Dependency Injection**: Client boundary for easy mocking
- **Error Translation**: Predictable HTTP status codes on failure
- **Thin Controller**: Delegates to client for business logic


#### 3.2.4 Recoater Hardware Control APIs

The Recoater Hardware Control APIs provide direct control interfaces for the physical components of the Aerosint Recoater system. These APIs enable precise manipulation of drum movement, pressure systems, blade positioning, and leveler operations. They serve as the bridge between the web interface and the industrial hardware, allowing operators to control the recoater through intuitive frontend components that translate to precise hardware commands.

##### ******* Drum Control API

The Drum Control API manages the three critical aspects of drum operations: rotational motion, ejection pressure for powder distribution, and suction pressure for powder retention. These APIs enable precise control of material handling during the multi-material printing process.

###### GET /drums/{drum_id}/motion

Retrieves the current motion status and parameters for a specific drum. This endpoint provides real-time information about drum position, movement state, and active motion commands to support operator monitoring and system coordination.

**How does it work:**
1. FastAPI path validation ensures `drum_id` is within valid range (0-2)
2. Dependency injection provides an initialized `RecoaterClient` instance
3. Client calls `get_drum_motion(drum_id)` to fetch current motion status from hardware
4. Response includes motion state, position, speed, and movement mode information
5. Returns structured response with connection status and motion details
6. Error handling converts hardware exceptions to appropriate HTTP status codes

**Simple Sequence Diagram:**
```mermaid
sequenceDiagram
    participant Frontend as RecoaterView
    participant DrumRouter as Drum Router (drum.py)
    participant Client as RecoaterClient (client.py)
    participant Hardware as Recoater Hardware

    Frontend->>DrumRouter: GET /drums/0/motion
    DrumRouter->>DrumRouter: get_drum_motion(drum_id=0)
    DrumRouter->>DrumRouter: Validate drum_id (0-2)
    DrumRouter->>Client: get_drum_motion(0)
    Client->>Hardware: HTTP GET /drums/0/motion
    Hardware-->>Client: Motion status JSON
    Client-->>DrumRouter: Motion data
    DrumRouter-->>Frontend: {drum_id: 0, motion: {...}, connected: true}
```

**Design Patterns Used and Why:**
- **Path Parameter Validation Pattern**: Uses FastAPI `Path` constraints to validate drum ID ranges (0-2) at the API layer
- **Hardware Abstraction Pattern**: `RecoaterClient` abstracts hardware communication details from the API endpoint
- **Connection Status Reporting Pattern**: Always includes connection status in responses for frontend health monitoring
- **Error Translation Pattern**: Converts hardware-specific exceptions to standardized HTTP status codes (503 for connection errors, 400 for API errors)
- **Dependency Injection Pattern**: Enables testable hardware client management and easy mock substitution

###### POST /drums/{drum_id}/motion

Executes motion commands for drum rotation with support for multiple movement modes. This endpoint accepts motion parameters and translates them into hardware-specific commands for precise drum positioning and movement control.

**How does it work:**
1. Validates `drum_id` parameter and `DrumMotionRequest` payload using Pydantic
2. Motion request includes mode (absolute, relative, turns, speed, homing), speed, and optional distance/turns
3. Calls `client.set_drum_motion()` with validated parameters
4. Hardware executes the motion command based on specified mode and parameters
5. Returns command acknowledgment with motion parameters and hardware response
6. Supports multiple motion modes for different operational requirements

**Simple Sequence Diagram:**
```mermaid
sequenceDiagram
    participant Frontend as RecoaterView
    participant DrumRouter as Drum Router (drum.py)
    participant Client as RecoaterClient (client.py)
    participant Hardware as Recoater Hardware

    Frontend->>DrumRouter: POST /drums/1/motion {mode: "relative", speed: 10, distance: 50}
    DrumRouter->>DrumRouter: set_drum_motion(motion_request, drum_id=1)
    DrumRouter->>DrumRouter: Validate DrumMotionRequest
    DrumRouter->>Client: set_drum_motion(1, "relative", 10, 50)
    Client->>Hardware: HTTP POST /drums/1/motion
    Hardware-->>Client: Command acknowledgment
    Client-->>DrumRouter: Motion response
    DrumRouter-->>Frontend: {drum_id: 1, motion_command: {...}, response: {...}}
```

**Design Patterns Used and Why:**
- **Command Pattern**: Encapsulates motion commands with mode, speed, and distance parameters for flexible operation
- **Multi-Mode Operation Pattern**: Supports different motion modes (absolute, relative, turns, speed, homing) through single endpoint
- **Parameter Validation Pattern**: Uses Pydantic models to validate motion parameters and constraints before hardware execution
- **Response Echo Pattern**: Returns both the original command parameters and hardware response for confirmation
- **Speed Constraint Pattern**: Validates speed parameters to ensure safe hardware operation

###### DELETE /drums/{drum_id}/motion

Provides emergency stop functionality for drum motion commands. This endpoint immediately cancels any active motion to ensure operator safety and prevent hardware damage during emergency situations.

**How does it work:**
1. Validates `drum_id` parameter for target drum identification
2. Calls `client.cancel_drum_motion(drum_id)` to send stop command
3. Hardware immediately halts current motion and clears motion queue
4. Returns cancellation confirmation with action type and hardware response
5. Ensures immediate response for safety-critical operations
6. Maintains connection status for frontend error handling

**Simple Sequence Diagram:**
```mermaid
sequenceDiagram
    participant Frontend as RecoaterView
    participant DrumRouter as Drum Router (drum.py)
    participant Client as RecoaterClient (client.py)
    participant Hardware as Recoater Hardware

    Frontend->>DrumRouter: DELETE /drums/2/motion (Emergency Stop)
    DrumRouter->>DrumRouter: cancel_drum_motion(drum_id=2)
    DrumRouter->>Client: cancel_drum_motion(2)
    Client->>Hardware: HTTP DELETE /drums/2/motion
    Hardware-->>Client: Motion cancelled immediately
    Client-->>DrumRouter: Cancellation confirmation
    DrumRouter-->>Frontend: {drum_id: 2, action: "motion_cancelled", response: {...}}
```

**Design Patterns Used and Why:**
- **Emergency Stop Pattern**: Provides immediate motion cancellation for safety-critical situations
- **Action Confirmation Pattern**: Returns explicit action type ("motion_cancelled") for clear operation feedback
- **Immediate Response Pattern**: Prioritizes fast response time for emergency operations
- **Safety First Pattern**: No validation delays - immediately forwards cancellation to hardware

###### GET /drums/{drum_id}/ejection

Monitors ejection pressure status for powder distribution control. This endpoint provides current and target pressure readings in configurable units to support different operational requirements and operator preferences.

**How does it work:**
1. Validates `drum_id` parameter and optional `unit` query parameter (pascal/bar)
2. Calls `client.get_drum_ejection(drum_id, unit)` to fetch pressure data
3. Hardware returns current pressure, target pressure, and pressure limits
4. Response includes pressure values in requested units with conversion handling
5. Supports both Pascal and Bar units for international compatibility
6. Provides pressure status for powder flow monitoring and control

**Simple Sequence Diagram:**
```mermaid
sequenceDiagram
    participant Frontend as RecoaterView
    participant DrumRouter as Drum Router (drum.py)
    participant Client as RecoaterClient (client.py)
    participant Hardware as Recoater Hardware

    Frontend->>DrumRouter: GET /drums/0/ejection?unit=bar
    DrumRouter->>DrumRouter: get_drum_ejection(drum_id=0, unit="bar")
    DrumRouter->>Client: get_drum_ejection(0, "bar")
    Client->>Hardware: HTTP GET /drums/0/ejection
    Hardware-->>Client: Pressure data (converted to bar)
    Client-->>DrumRouter: Ejection pressure info
    DrumRouter-->>Frontend: {drum_id: 0, ejection: {current: 2.5, target: 3.0, unit: "bar"}}
```

**Design Patterns Used and Why:**
- **Unit Conversion Pattern**: Supports multiple pressure units (Pascal, Bar) for international compatibility
- **Query Parameter Pattern**: Uses optional query parameters for unit specification with sensible defaults
- **Pressure Monitoring Pattern**: Provides comprehensive pressure status including current, target, and limits
- **Hardware Abstraction Pattern**: Client handles unit conversion logic, keeping API interface clean

###### PUT /drums/{drum_id}/ejection

Controls ejection pressure settings for powder distribution during printing operations. This endpoint accepts pressure targets with unit specifications and applies them to the hardware for precise material flow control.

**How does it work:**
1. Validates `DrumEjectionRequest` payload with target pressure and unit specification
2. Pydantic validation ensures non-negative pressure values and valid units
3. Calls `client.set_drum_ejection()` with target pressure and unit parameters
4. Hardware adjusts ejection pressure system to reach target value
5. Returns command echo and hardware response for confirmation
6. Supports pressure range validation for safe operation

**Simple Sequence Diagram:**
```mermaid
sequenceDiagram
    participant Frontend as RecoaterView
    participant DrumRouter as Drum Router (drum.py)
    participant Client as RecoaterClient (client.py)
    participant Hardware as Recoater Hardware

    Frontend->>DrumRouter: PUT /drums/1/ejection {target: 25000, unit: "pascal"}
    DrumRouter->>DrumRouter: set_drum_ejection(ejection_request, drum_id=1)
    DrumRouter->>DrumRouter: Validate DrumEjectionRequest
    DrumRouter->>Client: set_drum_ejection(1, 25000, "pascal")
    Client->>Hardware: HTTP PUT /drums/1/ejection
    Hardware-->>Client: Pressure adjustment initiated
    Client-->>DrumRouter: Ejection response
    DrumRouter-->>Frontend: {drum_id: 1, ejection_command: {...}, response: {...}}
```

**Design Patterns Used and Why:**
- **Pressure Control Pattern**: Provides precise pressure adjustment for material flow control
- **Unit Specification Pattern**: Allows pressure specification in multiple units for operational flexibility
- **Safety Validation Pattern**: Validates pressure ranges to prevent dangerous operating conditions
- **Command Echo Pattern**: Returns both command parameters and hardware response for operation verification
###### GET /drums/{drum_id}/suction

Retrieves the current suction pressure information for a specific drum. Suction helps retain powder during recoating by applying negative pressure.

**How does it work:**
1. Validates `drum_id` using FastAPI `Path` constraints (0-2 range)
2. Dependency injection provides the `RecoaterClient` instance
3. Calls `client.get_drum_suction(drum_id)` to fetch suction data
4. Returns structured JSON containing current suction pressure and connection status
5. Translates connection/API errors to 503/400 and unexpected issues to 500
6. Read-only, safe for periodic monitoring

**Simple Sequence Diagram:**
```mermaid
sequenceDiagram
    participant Frontend as RecoaterView
    participant DrumRouter as Drum Router (drum.py)
    participant Client as RecoaterClient (client.py)
    participant Hardware as Recoater Hardware

    Frontend->>DrumRouter: GET /drums/0/suction
    DrumRouter->>DrumRouter: get_drum_suction(drum_id=0)
    DrumRouter->>Client: get_drum_suction(0)
    Client->>Hardware: HTTP GET /drums/0/suction
    Hardware-->>Client: Suction pressure JSON
    Client-->>DrumRouter: Suction data
    DrumRouter-->>Frontend: {drum_id: 0, suction: {...}, connected: true}
```

**Design Patterns Used and Why:**
- **Path Parameter Validation Pattern**: Ensures valid drum IDs (0-2)
- **Hardware Abstraction Pattern**: `RecoaterClient` isolates hardware specifics
- **Error Translation Pattern**: Consistent HTTP status codes on failures
- **Monitoring Pattern**: Safe, read-only status retrieval

###### PUT /drums/{drum_id}/suction

Sets the target suction pressure for a specific drum. This controls powder retention during the coating process.

**How does it work:**
1. Validates `DrumSuctionRequest` payload (target pressure)
2. Injects `RecoaterClient` and calls `client.set_drum_suction(drum_id, target)`
3. Hardware adjusts suction system toward the target value
4. Returns echoed command parameters and hardware response
5. Maps connection/API errors to 503/400 and unexpected issues to 500
6. Enforces drum ID range (0-2) via `Path` constraints

**Simple Sequence Diagram:**
```mermaid
sequenceDiagram
    participant Frontend as RecoaterView
    participant DrumRouter as Drum Router (drum.py)
    participant Client as RecoaterClient (client.py)
    participant Hardware as Recoater Hardware

    Frontend->>DrumRouter: PUT /drums/1/suction {target: 12000}
    DrumRouter->>DrumRouter: set_drum_suction(suction_request, drum_id=1)
    DrumRouter->>DrumRouter: Validate DrumSuctionRequest
    DrumRouter->>Client: set_drum_suction(1, 12000)
    Client->>Hardware: HTTP PUT /drums/1/suction
    Hardware-->>Client: Adjustment initiated
    Client-->>DrumRouter: Suction response
    DrumRouter-->>Frontend: {drum_id: 1, suction_command: {target: 12000}, response: {...}}
```

**Design Patterns Used and Why:**
- **Command Pattern**: Encapsulates suction adjustment operation
- **Input Validation Pattern**: Pydantic enforces safe ranges/types
- **Dependency Injection Pattern**: Testable client boundary
- **Action Confirmation Pattern**: Returns both command and response

##### ******* Leveler Control API

The Leveler Control API manages the powder spreading mechanism that ensures uniform layer thickness across the build platform. This system controls pressure application and sensor monitoring for consistent powder distribution quality.

###### GET /leveler/pressure

Retrieves comprehensive leveler pressure information including maximum, target, and current pressure values. This endpoint provides essential data for powder surface quality control and leveler system monitoring.

**How does it work:**
1. Dependency injection provides initialized `RecoaterClient` instance
2. Calls `client.get_leveler_pressure()` to fetch comprehensive pressure data
3. Hardware returns maximum pressure capability, target pressure, and current pressure
4. Response includes all pressure values for complete system status
5. Provides data for powder spreading quality assessment
6. Supports leveler system health monitoring

**Simple Sequence Diagram:**
```mermaid
sequenceDiagram
    participant Frontend as RecoaterView
    participant LevelerRouter as Leveler Router (leveler.py)
    participant Client as RecoaterClient (client.py)
    participant Hardware as Recoater Hardware

    Frontend->>LevelerRouter: GET /leveler/pressure
    LevelerRouter->>LevelerRouter: get_leveler_pressure()
    LevelerRouter->>Client: get_leveler_pressure()
    Client->>Hardware: HTTP GET /leveler/pressure
    Hardware-->>Client: Pressure data (max, target, current)
    Client-->>LevelerRouter: Leveler pressure info
    LevelerRouter-->>Frontend: {leveler_pressure: {maximum: 50000, target: 30000, value: 29500}}
```

**Design Patterns Used and Why:**
- **Comprehensive Status Pattern**: Returns maximum, target, and current values for complete system visibility
- **Quality Control Pattern**: Provides pressure data essential for powder spreading quality assessment
- **System Health Pattern**: Enables monitoring of leveler system performance and status
- **Pascal Standard Pattern**: Uses consistent Pascal units for all pressure measurements

###### PUT /leveler/pressure

Sets target leveling pressure for powder spreading operations. This endpoint adjusts the pressure applied during powder distribution to achieve uniform layer thickness and surface quality.

**How does it work:**
1. Validates `LevelerPressureRequest` payload with target pressure in Pascal
2. Pydantic validation ensures non-negative pressure values within safe ranges
3. Calls `client.set_leveler_pressure()` with target pressure parameter
4. Hardware adjusts leveler pressure system to reach specified target
5. Returns command parameters and hardware response for verification
6. Affects powder spreading uniformity and layer quality

**Simple Sequence Diagram:**
```mermaid
sequenceDiagram
    participant Frontend as RecoaterView
    participant LevelerRouter as Leveler Router (leveler.py)
    participant Client as RecoaterClient (client.py)
    participant Hardware as Recoater Hardware

    Frontend->>LevelerRouter: PUT /leveler/pressure {target: 35000}
    LevelerRouter->>LevelerRouter: set_leveler_pressure(pressure_request)
    LevelerRouter->>LevelerRouter: Validate LevelerPressureRequest
    LevelerRouter->>Client: set_leveler_pressure(35000)
    Client->>Hardware: HTTP PUT /leveler/pressure
    Hardware-->>Client: Pressure adjustment initiated
    Client-->>LevelerRouter: Leveler response
    LevelerRouter-->>Frontend: {leveler_command: {target: 35000}, response: {...}}
```

**Design Patterns Used and Why:**
- **Quality Control Pattern**: Enables precise pressure adjustment for optimal powder spreading
- **Safety Validation Pattern**: Validates pressure ranges to prevent equipment damage
- **Command Verification Pattern**: Returns both command and response for operation confirmation
- **Uniform Distribution Pattern**: Controls pressure for consistent layer thickness

###### GET /leveler/sensor

Retrieves the current state of the leveler’s magnetic sensor. This indicates whether the leveler is engaged/detected, supporting diagnostics and alignment checks.

**How does it work:**
1. Dependency injection provides `RecoaterClient`
2. Calls `client.get_leveler_sensor()` to read sensor state
3. Returns JSON containing sensor status and connection flag
4. Maps connection/API errors to 503/400 and unexpected issues to 500
5. Read-only, safe to poll periodically
6. Complements pressure endpoints for full leveler monitoring

**Simple Sequence Diagram:**
```mermaid
sequenceDiagram
    participant Frontend as RecoaterView
    participant LevelerRouter as Leveler Router (leveler.py)
    participant Client as RecoaterClient (client.py)

    Frontend->>LevelerRouter: GET /leveler/sensor
    LevelerRouter->>Client: get_leveler_sensor()
    Client-->>LevelerRouter: { state: ... }
    LevelerRouter-->>Frontend: { leveler_sensor: {...}, connected: true }
```

**Design Patterns Used and Why:**
- **Hardware Abstraction Pattern**: Client shields API from hardware details
- **Error Translation Pattern**: Predictable HTTP codes on failure
- **Monitoring Pattern**: Lightweight, read-only status retrieval

##### ******* Blade Control API

The Blade Control API manages the scraping blade system that removes excess powder from the drum surface after each layer. This system provides both collective control for synchronized operations and individual screw control for precise blade angle adjustment.

###### GET /drums/{drum_id}/blade/screws

Retrieves comprehensive information about both screws of the specified drum's scraping blade. This endpoint provides position data, operational limits, and status information for complete blade system monitoring.

**How does it work:**
1. Validates `drum_id` parameter for target drum identification
2. Calls `client.get_blade_screws_info(drum_id)` to fetch comprehensive screw data
3. Hardware returns information for both screws including position, limits, and status
4. Response includes data for both screws of the blade system
5. Provides complete blade system status for operational monitoring
6. Supports blade positioning and limit verification

**Simple Sequence Diagram:**
```mermaid
sequenceDiagram
    participant Frontend as RecoaterView
    participant BladeRouter as Blade Router (blade.py)
    participant Client as RecoaterClient (client.py)
    participant Hardware as Recoater Hardware

    Frontend->>BladeRouter: GET /drums/0/blade/screws
    BladeRouter->>BladeRouter: get_blade_screws_info(drum_id=0)
    BladeRouter->>Client: get_blade_screws_info(0)
    Client->>Hardware: HTTP GET /drums/0/blade/screws
    Hardware-->>Client: Both screws data (position, limits, status)
    Client-->>BladeRouter: Blade screws info
    BladeRouter-->>Frontend: {drum_id: 0, blade_screws: [{screw_0: {...}}, {screw_1: {...}}]}
```

**Design Patterns Used and Why:**
- **Collective Information Pattern**: Returns data for both screws in a single response for efficiency
- **Comprehensive Status Pattern**: Provides position, limits, and status for complete system visibility
- **Blade System Pattern**: Treats both screws as components of unified blade system
- **Hardware Abstraction Pattern**: Client handles complex screw data aggregation

###### GET /drums/{drum_id}/blade/screws/motion

Retrieves the current collective motion command/status for the blade screws on a drum. Useful for checking if a synchronous move is underway.

**How does it work:**
1. Validates `drum_id` (0-2) via `Path` constraints
2. Injects `RecoaterClient`
3. Calls `client.get_blade_screws_motion(drum_id)`
4. Returns motion parameters/status and connection flag
5. Maps connection/API errors to 503/400; unexpected to 500

**Simple Sequence Diagram:**
```mermaid
sequenceDiagram
    participant Frontend as RecoaterView
    participant BladeRouter as Blade Router (blade.py)
    participant Client as RecoaterClient
    Frontend->>BladeRouter: GET /drums/0/blade/screws/motion
    BladeRouter->>Client: get_blade_screws_motion(0)
    Client-->>BladeRouter: { mode: ..., distance: ..., state: ... }
    BladeRouter-->>Frontend: {drum_id: 0, motion: {...}, connected: true}
```

**Design Patterns Used and Why:**
- **Status Query Pattern** for in-progress operations
- **Error Translation Pattern** for consistent failures

###### POST /drums/{drum_id}/blade/screws/motion

Creates a collective motion command for both blade screws (synchronous movement).

**How does it work:**
1. Validates `BladeMotionRequest` payload (`mode`, `distance`)
2. Calls `client.set_blade_screws_motion(drum_id, mode, distance)`
3. Hardware starts synchronized move across both screws
4. Returns echoed command and hardware response
5. Errors translated to 503/400/500 consistently

**Simple Sequence Diagram:**
```mermaid
sequenceDiagram
    participant Frontend as RecoaterView
    participant BladeRouter as Blade Router (blade.py)
    participant Client as RecoaterClient
    Frontend->>BladeRouter: POST /drums/1/blade/screws/motion {mode: linear, distance: 2.0}
    BladeRouter->>Client: set_blade_screws_motion(1, linear, 2.0)
    Client-->>BladeRouter: Response
    BladeRouter-->>Frontend: {motion_command: {...}, response: {...}}
```

**Design Patterns Used and Why:**
- **Command Pattern** encapsulates movement
- **Action Confirmation Pattern** echoes command and response

###### DELETE /drums/{drum_id}/blade/screws/motion

Cancels any active collective blade screws motion for the specified drum.

**How does it work:**
1. Validates `drum_id` and injects `RecoaterClient`
2. Calls `client.cancel_blade_screws_motion(drum_id)`
3. Returns action indicator and hardware response
4. Errors mapped to 503/400/500

**Simple Sequence Diagram:**
```mermaid
sequenceDiagram
    participant Frontend as RecoaterView
    participant BladeRouter as Blade Router (blade.py)
    participant Client as RecoaterClient
    Frontend->>BladeRouter: DELETE /drums/2/blade/screws/motion
    BladeRouter->>Client: cancel_blade_screws_motion(2)
    Client-->>BladeRouter: Response
    BladeRouter-->>Frontend: {action: motion_cancelled, response: {...}}
```

**Design Patterns Used and Why:**
- **Cancellation Pattern** for safe stop of in-flight commands
- **Error Translation Pattern** standardizes failures

###### GET /drums/{drum_id}/blade/screws/{screw_id}

Retrieves detailed information (position, limits, status) for a specific blade screw.

**How does it work:**
1. Validates `drum_id` and `screw_id`
2. Calls `client.get_blade_screw_info(drum_id, screw_id)`
3. Returns screw-specific info and connection status
4. Errors mapped to 503/400/500

**Simple Sequence Diagram:**
```mermaid
sequenceDiagram
    participant Frontend as RecoaterView
    participant BladeRouter as Blade Router (blade.py)
    participant Client as RecoaterClient
    Frontend->>BladeRouter: GET /drums/0/blade/screws/1
    BladeRouter->>Client: get_blade_screw_info(0, 1)
    Client-->>BladeRouter: Screw data
    BladeRouter-->>Frontend: {screw_info: {...}}
```

**Design Patterns Used and Why:**
- **Fine-Grained Control Pattern** for individual component status

###### GET /drums/{drum_id}/blade/screws/{screw_id}/motion

Retrieves the current motion command/status for a single blade screw.

**How does it work:**
1. Validates parameters and injects client
2. Calls `client.get_blade_screw_motion(drum_id, screw_id)`
3. Returns motion status for that screw
4. Errors mapped to 503/400/500

**Simple Sequence Diagram:**
```mermaid
sequenceDiagram
    participant Frontend as RecoaterView
    participant BladeRouter as Blade Router (blade.py)
    participant Client as RecoaterClient
    Frontend->>BladeRouter: GET /drums/0/blade/screws/1/motion
    BladeRouter->>Client: get_blade_screw_motion(0, 1)
    Client-->>BladeRouter: Motion data
    BladeRouter-->>Frontend: {motion: {...}}
```

**Design Patterns Used and Why:**
- **Status Query Pattern** for targeted component

###### POST /drums/{drum_id}/blade/screws/{screw_id}/motion

Creates a motion command for an individual blade screw (independent of its pair).

**How does it work:**
1. Validates `BladeIndividualMotionRequest` (`distance`)
2. Calls `client.set_blade_screw_motion(drum_id, screw_id, distance)`
3. Hardware actuates the specific screw
4. Returns echoed command and response
5. Errors mapped to 503/400/500

**Simple Sequence Diagram:**
```mermaid
sequenceDiagram
    participant Frontend as RecoaterView
    participant BladeRouter as Blade Router (blade.py)
    participant Client as RecoaterClient
    Frontend->>BladeRouter: POST /drums/2/blade/screws/0/motion {distance: -1.5}
    BladeRouter->>Client: set_blade_screw_motion(2, 0, -1.5)
    Client-->>BladeRouter: Response
    BladeRouter-->>Frontend: {motion_command: {...}, response: {...}}
```

**Design Patterns Used and Why:**
- **Command Pattern** per-component control
- **Action Confirmation Pattern** echoes command

###### DELETE /drums/{drum_id}/blade/screws/{screw_id}/motion

Cancels any active motion for a specific blade screw.

**How does it work:**
1. Validates parameters and injects client
2. Calls `client.cancel_blade_screw_motion(drum_id, screw_id)`
3. Returns action indicator and response
4. Errors mapped to 503/400/500

**Simple Sequence Diagram:**
```mermaid
sequenceDiagram
    participant Frontend as RecoaterView
    participant BladeRouter as Blade Router (blade.py)
    participant Client as RecoaterClient
    Frontend->>BladeRouter: DELETE /drums/1/blade/screws/1/motion
    BladeRouter->>Client: cancel_blade_screw_motion(1, 1)
    Client-->>BladeRouter: Response
    BladeRouter-->>Frontend: {action: motion_cancelled, response: {...}}
```

**Design Patterns Used and Why:**
- **Cancellation Pattern** for safe stop
- **Error Translation Pattern** for predictable failures

### 3.3 Service Layer (Business Logic)

#### 3.3.1 Communication

Purpose and why it lives here
- The Communication service provides real-time pub/sub over WebSockets to push status updates to the frontend. It manages connections, client subscriptions, and efficient broadcasting.
- It belongs in the Service Layer because it coordinates application behavior (what data to send, who should receive it, and at what cadence) and depends on infrastructure (FastAPI WebSocket objects), but it’s not a pure transport client nor a presentation concern.

##### Class Diagram
```mermaid
classDiagram
        class WebSocketConnectionManager {
            +List~WebSocket~ active_connections
            +Dict~WebSocket, Set~str~~ connection_subscriptions
            +connect(websocket) async
            +disconnect(websocket)
            +update_subscription(websocket, data_types)
            +broadcast(message) async
            -_filter_message_for_connection(websocket, message) Dict
            +get_required_data_types() Set~str~
            +connection_count int
            +has_connections bool
        }

        class StatusPollingService {
            <<uses>>
        }

        WebSocketConnectionManager <.. StatusPollingService : used by
```

##### Example Workflows
Real-time status broadcast to subscribed clients
```mermaid
sequenceDiagram
        participant FE as Frontend WebSocket Client
        participant WS as WebSocketConnectionManager
        participant Poller as StatusPollingService
        participant Gather as RecoaterDataGatherer
        participant Client as RecoaterClient

        FE->>WS: WebSocket CONNECT
        WS->>FE: accept()
        WS->>WS: add to active_connections; subscriptions={"status"}

        loop every poll_interval
                Poller->>WS: get_required_data_types()
                WS-->>Poller: {"status","drum","leveler","print"}
                Poller->>Gather: gather_all_data(Client, required_types)
                Gather->>Client: get_state(), get_drums(), ... (to_thread)
                Gather-->>Poller: gathered_data
                Poller->>WS: broadcast(construct_status_message(gathered_data))
                WS->>WS: filter per-connection by subscriptions
                WS-->>FE: send_json(filtered_message)
        end
```

##### Integration
- Initialized in `app.dependencies.initialize_websocket_services()`, which constructs a single `WebSocketConnectionManager`, a `WebSocketHandler` (router-level), and a `StatusPollingService` bound to this manager.
- Retrieved via `get_websocket_handler()` and `get_status_poller()` for routers and startup hooks to start/stop polling.
- The polling cadence and message shape are coordinated with Monitoring services; the manager exposes `get_required_data_types()` so Monitoring can gather only what is needed.


#### 3.3.2 Job Management

Purpose and why it lives here
- The Job Management service orchestrates the full multi-material, layer-by-layer print workflow. It coordinates OPC UA variables, drum uploads, hardware job control, caching, error handling, and progress.
- It belongs in the Service Layer because it implements core business rules and workflows, composing infrastructure (recoater client, CLI editor, OPC UA) into domain-specific behavior.

##### Class Diagram
```mermaid
classDiagram
        class MultiMaterialJobService {
            -RecoaterClient recoater_client
            -opcua : OPCUAService
            -cli_parser : Editor
            -current_job : MultiMaterialJobState
            -cli_cache : Dict~str, CliCacheEntry~
            -drum_cli_cache : Dict~int, Dict~str, Any~~
            -_background_task : asyncio.Task
            +start_layer_by_layer_job() async bool
            +cancel_job() async bool
            +get_job_status() async Dict
            +get_drum_status(drum_id) Dict
            +clear_all_error_flags() async bool
            -_validate_and_setup_job() async int
            -_process_all_layers() async
            -_process_layer(layer_index) async
            -_upload_layer_to_drums(layer_index) async
            -_get_layer_data_for_drum(drum_id, layer_index) async bytes
            -_prepare_and_start_print_job(layer_index) async
        }

        class LayerProcessingMixin
        class OPCUACoordinationMixin
        class CliCachingMixin
        class OPCUAService
        class RecoaterClient
        class Editor
        class MultiMaterialJobState
        class CliCacheEntry

        MultiMaterialJobService --|> LayerProcessingMixin
        MultiMaterialJobService --|> OPCUACoordinationMixin
        MultiMaterialJobService --|> CliCachingMixin
        MultiMaterialJobService --> OPCUAService : uses
        MultiMaterialJobService --> RecoaterClient : uses
        MultiMaterialJobService --> Editor : uses
        MultiMaterialJobService --> MultiMaterialJobState : manages
        MultiMaterialJobService --> CliCacheEntry : stores
```

##### Example Workflows
Layer-by-layer print workflow (happy path)
```mermaid
sequenceDiagram
        participant API as /cli/start-multimaterial-job
        participant JM as MultiMaterialJobService
        participant OPC as OPCUAService
        participant HW as RecoaterClient

        API->>JM: start_layer_by_layer_job()
        JM->>JM: _validate_and_setup_job() -> max_layers
        JM->>OPC: set_job_active(total_layers=max_layers)
        JM->>OPC: update_layer_progress(1)

        loop for each layer i=1..N
                JM->>OPC: set_recoater_ready_to_print(False)
                JM->>OPC: set_recoater_layer_complete(False)

                par upload to drums
                        JM->>HW: upload_drum_geometry(drum0, layer i)
                        JM->>HW: upload_drum_geometry(drum1, layer i)
                        JM->>HW: upload_drum_geometry(drum2, layer i)
                end

                JM->>OPC: set_recoater_ready_to_print(True)
                JM->>HW: start_print_job()
                JM->>JM: wait_for_layer_completion()
                JM->>OPC: set_recoater_layer_complete(True)
                JM->>OPC: update_layer_progress(i+1)
        end

        JM->>OPC: set_job_inactive()
        JM-->>API: success
```

##### Integration
- Constructed in `app.dependencies.initialize_multilayer_job_manager()` as `MultiMaterialJobService(recoater_client, opcua=_opcua_service or opcua_service, job_config=get_job_config())` and exposed via legacy-compatible `get_multilayer_job_manager()`.
- Uses the global Recoater Client from `initialize_recoater_client()` and the global OPC UA service from `initialize_opcua_service()`.
- Routers call into this service for printing workflows, cache management, cancellation, and status.

##### Mixins
- Inherited mixins and their roles:
    - `LayerProcessingMixin`: job lifecycle (cancel, status), layer upload helpers, background task cleanup.
    - `OPCUACoordinationMixin`: 7-variable coordination helpers (job_active, total_layers, current_layer, ready/complete, backend_error, plc_error), wait/clear error flags.
    - `CliCachingMixin`: generic and per-drum CLI caching, max-layer calculation, cache utilities.
- Advantages:
    - Composability and separation of concerns; easy unit testing per concern; reuse across future services.
    - Keeps `MultiMaterialJobService` thin and focused on orchestration.
- Disadvantages:
    - Method Resolution Order (MRO) complexity; implicit attribute requirements (e.g., `recoater_client`, `job_config`) can be missed; harder to navigate for newcomers.


#### 3.3.3 Monitoring

Purpose and why it lives here
- The Monitoring services collect live status from the recoater and broadcast to clients. They coordinate polling cadence, required data selection, error handling, and message shaping.
- They belong in the Service Layer because they orchestrate cross-cutting behavior between infrastructure (RecoaterClient) and communication (WebSockets) and implement application policy (what to poll, when, and how to present it).

##### Class Diagram
```mermaid
classDiagram
        class StatusPollingService {
            -websocket_manager : WebSocketConnectionManager
            -data_gatherer : RecoaterDataGatherer
            -poll_interval : float
            -polling_task : asyncio.Task
            -_running : bool
            +start() async
            +stop() async
            -_polling_loop() async
            -_poll_and_broadcast() async
            -_broadcast_connection_error(error) async
            +is_running bool
            +update_poll_interval(interval)
        }

        class RecoaterDataGatherer {
            +gather_all_data(client, required_types) async Dict
            +gather_all_drum_data(client) async Dict
            +gather_single_drum_data(client, drum_id) async Dict
            +gather_leveler_data(client) async Dict
            +gather_print_data(client) async Dict
            +construct_status_message(gathered) Dict
        }

        class WebSocketConnectionManager
        class RecoaterClient

        StatusPollingService --> RecoaterDataGatherer : uses
        StatusPollingService --> WebSocketConnectionManager : uses
        RecoaterDataGatherer --> RecoaterClient : calls (to_thread)
```

##### Example Workflows
Polling and broadcasting flow with on-demand data selection
```mermaid
sequenceDiagram
        participant Poller as StatusPollingService
        participant WS as WebSocketConnectionManager
        participant Gather as RecoaterDataGatherer
        participant Client as RecoaterClient

        Poller->>WS: has_connections?
        alt at least one client
                Poller->>WS: get_required_data_types()
                WS-->>Poller: {"status","drum","leveler","print"}
                Poller->>Gather: gather_all_data(Client, types)
                Gather->>Client: get_state(), get_drums(), ... (asyncio.to_thread)
                Gather-->>Poller: data
                Poller->>Gather: construct_status_message(data)
                Gather-->>Poller: message
                Poller->>WS: broadcast(message)
                WS-->>WS: per-connection filtering
        else no clients
                Poller-->>Poller: skip work this tick
        end
```

##### Integration
- Instantiated in `initialize_websocket_services()` as `StatusPollingService(WebSocketConnectionManager())` and retrievable via `get_status_poller()`.
- Lazily obtains the `RecoaterClient` using `get_recoater_client_instance()` inside `_poll_and_broadcast()` to avoid circular dependencies and to tolerate late initialization.
- Emits messages shaped for the frontend; subscriptions are enforced by `WebSocketConnectionManager`.


#### 3.3.4 OPCUA

Purpose and why it lives here
- The OPC UA service hosts an in-process OPC UA server, exposing the 7‑variable coordination protocol for PLC integration and providing coordination helpers and lightweight health monitoring.
- It belongs in the Service Layer because it encodes domain coordination rules (job lifecycle, progress, readiness, errors) while delegating protocol specifics to infrastructure (asyncua) via mixins.

##### Class Diagram
```mermaid
classDiagram
        class OPCUAService {
            -config : ServerConfig
            -_server_running : bool
            -_opcua_server : Server
            -_variable_nodes : Dict~str, Node~
            -_variable_cache : Dict~str, Any~
            -_connected : bool
            -_monitoring_task : asyncio.Task
            -_event_handlers : Dict~str, List~Callable~~
            +initialize() async bool
            +shutdown() async bool
            +connect() async bool
            +disconnect() async bool
            +is_connected() bool
            +start_server() async bool
            +stop_server() async bool
            +write_variable(name, value) async bool
            +read_variable(name) async Any
            +subscribe_to_changes(vars, handler) async bool
            +start_monitoring(interval) async
            +stop_monitoring() async
            +set_job_active(total_layers) async bool
            +set_job_inactive() async bool
            +update_layer_progress(n) async bool
            +set_recoater_ready_to_print(b) async bool
            +set_recoater_layer_complete(b) async bool
            +set_backend_error(b) async bool
            +set_plc_error(b) async bool
            +clear_error_flags() async bool
        }

        class CoordinationMixin
        class ServerMixin
        class MonitoringMixin
        class ServerConfig

        OPCUAService --|> CoordinationMixin
        OPCUAService --|> ServerMixin
        OPCUAService --|> MonitoringMixin
        OPCUAService --> ServerConfig : uses
```

##### Example Workflows
Variable write with event handler trigger
```mermaid
sequenceDiagram
        participant JM as MultiMaterialJobService
        participant OPC as OPCUAService
        participant Hand as EventHandler (callback)

        JM->>OPC: subscribe_to_changes(["current_layer"], Hand)
        OPC-->>JM: subscribed

        JM->>OPC: update_layer_progress(10)
        OPC->>OPC: CoordinationMixin.write_variable("current_layer", 10)
        OPC->>OPC: ServerMixin.write_variable(...) -> set_value on Node
        OPC->>Hand: on_change(name="current_layer", value=10)
```

##### Integration
- A module-level singleton `opcua_service = OPCUAService()` is defined and initialized via `app.dependencies.initialize_opcua_service()` during app startup; `get_opcua_service()` returns the instance.
- `MultiMaterialJobService` receives an OPC UA instance via DI (`initialize_multilayer_job_manager`) and uses coordination helpers to set variables for PLC synchronization throughout the job.
- The server configuration is loaded dynamically (`app.config.opcua_config`) and converted into the pure domain `ServerConfig`.

##### Mixins
- `CoordinationMixin` (application rules): logical connection management, job lifecycle helpers, progress updates, error flag control, event handler system; overrides `write_variable` to trigger events.
- `ServerMixin` (infrastructure): asyncua-based server lifecycle and variable nodes, type coercion, read/write, cache for fast getters.
- `MonitoringMixin` (cross-cutting): background monitoring loop with safe cancellation; can be extended for health checks.
- Advantages:
    - Clear separation between domain coordination, server infrastructure, and monitoring; easier replacement/testing.
    - Composition makes `OPCUAService` readable and focused on orchestration logic.
- Disadvantages:
    - Requires careful MRO ordering; implicit coupling between mixins; tracing behavior spans multiple files.


### 3.4 Infrastructure Layer

#### 3.4.1 CLI Editor


#### 3.4.2 Recoater Client


#### 3.4.3 Mock Recoater Client


### 3.5 Overall Backend Workflow

This section explains how the different parts of the backend system work together, from the moment a request arrives until a response is sent back.

#### 3.5.1 Router Registration Flow

When the backend application starts up, it needs to organize all the API endpoints (URLs) so that incoming requests know where to go. This is like setting up a postal system where each address (URL) has a specific mailbox (endpoint function).

**How Router Registration Works:**

```mermaid
sequenceDiagram
    participant Main as FastAPI App
    participant API as API Package
    participant Print as Print Router
    participant Hardware as Hardware Router
    participant Endpoints as Individual Endpoints

    Main->>API: Import API routers
    API->>Print: Include print router (/print)
    API->>Hardware: Include hardware router (/recoater)

    Print->>Endpoints: Register CLI endpoints
    Print->>Endpoints: Register layer endpoints
    Print->>Endpoints: Register multimaterial endpoints

    Hardware->>Endpoints: Register drum endpoints
    Hardware->>Endpoints: Register leveler endpoints
    Hardware->>Endpoints: Register blade endpoints

    API->>Main: Return configured routers
    Main->>Main: Mount routers with prefixes
```

**Step-by-Step Process:**

1. **Main Application Starts**: The FastAPI application begins initialization
2. **Import Routers**: The main app imports router modules for different functional areas
3. **Organize by Domain**:
   - Print Router handles all printing-related URLs (like `/api/print/cli/upload`)
   - Hardware Router handles all hardware control URLs (like `/api/recoater/drums/0/motion`)
4. **Register Individual Endpoints**: Each router registers its specific endpoint functions
5. **Mount with Prefixes**: The main app combines all routers with their URL prefixes
6. **Ready to Handle Requests**: Now when a request comes in, the system knows exactly which function should handle it

**Why This Organization Matters:**
- **Clear Structure**: Related endpoints are grouped together
- **Easy Maintenance**: Changes to print features only affect the print router
- **Scalability**: New features can be added by creating new routers
- **Team Collaboration**: Different developers can work on different routers without conflicts


---


## 4. Frontend Development

```mermaid
graph TB
    subgraph "Frontend Application"
        subgraph "Presentation Layer (Views)"
            STATUS_VIEW["StatusView"]
            RECOATER_VIEW["RecoaterView"]
            PRINT_VIEW["PrintView"]
            CONFIG_VIEW["ConfigurationView"]
            DEBUG_VIEW["DebugView"]
        end

        subgraph "Component Layer"
            STATUS_INDICATOR["StatusIndicator"]
            DRUM_CONTROL["DrumControl"]
            LEVELER_CONTROL["LevelerControl"]
            FILE_UPLOAD["FileUploadColumn"]
            JOB_PROGRESS["JobProgressDisplay"]
            MULTI_LAYER_JOB["MultiLayerJobControl"]
            ERROR_MODAL["CriticalErrorModal"]
            ERROR_DISPLAY["ErrorDisplayPanel"]
        end

        subgraph "Application State Layer (Pinia Stores)"
            STATUS_STORE["statusStore"]
            PRINT_JOB_STORE["printJobStore"]
        end

        subgraph "Service Layer"
            API_SERVICE["apiService"]
        end

        subgraph "Infrastructure Layer"
            ROUTER["Vue Router"]
            WEBSOCKET["WebSocket Client"]
            HTTP_CLIENT["Axios HTTP Client"]
        end
    end

    subgraph "Backend"
        subgraph "Presentation Layer"
            SYSTEM_APIS["System APIs"]
            subgraph "Print APIs"
                CLI_API["CLI API"]
                LAYER_API["Layer API"]
                MM_API["Multi-Material API"]
                JOB_API["Job API"]
                PRINT_DRUM_API["Drum API"]
            end
            subgraph "Recoater Control APIs"
                RECOATER_DRUM_API["Drum API"]
                RECOATER_BLADE_API["Blade API"]
                RECOATER_LEVELER_API["Leveler API"]
            end
        end
        subgraph "Service Layer"
            JOB_MANAGEMENT["Job Management"]
            COMMUNICATION["Communication"]
            MONITORING["Monitoring"]
            OPCUA["OPCUA"]
        end
        subgraph "Infrastructure Layer"
            CLI_EDITOR["CLI Editor"]
            RECOATER_CLIENT["Recoater Client"]
            MOCK_CLIENT["Mock Recoater Client"]
        end
    end

    subgraph "Aerosint Recoater"
        SERVER["Server"]
    end

    subgraph "TwinCAT XAR"
        PLC["PLC"]
    end

RECOATER_CLIENT <--> SERVER

JOB_MANAGEMENT <--> RECOATER_CLIENT
JOB_MANAGEMENT <--> OPCUA
COMMUNICATION <--> RECOATER_CLIENT
MONITORING <--> RECOATER_CLIENT
MONITORING <--> COMMUNICATION
OPCUA <--> PLC

SYSTEM_APIS <--> RECOATER_CLIENT
SYSTEM_APIS <--> OPCUA

CLI_API <--> JOB_MANAGEMENT
CLI_API <--> CLI_EDITOR
LAYER_API <--> JOB_MANAGEMENT
MM_API <--> JOB_MANAGEMENT
JOB_API <--> JOB_MANAGEMENT
PRINT_DRUM_API <--> RECOATER_CLIENT

RECOATER_DRUM_API <--> RECOATER_CLIENT
RECOATER_BLADE_API <--> RECOATER_CLIENT
RECOATER_LEVELER_API <--> RECOATER_CLIENT

STATUS_VIEW <--> STATUS_STORE
STATUS_VIEW <--> API_SERVICE
RECOATER_VIEW <--> STATUS_STORE
RECOATER_VIEW <--> API_SERVICE
PRINT_VIEW <--> PRINT_JOB_STORE
PRINT_VIEW <--> API_SERVICE
CONFIG_VIEW <--> API_SERVICE
DEBUG_VIEW <--> STATUS_STORE
DEBUG_VIEW <--> API_SERVICE

STATUS_INDICATOR <--> STATUS_STORE
DRUM_CONTROL <--> STATUS_STORE
DRUM_CONTROL <--> API_SERVICE
LEVELER_CONTROL <--> STATUS_STORE
LEVELER_CONTROL <--> API_SERVICE
FILE_UPLOAD <--> API_SERVICE
JOB_PROGRESS <--> PRINT_JOB_STORE
MULTI_LAYER_JOB <--> PRINT_JOB_STORE
MULTI_LAYER_JOB <--> API_SERVICE
ERROR_MODAL <--> PRINT_JOB_STORE
ERROR_DISPLAY <--> PRINT_JOB_STORE

STATUS_STORE <--> API_SERVICE
STATUS_STORE <--> WEBSOCKET
PRINT_JOB_STORE <--> API_SERVICE
PRINT_JOB_STORE <--> STATUS_STORE

API_SERVICE <--> HTTP_CLIENT
ROUTER <--> STATUS_STORE

API_SERVICE <--> SYSTEM_APIS
API_SERVICE <--> CLI_API
API_SERVICE <--> LAYER_API
API_SERVICE <--> MM_API
API_SERVICE <--> JOB_API
API_SERVICE <--> PRINT_DRUM_API
API_SERVICE <--> RECOATER_DRUM_API
API_SERVICE <--> RECOATER_BLADE_API
API_SERVICE <--> RECOATER_LEVELER_API

WEBSOCKET <--> COMMUNICATION

```

### 4.1 Frontend Layered Architecture
- Presentation Layer (Views & Components)
- Application Layer (Stores & Business Logic)
- Service Layer (API Communication)
- Infrastructure Layer (Utilities & External Integrations)
- Component Hierarchy and Dependencies
- State Management Architecture
- Routing and Navigation Structure


### 4.2 Component Development
- Vue.js 3 Composition API
- Component Lifecycle
- Props and Events
- Slot Usage


### 4.3 State Management
- Pinia Store Setup
- State Organization
- Actions and Getters
- Store Composition


### 4.4 Routing
- Vue Router Configuration
- Route Guards
- Navigation Patterns


### 4.5 API Integration
- HTTP Client Setup
- Request Interceptors
- Error Handling
- Loading States


### 4.6 Real-time Features
- WebSocket Integration
- Event Handling
- State Synchronization


### 4.7 Styling and UI
- CSS Organization
- Component Styling
- Responsive Design





## 5. API Documentation


### 5.1 API Overview


### 5.2 Authentication


### 5.3 Endpoint Reference
- Print Control APIs
- Recoater Control APIs
- Status APIs
- Configuration APIs


### 5.4 WebSocket Events


### 5.5 Error Codes


### 5.6 Rate Limiting


