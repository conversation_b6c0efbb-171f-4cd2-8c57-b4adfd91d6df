/**
 * Vue Router Configuration
 * ========================
 * 
 * Defines the routing configuration for the Recoater HMI application.
 * Maps URLs to Vue components for navigation between different views.
 */

import { createRouter, createWebHistory } from 'vue-router'
import StatusView from '../views/StatusView.vue'
import { useStatusStore } from '../stores/status'

const router = createRouter({
  history: createWebHistory(import.meta.env.BASE_URL),
  routes: [
    {
      path: '/',
      name: 'status',
      component: StatusView
    },
    {
      path: '/recoater',
      name: 'recoater',
      component: () => import('../views/RecoaterView.vue')
    },
    {
      path: '/print',
      name: 'print',
      component: () => import('../views/PrintView.vue')
    },
    {
      path: '/config',
      name: 'config',
      component: () => import('../views/ConfigurationView.vue')
    },
    {
      path: '/debug',
      name: 'debug',
      component: () => import('../views/DebugView.vue')
    }    
  ]
})

// Navigation guard to update data subscriptions based on current page
router.beforeEach((to, _from, next) => {
  const statusStore = useStatusStore()

  // Update current page in store to trigger selective data fetching
  if (to.name) {
    statusStore.setCurrentPage(to.name)
  }

  next()
})

export default router
