# Chapter 5: JavaScript Fundamentals
## Modern JavaScript for Vue.js Development

```
╔══════════════════════════════════════════════════════════════════════════════════════╗
║                          CHAPTER 5: JAVASCRIPT FUNDAMENTALS                          ║
║                      ES6+ Features and Patterns for Vue.js                          ║
╚══════════════════════════════════════════════════════════════════════════════════════╝
```

## Chapter Objectives

By the end of this chapter, you will understand:
- Modern JavaScript (ES6+) features used in Vue.js development
- Async/await patterns for API integration
- Destructuring, template literals, and optional chaining
- Error handling strategies for industrial applications

## Modern JavaScript Evolution

### Variable Declarations and Vue Reactivity

```javascript
// Traditional JavaScript (pre-ES6) - AVOID
var layerNumber = 1
var layerNumber = 5  // Can redeclare - DANGEROUS!

// Modern JavaScript (ES6+) - PREFERRED
let layerNumber = 1        // Block-scoped, can reassign
const maxLayers = 100      // Block-scoped, cannot reassign

// Vue.js Reactive Variables - BEST FOR COMPONENTS
import { ref, reactive } from 'vue'
const layerNumber = ref(1)        // Reactive primitive
const config = reactive({         // Reactive object
  drumCount: 3,
  defaultLayer: 1
})
```

## Async/Await for API Integration

### Real Implementation from Our Project

```javascript
import { ref } from 'vue'
import { useStatusStore } from '@/stores/status'
import apiService from '@/services/api'

const selectedLayerNumber = ref(1)
const previewLoading = ref(false)
const previewImageUrl = ref('')
const statusStore = useStatusStore()

const loadPreview = async () => {
  if (!statusStore.isConnected) return
  
  try {
    previewLoading.value = true
    
    const drumId = parseInt(previewSource.value.split('-')[1])
    const response = await apiService.getDrumGeometryPreview(
      drumId, 
      selectedLayerNumber.value
    )
    
    if (response.data) {
      // Clean up previous URL
      if (previewImageUrl.value) {
        URL.revokeObjectURL(previewImageUrl.value)
      }
      
      // Update reactive state
      previewImageUrl.value = URL.createObjectURL(response.data)
    }
    
  } catch (error) {
    console.error('Failed to load preview:', error)
    const errorMessage = error.response?.data?.detail || error.message
    showErrorMessage(`Preview failed: ${errorMessage}`)
    
  } finally {
    previewLoading.value = false
  }
}
```

## Essential Modern JavaScript Features

### 1. Destructuring Assignment

```javascript
// Object destructuring
const { statusStore, printJobStore } = useStores()
const { name, age, department } = user

// Array destructuring
const [x, y] = coordinates
const [first, ...rest] = colors

// In Vue components
const { isConnected, connectionStatus } = statusStore
```

### 2. Template Literals

```javascript
// String interpolation
const message = `Loading preview for drum ${drumId}, layer ${layer}`
const url = `/api/drums/${drumId}/preview?layer=${layer}`

// Multi-line strings
const query = `
  SELECT * FROM layers 
  WHERE drum_id = ${drumId} 
    AND layer_number = ${layer}
`
```

### 3. Optional Chaining and Nullish Coalescing

```javascript
// Safe property access
const layerCount = drumFile?.metadata?.layerCount ?? 0
const fileName = file?.name ?? 'Unknown file'
const errorMessage = error.response?.data?.detail ?? error.message ?? 'Unknown error'

// Difference between || and ??
const value1 = false || 'fallback'    // 'fallback' (false is falsy)
const value2 = false ?? 'fallback'    // false (false is not null/undefined)
```

### 4. Arrow Functions

```javascript
// Arrow function syntax
const loadPreview = async () => {
  // Function body
}

// Array methods with arrow functions
const drumNames = drumIds.map(id => `Drum ${id}`)
const activeDrums = drumIds.filter(id => drums[id]?.isActive)
```

## 🚨 Error Handling Patterns

### Comprehensive Error Strategy

```javascript
const loadPreview = async () => {
  try {
    // Input validation
    if (!statusStore.isConnected) {
      throw new Error('Recoater not connected')
    }
    
    previewLoading.value = true
    
    // API call with timeout
    const controller = new AbortController()
    const timeoutId = setTimeout(() => controller.abort(), 30000)
    
    const response = await apiService.getDrumGeometryPreview(
      drumId, 
      selectedLayerNumber.value,
      { signal: controller.signal }
    )
    
    clearTimeout(timeoutId)
    
    // Validate response
    if (!response.data) {
      throw new Error('Empty response from server')
    }
    
    // Success: update UI
    previewImageUrl.value = URL.createObjectURL(response.data)
    
  } catch (error) {
    console.error('Preview load failed:', error)
    
    // User-friendly error messages
    let userMessage
    if (error.name === 'AbortError') {
      userMessage = 'Preview request timed out'
    } else if (error.response?.status === 404) {
      userMessage = 'Preview not found. File may not be uploaded.'
    } else {
      userMessage = `Unexpected error: ${error.message}`
    }
    
    showErrorNotification(userMessage)
    
  } finally {
    previewLoading.value = false
  }
}
```

## 📦 Module Systems

### ES6 Imports and Exports

```javascript
// Named exports and imports
export const usePrintJobStore = defineStore('printJob', () => { /* ... */ })
export const DRUM_COLORS = { 0: 'blue', 1: 'orange', 2: 'green' }

import { usePrintJobStore, DRUM_COLORS } from '@/stores/printJob'

// Default exports and imports
export default apiService
import apiService from '@/services/api'
```

## 🎓 Key Takeaways

### Modern JavaScript Benefits for Vue.js
1. **Cleaner Code**: Destructuring and template literals reduce boilerplate
2. **Safer Code**: Optional chaining prevents runtime errors  
3. **Async Clarity**: Async/await makes API calls readable
4. **Better Performance**: Modern features are optimized

### Best Practices
1. **Use const by default**, let when reassignment needed
2. **Prefer arrow functions** for component methods
3. **Always handle errors** in async functions
4. **Use optional chaining** for safe property access
5. **Leverage destructuring** for cleaner variable assignment

## 🚀 What's Next

Now that you understand modern JavaScript, we'll see how Vue.js builds upon these concepts to create reactive user interfaces.

**Chapter 6: Vue.js Introduction** will cover:
- Vue.js component system
- Reactive data binding
- Template syntax and directives

---

**Continue your learning journey** → [Chapter 6: Vue.js Introduction](06-vuejs-introduction.md)

## 💡 Practice Exercises

1. Convert callback-based code to async/await
2. Implement error handling for an API call
3. Use destructuring to clean up object property access
4. Create template literals for dynamic strings

*Modern JavaScript is the foundation of effective Vue.js development. Master these patterns and your Vue.js code will be clean, safe, and maintainable.*

---

## 🚀 Mini Project: Task Dashboard - JavaScript Functionality

### Exercise 5.1: Core JavaScript Implementation

Create the complete JavaScript functionality for your Task Dashboard. Create `scripts/main.js`:

```javascript
/* ===========================================
   TASK DASHBOARD - MAIN JAVASCRIPT
   Modern ES6+ Implementation
   =========================================== */

// Import utilities (we'll create this next)
// import { generateId, formatDate, debounce } from './utils.js';

/* ===========================================
   APPLICATION STATE
   =========================================== */

class TaskDashboard {
  constructor() {
    // Application state
    this.tasks = [];
    this.categories = [
      { id: 'work', name: 'Work', emoji: '🏢', color: '#007bff' },
      { id: 'personal', name: 'Personal', emoji: '🏠', color: '#28a745' },
      { id: 'learning', name: 'Learning', emoji: '📚', color: '#ffc107' },
      { id: 'health', name: 'Health', emoji: '💪', color: '#dc3545' }
    ];
    
    this.filters = {
      search: '',
      category: '',
      status: '',
      priority: ''
    };
    
    this.theme = 'light';
    
    // DOM elements
    this.elements = {};
    
    // Initialize the application
    this.init();
  }

  /* ===========================================
     INITIALIZATION
     =========================================== */

  async init() {
    try {
      this.cacheElements();
      this.loadFromStorage();
      this.attachEventListeners();
      this.loadSampleData();
      this.updateDisplay();
      this.initializeTheme();
      
      console.log('Task Dashboard initialized successfully');
    } catch (error) {
      console.error('Failed to initialize Task Dashboard:', error);
      this.showError('Failed to initialize application');
    }
  }

  cacheElements() {
    // Form elements
    this.elements = {
      taskForm: document.getElementById('task-form'),
      titleInput: document.getElementById('task-title'),
      descriptionInput: document.getElementById('task-description'),
      categorySelect: document.getElementById('task-category'),
      prioritySelect: document.getElementById('task-priority'),
      dueDateInput: document.getElementById('task-due-date'),
      
      // Display elements
      taskList: document.getElementById('task-list'),
      emptyState: document.getElementById('empty-state'),
      progressBar: document.querySelector('.progress-fill'),
      progressText: document.querySelector('.progress-text'),
      
      // Statistics
      totalTasks: document.getElementById('total-tasks'),
      completedTasks: document.getElementById('completed-tasks'),
      completionPercentage: document.getElementById('completion-percentage'),
      
      // Filters
      searchInput: document.getElementById('task-search'),
      filterCategory: document.getElementById('filter-category'),
      filterStatus: document.getElementById('filter-status'),
      filterPriority: document.getElementById('filter-priority'),
      
      // Controls
      themeToggle: document.getElementById('theme-toggle'),
      clearCompletedBtn: document.getElementById('clear-completed-btn'),
      exportBtn: document.getElementById('export-tasks-btn'),
      
      // Modal
      dataModal: document.getElementById('data-modal'),
      modalClose: document.querySelector('.modal-close'),
      dataManagementBtn: document.getElementById('data-management-btn'),
      importBtn: document.getElementById('import-data-btn'),
      exportDataBtn: document.getElementById('export-data-btn'),
      clearAllBtn: document.getElementById('clear-all-data-btn'),
      importFileInput: document.getElementById('import-file-input'),
      
      // Other
      lastUpdated: document.getElementById('last-updated')
    };
  }

  /* ===========================================
     EVENT LISTENERS
     =========================================== */

  attachEventListeners() {
    // Form submission
    this.elements.taskForm.addEventListener('submit', (e) => {
      e.preventDefault();
      this.handleTaskSubmit();
    });

    // Real-time search with debouncing
    this.elements.searchInput.addEventListener('input', 
      this.debounce((e) => this.handleSearch(e.target.value), 300)
    );

    // Filter changes
    this.elements.filterCategory.addEventListener('change', (e) => {
      this.filters.category = e.target.value;
      this.updateDisplay();
    });

    this.elements.filterStatus.addEventListener('change', (e) => {
      this.filters.status = e.target.value;
      this.updateDisplay();
    });

    this.elements.filterPriority.addEventListener('change', (e) => {
      this.filters.priority = e.target.value;
      this.updateDisplay();
    });

    // Theme toggle
    this.elements.themeToggle.addEventListener('click', () => {
      this.toggleTheme();
    });

    // Bulk actions
    this.elements.clearCompletedBtn.addEventListener('click', () => {
      this.clearCompletedTasks();
    });

    this.elements.exportBtn.addEventListener('click', () => {
      this.exportTasks();
    });

    // Modal controls
    this.elements.dataManagementBtn.addEventListener('click', () => {
      this.openModal();
    });

    this.elements.modalClose.addEventListener('click', () => {
      this.closeModal();
    });

    this.elements.dataModal.addEventListener('click', (e) => {
      if (e.target === this.elements.dataModal) {
        this.closeModal();
      }
    });

    // Data management
    this.elements.importBtn.addEventListener('click', () => {
      this.elements.importFileInput.click();
    });

    this.elements.importFileInput.addEventListener('change', (e) => {
      this.handleFileImport(e.target.files[0]);
    });

    this.elements.exportDataBtn.addEventListener('click', () => {
      this.exportTasks('json');
      this.closeModal();
    });

    this.elements.clearAllBtn.addEventListener('click', () => {
      this.clearAllData();
    });

    // Keyboard shortcuts
    document.addEventListener('keydown', (e) => {
      this.handleKeyboardShortcuts(e);
    });

    // Auto-save when window closes
    window.addEventListener('beforeunload', () => {
      this.saveToStorage();
    });
  }

  /* ===========================================
     TASK MANAGEMENT
     =========================================== */

  async handleTaskSubmit() {
    try {
      const taskData = this.getFormData();
      
      // Validate task data
      if (!this.validateTask(taskData)) {
        return;
      }

      // Create new task
      const newTask = this.createTask(taskData);
      
      // Add to tasks array
      this.tasks.unshift(newTask); // Add to beginning for recent-first display
      
      // Update display
      this.updateDisplay();
      
      // Reset form
      this.elements.taskForm.reset();
      this.elements.titleInput.focus();
      
      // Save to storage
      this.saveToStorage();
      
      // Show success feedback
      this.showSuccess(`Task "${newTask.title}" created successfully!`);
      
    } catch (error) {
      console.error('Error creating task:', error);
      this.showError('Failed to create task. Please try again.');
    }
  }

  getFormData() {
    return {
      title: this.elements.titleInput.value.trim(),
      description: this.elements.descriptionInput.value.trim(),
      category: this.elements.categorySelect.value,
      priority: this.elements.prioritySelect.value,
      dueDate: this.elements.dueDateInput.value
    };
  }

  validateTask(taskData) {
    // Clear previous errors
    this.clearFormErrors();
    
    let isValid = true;
    
    // Title validation
    if (!taskData.title) {
      this.showFieldError('task-title', 'Task title is required');
      isValid = false;
    } else if (taskData.title.length < 3) {
      this.showFieldError('task-title', 'Task title must be at least 3 characters');
      isValid = false;
    }
    
    // Due date validation
    if (taskData.dueDate) {
      const dueDate = new Date(taskData.dueDate);
      const today = new Date();
      today.setHours(0, 0, 0, 0);
      
      if (dueDate < today) {
        this.showFieldError('task-due-date', 'Due date cannot be in the past');
        isValid = false;
      }
    }
    
    return isValid;
  }

  createTask(taskData) {
    return {
      id: this.generateId(),
      title: taskData.title,
      description: taskData.description,
      category: taskData.category,
      priority: taskData.priority,
      dueDate: taskData.dueDate,
      completed: false,
      createdAt: new Date().toISOString(),
      completedAt: null
    };
  }

  toggleTaskComplete(taskId) {
    const task = this.tasks.find(t => t.id === taskId);
    if (!task) return;
    
    task.completed = !task.completed;
    task.completedAt = task.completed ? new Date().toISOString() : null;
    
    this.updateDisplay();
    this.saveToStorage();
    
    const action = task.completed ? 'completed' : 'reopened';
    this.showSuccess(`Task "${task.title}" ${action}!`);
  }

  deleteTask(taskId) {
    const taskIndex = this.tasks.findIndex(t => t.id === taskId);
    if (taskIndex === -1) return;
    
    const task = this.tasks[taskIndex];
    
    if (confirm(`Are you sure you want to delete "${task.title}"?`)) {
      this.tasks.splice(taskIndex, 1);
      this.updateDisplay();
      this.saveToStorage();
      this.showSuccess(`Task "${task.title}" deleted!`);
    }
  }

  editTask(taskId) {
    const task = this.tasks.find(t => t.id === taskId);
    if (!task) return;
    
    // Simple edit: populate form with task data
    this.elements.titleInput.value = task.title;
    this.elements.descriptionInput.value = task.description || '';
    this.elements.categorySelect.value = task.category || '';
    this.elements.prioritySelect.value = task.priority;
    this.elements.dueDateInput.value = task.dueDate || '';
    
    // Remove the original task
    this.deleteTask(taskId);
    
    // Focus on title input
    this.elements.titleInput.focus();
    this.elements.titleInput.select();
  }

  /* ===========================================
     DISPLAY AND FILTERING
     =========================================== */

  updateDisplay() {
    const filteredTasks = this.getFilteredTasks();
    
    this.renderTasks(filteredTasks);
    this.updateStatistics();
    this.updateProgress();
    this.updateLastUpdated();
  }

  getFilteredTasks() {
    return this.tasks.filter(task => {
      // Search filter
      if (this.filters.search) {
        const searchTerm = this.filters.search.toLowerCase();
        const matchesSearch = 
          task.title.toLowerCase().includes(searchTerm) ||
          (task.description && task.description.toLowerCase().includes(searchTerm));
        
        if (!matchesSearch) return false;
      }
      
      // Category filter
      if (this.filters.category && task.category !== this.filters.category) {
        return false;
      }
      
      // Status filter
      if (this.filters.status) {
        if (this.filters.status === 'completed' && !task.completed) return false;
        if (this.filters.status === 'pending' && task.completed) return false;
        if (this.filters.status === 'overdue' && !this.isTaskOverdue(task)) return false;
      }
      
      // Priority filter
      if (this.filters.priority && task.priority !== this.filters.priority) {
        return false;
      }
      
      return true;
    });
  }

  renderTasks(tasks) {
    // Hide sample task
    const sampleTask = document.querySelector('.sample-task');
    if (sampleTask) {
      sampleTask.style.display = 'none';
    }
    
    // Show/hide empty state
    this.elements.emptyState.style.display = tasks.length === 0 ? 'block' : 'none';
    
    // Clear existing tasks (except sample and empty state)
    const existingTasks = this.elements.taskList.querySelectorAll('.task-card:not(.sample-task):not(.empty-state)');
    existingTasks.forEach(task => task.remove());
    
    // Render filtered tasks
    tasks.forEach(task => {
      const taskElement = this.createTaskElement(task);
      this.elements.taskList.appendChild(taskElement);
    });
  }

  createTaskElement(task) {
    const article = document.createElement('article');
    article.className = `task-card ${task.completed ? 'completed' : ''}`;
    article.setAttribute('data-task-id', task.id);
    article.setAttribute('role', 'listitem');
    
    const category = this.categories.find(c => c.id === task.category);
    const isOverdue = this.isTaskOverdue(task);
    const priorityEmoji = { high: '🔴', medium: '🟡', low: '🟢' }[task.priority];
    
    article.innerHTML = `
      <div class="task-header">
        <input 
          type="checkbox" 
          class="task-checkbox" 
          ${task.completed ? 'checked' : ''}
          aria-label="Mark task as ${task.completed ? 'incomplete' : 'complete'}"
        >
        <h3 class="task-title">${this.escapeHtml(task.title)}</h3>
        <span class="task-priority priority-${task.priority}" 
              aria-label="${task.priority} priority">${priorityEmoji}</span>
      </div>
      
      <div class="task-body">
        ${task.description ? `<p class="task-description">${this.escapeHtml(task.description)}</p>` : ''}
        <div class="task-meta">
          ${category ? `<span class="task-category category-${category.id}">${category.emoji} ${category.name}</span>` : ''}
          ${task.dueDate ? `
            <time class="task-due-date ${isOverdue ? 'overdue' : ''}" 
                  datetime="${task.dueDate}">
              ${isOverdue ? '⚠️ ' : ''}Due: ${this.formatDate(task.dueDate)}
            </time>
          ` : ''}
        </div>
      </div>
      
      <div class="task-actions">
        <button class="task-action-btn edit-btn" 
                aria-label="Edit task: ${this.escapeHtml(task.title)}">
          ✏️
        </button>
        <button class="task-action-btn delete-btn" 
                aria-label="Delete task: ${this.escapeHtml(task.title)}">
          🗑️
        </button>
      </div>
    `;
    
    // Attach event listeners
    const checkbox = article.querySelector('.task-checkbox');
    const editBtn = article.querySelector('.edit-btn');
    const deleteBtn = article.querySelector('.delete-btn');
    
    checkbox.addEventListener('change', () => this.toggleTaskComplete(task.id));
    editBtn.addEventListener('click', () => this.editTask(task.id));
    deleteBtn.addEventListener('click', () => this.deleteTask(task.id));
    
    // Add animation
    article.classList.add('fade-in');
    
    return article;
  }

  /* ===========================================
     STATISTICS AND PROGRESS
     =========================================== */

  updateStatistics() {
    const total = this.tasks.length;
    const completed = this.tasks.filter(t => t.completed).length;
    const percentage = total > 0 ? Math.round((completed / total) * 100) : 0;
    
    this.elements.totalTasks.textContent = total;
    this.elements.completedTasks.textContent = completed;
    this.elements.completionPercentage.textContent = `${percentage}%`;
  }

  updateProgress() {
    const total = this.tasks.length;
    const completed = this.tasks.filter(t => t.completed).length;
    const percentage = total > 0 ? (completed / total) * 100 : 0;
    
    this.elements.progressBar.style.width = `${percentage}%`;
    this.elements.progressText.textContent = `${Math.round(percentage)}% Complete`;
    
    // Update ARIA attributes
    const progressContainer = document.querySelector('.progress-container');
    progressContainer.setAttribute('aria-valuenow', Math.round(percentage));
  }

  /* ===========================================
     SEARCH AND FILTERING
     =========================================== */

  handleSearch(searchTerm) {
    this.filters.search = searchTerm;
    this.updateDisplay();
  }

  clearFilters() {
    this.filters = { search: '', category: '', status: '', priority: '' };
    this.elements.searchInput.value = '';
    this.elements.filterCategory.value = '';
    this.elements.filterStatus.value = '';
    this.elements.filterPriority.value = '';
    this.updateDisplay();
  }

  /* ===========================================
     BULK OPERATIONS
     =========================================== */

  clearCompletedTasks() {
    const completedTasks = this.tasks.filter(t => t.completed);
    
    if (completedTasks.length === 0) {
      this.showInfo('No completed tasks to clear.');
      return;
    }
    
    if (confirm(`Delete ${completedTasks.length} completed task(s)?`)) {
      this.tasks = this.tasks.filter(t => !t.completed);
      this.updateDisplay();
      this.saveToStorage();
      this.showSuccess(`${completedTasks.length} completed tasks cleared!`);
    }
  }

  clearAllData() {
    if (confirm('Are you sure you want to delete ALL tasks? This cannot be undone.')) {
      if (confirm('This will permanently delete all your tasks. Are you absolutely sure?')) {
        this.tasks = [];
        this.updateDisplay();
        this.saveToStorage();
        this.closeModal();
        this.showSuccess('All tasks cleared!');
      }
    }
  }

  /* ===========================================
     DATA PERSISTENCE
     =========================================== */

  saveToStorage() {
    try {
      const data = {
        tasks: this.tasks,
        theme: this.theme,
        lastUpdated: new Date().toISOString()
      };
      
      localStorage.setItem('taskDashboard', JSON.stringify(data));
      this.updateLastUpdated();
    } catch (error) {
      console.error('Failed to save to storage:', error);
      this.showError('Failed to save data locally');
    }
  }

  loadFromStorage() {
    try {
      const data = localStorage.getItem('taskDashboard');
      if (data) {
        const parsed = JSON.parse(data);
        this.tasks = parsed.tasks || [];
        this.theme = parsed.theme || 'light';
      }
    } catch (error) {
      console.error('Failed to load from storage:', error);
      this.showError('Failed to load saved data');
    }
  }

  /* ===========================================
     IMPORT/EXPORT
     =========================================== */

  exportTasks(format = 'json') {
    try {
      const data = {
        tasks: this.tasks,
        categories: this.categories,
        exportedAt: new Date().toISOString(),
        version: '1.0'
      };
      
      let content, filename, mimeType;
      
      if (format === 'json') {
        content = JSON.stringify(data, null, 2);
        filename = `tasks-${this.formatDate(new Date(), 'filename')}.json`;
        mimeType = 'application/json';
      } else {
        // Text format
        content = this.tasksToText();
        filename = `tasks-${this.formatDate(new Date(), 'filename')}.txt`;
        mimeType = 'text/plain';
      }
      
      this.downloadFile(content, filename, mimeType);
      this.showSuccess('Tasks exported successfully!');
      
    } catch (error) {
      console.error('Export failed:', error);
      this.showError('Failed to export tasks');
    }
  }

  async handleFileImport(file) {
    if (!file) return;
    
    try {
      const text = await this.readFile(file);
      const data = JSON.parse(text);
      
      if (!data.tasks || !Array.isArray(data.tasks)) {
        throw new Error('Invalid file format');
      }
      
      // Validate imported tasks
      const validTasks = data.tasks.filter(task => this.isValidTask(task));
      
      if (validTasks.length === 0) {
        throw new Error('No valid tasks found in file');
      }
      
      // Merge with existing tasks (assign new IDs to avoid conflicts)
      const importedTasks = validTasks.map(task => ({
        ...task,
        id: this.generateId(),
        createdAt: task.createdAt || new Date().toISOString()
      }));
      
      this.tasks.push(...importedTasks);
      this.updateDisplay();
      this.saveToStorage();
      this.closeModal();
      
      this.showSuccess(`${importedTasks.length} tasks imported successfully!`);
      
    } catch (error) {
      console.error('Import failed:', error);
      this.showError('Failed to import tasks. Please check the file format.');
    }
  }

  /* ===========================================
     THEME MANAGEMENT
     =========================================== */

  initializeTheme() {
    // Check for saved theme preference or default to 'light'
    const savedTheme = localStorage.getItem('theme') || this.theme;
    this.setTheme(savedTheme);
  }

  toggleTheme() {
    const newTheme = this.theme === 'light' ? 'dark' : 'light';
    this.setTheme(newTheme);
  }

  setTheme(theme) {
    this.theme = theme;
    document.documentElement.setAttribute('data-theme', theme);
    localStorage.setItem('theme', theme);
    
    // Update theme toggle button accessibility
    const toggleBtn = this.elements.themeToggle;
    toggleBtn.setAttribute('aria-label', 
      theme === 'light' ? 'Switch to dark mode' : 'Switch to light mode'
    );
  }

  /* ===========================================
     UTILITY FUNCTIONS
     =========================================== */

  generateId() {
    return Date.now().toString(36) + Math.random().toString(36).substr(2);
  }

  formatDate(dateString, format = 'readable') {
    const date = new Date(dateString);
    
    if (format === 'filename') {
      return date.toISOString().split('T')[0];
    }
    
    return date.toLocaleDateString('en-US', {
      year: 'numeric',
      month: 'short',
      day: 'numeric'
    });
  }

  isTaskOverdue(task) {
    if (!task.dueDate || task.completed) return false;
    
    const dueDate = new Date(task.dueDate);
    const today = new Date();
    today.setHours(23, 59, 59, 999); // End of today
    
    return dueDate < today;
  }

  escapeHtml(text) {
    const div = document.createElement('div');
    div.textContent = text;
    return div.innerHTML;
  }

  debounce(func, wait) {
    let timeout;
    return function executedFunction(...args) {
      const later = () => {
        clearTimeout(timeout);
        func(...args);
      };
      clearTimeout(timeout);
      timeout = setTimeout(later, wait);
    };
  }

  readFile(file) {
    return new Promise((resolve, reject) => {
      const reader = new FileReader();
      reader.onload = e => resolve(e.target.result);
      reader.onerror = reject;
      reader.readAsText(file);
    });
  }

  downloadFile(content, filename, mimeType) {
    const blob = new Blob([content], { type: mimeType });
    const url = URL.createObjectURL(blob);
    const link = document.createElement('a');
    link.href = url;
    link.download = filename;
    document.body.appendChild(link);
    link.click();
    document.body.removeChild(link);
    URL.revokeObjectURL(url);
  }

  /* ===========================================
     UI FEEDBACK
     =========================================== */

  showSuccess(message) {
    this.showNotification(message, 'success');
  }

  showError(message) {
    this.showNotification(message, 'error');
  }

  showInfo(message) {
    this.showNotification(message, 'info');
  }

  showNotification(message, type = 'info') {
    // Create notification element
    const notification = document.createElement('div');
    notification.className = `notification notification-${type}`;
    notification.innerHTML = `
      <span class="notification-message">${this.escapeHtml(message)}</span>
      <button class="notification-close" aria-label="Close notification">×</button>
    `;
    
    // Add to page
    document.body.appendChild(notification);
    
    // Auto-close after 5 seconds
    const closeTimeout = setTimeout(() => {
      this.removeNotification(notification);
    }, 5000);
    
    // Manual close
    const closeBtn = notification.querySelector('.notification-close');
    closeBtn.addEventListener('click', () => {
      clearTimeout(closeTimeout);
      this.removeNotification(notification);
    });
    
    // Animate in
    requestAnimationFrame(() => {
      notification.classList.add('notification-show');
    });
  }

  removeNotification(notification) {
    notification.classList.add('notification-hide');
    setTimeout(() => {
      if (notification.parentNode) {
        notification.parentNode.removeChild(notification);
      }
    }, 300);
  }

  /* ===========================================
     FORM VALIDATION
     =========================================== */

  showFieldError(fieldId, message) {
    const field = document.getElementById(fieldId);
    const errorElement = document.getElementById(fieldId.replace('-', '-') + '-error');
    
    if (field) {
      field.setAttribute('aria-invalid', 'true');
      field.classList.add('error');
    }
    
    if (errorElement) {
      errorElement.textContent = message;
      errorElement.style.display = 'block';
    }
  }

  clearFormErrors() {
    const errorElements = document.querySelectorAll('.error-message');
    const fields = document.querySelectorAll('.form-input, .form-select, .form-textarea');
    
    errorElements.forEach(el => {
      el.textContent = '';
      el.style.display = 'none';
    });
    
    fields.forEach(field => {
      field.setAttribute('aria-invalid', 'false');
      field.classList.remove('error');
    });
  }

  /* ===========================================
     MODAL MANAGEMENT
     =========================================== */

  openModal() {
    this.elements.dataModal.classList.add('active');
    this.elements.dataModal.setAttribute('aria-hidden', 'false');
    
    // Focus management
    const firstFocusable = this.elements.dataModal.querySelector('button, input, select, textarea');
    if (firstFocusable) {
      firstFocusable.focus();
    }
    
    // Prevent body scroll
    document.body.style.overflow = 'hidden';
  }

  closeModal() {
    this.elements.dataModal.classList.remove('active');
    this.elements.dataModal.setAttribute('aria-hidden', 'true');
    
    // Restore body scroll
    document.body.style.overflow = '';
    
    // Return focus to trigger element
    this.elements.dataManagementBtn.focus();
  }

  /* ===========================================
     KEYBOARD SHORTCUTS
     =========================================== */

  handleKeyboardShortcuts(e) {
    // Ctrl/Cmd + Enter: Submit form
    if ((e.ctrlKey || e.metaKey) && e.key === 'Enter') {
      if (document.activeElement === this.elements.titleInput || 
          document.activeElement === this.elements.descriptionInput) {
        e.preventDefault();
        this.handleTaskSubmit();
      }
    }
    
    // Escape: Close modal
    if (e.key === 'Escape') {
      if (this.elements.dataModal.classList.contains('active')) {
        this.closeModal();
      }
    }
    
    // Ctrl/Cmd + K: Focus search
    if ((e.ctrlKey || e.metaKey) && e.key === 'k') {
      e.preventDefault();
      this.elements.searchInput.focus();
    }
  }

  /* ===========================================
     HELPER FUNCTIONS
     =========================================== */

  updateLastUpdated() {
    const now = new Date();
    this.elements.lastUpdated.textContent = 
      `Last updated: ${now.toLocaleTimeString()}`;
  }

  loadSampleData() {
    // Only load sample data if no tasks exist
    if (this.tasks.length === 0) {
      this.tasks = [
        {
          id: this.generateId(),
          title: "Complete Task Dashboard Project",
          description: "Build a fully functional task management application using HTML, CSS, and JavaScript",
          category: "learning",
          priority: "high",
          dueDate: "2024-02-01",
          completed: false,
          createdAt: new Date().toISOString(),
          completedAt: null
        },
        {
          id: this.generateId(),
          title: "Practice CSS Grid Layout",
          description: "Create responsive layouts using CSS Grid",
          category: "learning",
          priority: "medium",
          dueDate: "2024-01-25",
          completed: true,
          createdAt: new Date(Date.now() - 86400000).toISOString(), // Yesterday
          completedAt: new Date().toISOString()
        },
        {
          id: this.generateId(),
          title: "Weekly grocery shopping",
          description: "Buy fresh vegetables, fruits, and essentials",
          category: "personal",
          priority: "low",
          dueDate: "2024-01-20",
          completed: false,
          createdAt: new Date(Date.now() - 172800000).toISOString(), // 2 days ago
          completedAt: null
        }
      ];
    }
  }

  tasksToText() {
    let text = 'TASK DASHBOARD EXPORT\n';
    text += '===================\n\n';
    
    this.tasks.forEach((task, index) => {
      text += `${index + 1}. ${task.title}\n`;
      if (task.description) {
        text += `   Description: ${task.description}\n`;
      }
      text += `   Status: ${task.completed ? 'Completed' : 'Pending'}\n`;
      text += `   Priority: ${task.priority}\n`;
      if (task.category) {
        const category = this.categories.find(c => c.id === task.category);
        text += `   Category: ${category ? category.name : task.category}\n`;
      }
      if (task.dueDate) {
        text += `   Due Date: ${this.formatDate(task.dueDate)}\n`;
      }
      text += `   Created: ${this.formatDate(task.createdAt)}\n`;
      text += '\n';
    });
    
    return text;
  }

  isValidTask(task) {
    return task && 
           typeof task.title === 'string' && 
           task.title.length > 0 &&
           typeof task.completed === 'boolean';
  }
}

/* ===========================================
   INITIALIZE APPLICATION
   =========================================== */

// Wait for DOM to be fully loaded
document.addEventListener('DOMContentLoaded', () => {
  window.taskDashboard = new TaskDashboard();
});

// Add notification styles if they don't exist
const notificationStyles = `
  .notification {
    position: fixed;
    top: 20px;
    right: 20px;
    z-index: 10000;
    max-width: 400px;
    padding: 1rem 1.5rem;
    border-radius: 0.5rem;
    box-shadow: 0 10px 15px rgba(0, 0, 0, 0.1);
    display: flex;
    align-items: center;
    justify-content: space-between;
    gap: 1rem;
    transform: translateX(100%);
    transition: transform 0.3s ease;
  }
  
  .notification-show {
    transform: translateX(0);
  }
  
  .notification-hide {
    transform: translateX(100%);
  }
  
  .notification-success {
    background: #d4edda;
    color: #155724;
    border: 1px solid #c3e6cb;
  }
  
  .notification-error {
    background: #f8d7da;
    color: #721c24;
    border: 1px solid #f5c6cb;
  }
  
  .notification-info {
    background: #d1ecf1;
    color: #0c5460;
    border: 1px solid #bee5eb;
  }
  
  .notification-close {
    background: none;
    border: none;
    font-size: 1.5rem;
    cursor: pointer;
    color: inherit;
    opacity: 0.7;
  }
  
  .notification-close:hover {
    opacity: 1;
  }
`;

// Inject notification styles
const styleSheet = document.createElement('style');
styleSheet.textContent = notificationStyles;
document.head.appendChild(styleSheet);
```

### Exercise 5.2: Create Utility Functions

Create `scripts/utils.js` for reusable functions:

```javascript
/* ===========================================
   UTILITY FUNCTIONS
   Modern JavaScript helpers
   =========================================== */

/**
 * Generate a unique ID
 * @returns {string} Unique identifier
 */
export const generateId = () => {
  return Date.now().toString(36) + Math.random().toString(36).substr(2);
};

/**
 * Format date for display
 * @param {string|Date} date - Date to format
 * @param {string} format - Format type ('readable', 'filename', 'iso')
 * @returns {string} Formatted date string
 */
export const formatDate = (date, format = 'readable') => {
  const dateObj = new Date(date);
  
  if (isNaN(dateObj.getTime())) {
    return 'Invalid Date';
  }
  
  switch (format) {
    case 'filename':
      return dateObj.toISOString().split('T')[0];
    case 'iso':
      return dateObj.toISOString();
    case 'readable':
    default:
      return dateObj.toLocaleDateString('en-US', {
        year: 'numeric',
        month: 'short',
        day: 'numeric'
      });
  }
};

/**
 * Debounce function calls
 * @param {Function} func - Function to debounce
 * @param {number} wait - Wait time in milliseconds
 * @returns {Function} Debounced function
 */
export const debounce = (func, wait) => {
  let timeout;
  return function executedFunction(...args) {
    const later = () => {
      clearTimeout(timeout);
      func.apply(this, args);
    };
    clearTimeout(timeout);
    timeout = setTimeout(later, wait);
  };
};

/**
 * Throttle function calls
 * @param {Function} func - Function to throttle
 * @param {number} limit - Time limit in milliseconds
 * @returns {Function} Throttled function
 */
export const throttle = (func, limit) => {
  let inThrottle;
  return function executedFunction(...args) {
    if (!inThrottle) {
      func.apply(this, args);
      inThrottle = true;
      setTimeout(() => inThrottle = false, limit);
    }
  };
};

/**
 * Escape HTML to prevent XSS attacks
 * @param {string} text - Text to escape
 * @returns {string} Escaped HTML
 */
export const escapeHtml = (text) => {
  const div = document.createElement('div');
  div.textContent = text;
  return div.innerHTML;
};

/**
 * Deep clone an object
 * @param {any} obj - Object to clone
 * @returns {any} Cloned object
 */
export const deepClone = (obj) => {
  return JSON.parse(JSON.stringify(obj));
};

/**
 * Check if a value is empty
 * @param {any} value - Value to check
 * @returns {boolean} True if empty
 */
export const isEmpty = (value) => {
  return value == null || value === '' || 
         (Array.isArray(value) && value.length === 0) ||
         (typeof value === 'object' && Object.keys(value).length === 0);
};

/**
 * Validate email address
 * @param {string} email - Email to validate
 * @returns {boolean} True if valid email
 */
export const isValidEmail = (email) => {
  const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
  return emailRegex.test(email);
};

/**
 * Parse query parameters from URL
 * @param {string} url - URL to parse
 * @returns {Object} Query parameters object
 */
export const parseQueryParams = (url = window.location.href) => {
  const params = {};
  const urlObj = new URL(url);
  
  for (const [key, value] of urlObj.searchParams) {
    params[key] = value;
  }
  
  return params;
};

/**
 * Local storage wrapper with error handling
 */
export const storage = {
  get(key, defaultValue = null) {
    try {
      const item = localStorage.getItem(key);
      return item ? JSON.parse(item) : defaultValue;
    } catch (error) {
      console.error(`Error getting ${key} from localStorage:`, error);
      return defaultValue;
    }
  },
  
  set(key, value) {
    try {
      localStorage.setItem(key, JSON.stringify(value));
      return true;
    } catch (error) {
      console.error(`Error setting ${key} in localStorage:`, error);
      return false;
    }
  },
  
  remove(key) {
    try {
      localStorage.removeItem(key);
      return true;
    } catch (error) {
      console.error(`Error removing ${key} from localStorage:`, error);
      return false;
    }
  },
  
  clear() {
    try {
      localStorage.clear();
      return true;
    } catch (error) {
      console.error('Error clearing localStorage:', error);
      return false;
    }
  }
};

/**
 * Async/await wrapper with error handling
 * @param {Promise} promise - Promise to wrap
 * @returns {Promise<[error, data]>} Tuple with error and data
 */
export const to = (promise) => {
  return promise
    .then(data => [null, data])
    .catch(error => [error, null]);
};

/**
 * Sleep function for testing/demos
 * @param {number} ms - Milliseconds to sleep
 * @returns {Promise} Promise that resolves after delay
 */
export const sleep = (ms) => {
  return new Promise(resolve => setTimeout(resolve, ms));
};

/**
 * Random number between min and max
 * @param {number} min - Minimum value
 * @param {number} max - Maximum value
 * @returns {number} Random number
 */
export const randomBetween = (min, max) => {
  return Math.floor(Math.random() * (max - min + 1)) + min;
};

/**
 * Capitalize first letter of string
 * @param {string} str - String to capitalize
 * @returns {string} Capitalized string
 */
export const capitalize = (str) => {
  return str.charAt(0).toUpperCase() + str.slice(1);
};

/**
 * Convert string to kebab-case
 * @param {string} str - String to convert
 * @returns {string} Kebab-case string
 */
export const kebabCase = (str) => {
  return str
    .replace(/([a-z])([A-Z])/g, '$1-$2')
    .replace(/[\s_]+/g, '-')
    .toLowerCase();
};

/**
 * Convert string to camelCase
 * @param {string} str - String to convert
 * @returns {string} CamelCase string
 */
export const camelCase = (str) => {
  return str
    .replace(/[-_\s]+(.)?/g, (_, char) => char ? char.toUpperCase() : '')
    .replace(/^[A-Z]/, char => char.toLowerCase());
};
```

### Exercise 5.3: Error Handling Strategy

Implement comprehensive error handling throughout your application:

1. **Form Validation Errors**
2. **Storage Errors** (localStorage full/unavailable)
3. **File Import Errors** (invalid JSON, corrupted files)
4. **Runtime Errors** (unexpected exceptions)

Create `docs/error-handling.md` documenting your strategy.

### Exercise 5.4: Performance Optimization

Implement performance best practices:

1. **Event Delegation** for dynamic task elements
2. **Debouncing** for search input
3. **Virtual Scrolling** for large task lists (optional)
4. **Lazy Loading** for non-critical features

Document your optimizations in `docs/performance.md`.

### Success Criteria
- [ ] Complete JavaScript functionality implemented
- [ ] All CRUD operations working (Create, Read, Update, Delete)
- [ ] Local storage persistence functional
- [ ] Search and filtering operational
- [ ] Import/export features working
- [ ] Theme switching implemented
- [ ] Error handling comprehensive
- [ ] Keyboard shortcuts functional
- [ ] Performance optimizations applied

### 💡 Key Learning Points

1. **Modern JavaScript**: ES6+ features in real application
2. **Class-based Architecture**: Organizing complex functionality
3. **Event Handling**: Managing user interactions efficiently
4. **Data Persistence**: Local storage with error handling
5. **Error Management**: Graceful failure and user feedback
6. **Performance**: Optimizing for smooth user experience

### 🎯 Looking Ahead

In Chapter 6, you'll convert this vanilla JavaScript application to Vue.js, seeing firsthand how Vue.js simplifies complex state management and reactive updates. The solid foundation you've built here will make the Vue.js conversion much more meaningful.

**Your dashboard is now fully functional** - experience the power of modern JavaScript before transitioning to Vue.js!