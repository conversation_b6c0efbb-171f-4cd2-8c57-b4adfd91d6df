# Developer Guide — APIRecoater_Ethernet

This guide is structured for quick onboarding, task-focused workflows, deep architectural understanding, and reliable reference. It maps to four doc types: Tutorials (Quick Start), How-to Guides (task recipes), Explanation (Architecture), and Reference (API).

## Table of Contents

- [1. Introduction](#1-introduction)
  - [1.1 Project overview](#11-project-overview)
  - [1.2 Target audience](#12-target-audience)
  - [1.3 Prerequisites](#13-prerequisites)
  - [1.4 Technology stack](#14-technology-stack)
- [2. Quick Start (Tutorials)](#2-quick-start-tutorials)
  - [2.1 Clone and setup](#21-clone-and-setup)
  - [2.2 Running the application](#22-running-the-application)
  - [2.3 First steps](#23-first-steps)
  - [2.4 Verification](#24-verification)
- [3. How-to Guides](#3-how-to-guides)
  - [3.1 Add a new REST endpoint](#31-add-a-new-rest-endpoint)
  - [3.2 Add a new recoater command (backend to hardware)](#32-add-a-new-recoater-command-backend-to-hardware)
  - [3.3 Integrate a new OPC UA variable mapping](#33-integrate-a-new-opc-ua-variable-mapping)
  - [3.4 Add a new WebSocket event and consume it in frontend](#34-add-a-new-websocket-event-and-consume-it-in-frontend)
  - [3.5 Run the app fully offline with mocks](#35-run-the-app-fully-offline-with-mocks)
  - [3.6 Add a new page and state module in the frontend](#36-add-a-new-page-and-state-module-in-the-frontend)
  - [3.7 Extend error handling with new error code and UI surface](#37-extend-error-handling-with-new-error-code-and-ui-surface)
  - [3.8 Profile a slow service and add logging](#38-profile-a-slow-service-and-add-logging)
- [4. Architecture (Explanation)](#4-architecture-explanation)
  - [4.1 System design](#41-system-design)
  - [4.2 Communication architecture](#42-communication-architecture)
  - [4.3 Integration patterns](#43-integration-patterns)
  - [4.4 Cross-cutting concerns](#44-cross-cutting-concerns)
- [5. Backend Development](#5-backend-development)
  - [5.1 Clean Architecture Overview](#51-clean-architecture-overview)
  - [5.2 Presentation Layer](#52-presentation-layer)
    - [5.2.1 API Structure and Organization](#521-api-structure-and-organization)
    - [5.2.2 Configuration Management API](#522-configuration-management-api)
    - [5.2.3 System Status and Health API](#523-system-status-and-health-api)
    - [5.2.4 Error Management API](#524-error-management-api)
    - [5.2.5 Print Job Management APIs](#525-print-job-management-apis)
      - [******* CLI File Processing API](#5251-cli-file-processing-api)
      - [******* Legacy Drum Management API](#5252-legacy-drum-management-api)
      - [5.2.5.3 Basic Print Job API (Deprecated)](#5253-basic-print-job-api-deprecated)
      - [5.2.5.4 Layer Management API](#5254-layer-management-api)
      - [******* Multi-Material Job Orchestration API](#5255-multi-material-job-orchestration-api)
    - [5.2.6 Recoater Hardware Control APIs](#526-recoater-hardware-control-apis)
      - [5.2.6.1 Drum Control API](#5261-drum-control-api)
      - [5.2.6.2 Leveler Control API](#5262-leveler-control-api)
      - [5.2.6.3 Blade Control API](#5263-blade-control-api)
    - [5.2.7 API Design Patterns and Best Practices](#527-api-design-patterns-and-best-practices)
  - [5.3 Service Layer (Business Logic)](#53-service-layer-business-logic)
    - [5.3.1 Communication Services](#531-communication-services)
    - [5.3.2 Job Management Services](#532-job-management-services)
    - [5.3.3 Monitoring Services](#533-monitoring-services)
    - [5.3.4 OPC UA Services](#534-opc-ua-services)
  - [5.4 Infrastructure Layer](#54-infrastructure-layer)
    - [5.4.1 CLI Editor System](#541-cli-editor-system)
      - [******* CLI Data Models and Exceptions](#5411-cli-data-models-and-exceptions)
      - [******* Parser Implementations](#5412-parser-implementations)
      - [******* Generator Implementations](#5413-generator-implementations)
      - [5.4.1.4 CLI Renderer and Visualization](#5414-cli-renderer-and-visualization)
      - [******* Unified CLI Editor Interface](#5415-unified-cli-editor-interface)
    - [5.4.2 Recoater Hardware Client](#542-recoater-hardware-client)
      - [******* Main Hardware Client](#5421-main-hardware-client)
      - [5.4.2.2 Async Client Wrapper](#5422-async-client-wrapper)
      - [5.4.2.3 Specialized Control Modules](#5423-specialized-control-modules)
      - [5.4.2.4 File Management](#5424-file-management)
      - [5.4.2.5 Hardware Communication Exceptions](#5425-hardware-communication-exceptions)
    - [5.4.3 Mock Recoater Client](#543-mock-recoater-client)
      - [******* Mock Implementation Strategy](#5431-mock-implementation-strategy)
      - [******* Mock Client Components](#5432-mock-client-components)
      - [5.4.3.3 Mock Control Modules](#5433-mock-control-modules)
    - [5.4.4 Infrastructure Design Patterns](#544-infrastructure-design-patterns)
- [6. Frontend Development](#6-frontend-development)
  - [6.1 Layered architecture](#61-layered-architecture)
  - [6.2 Component development](#62-component-development)
  - [6.3 State management](#63-state-management)
  - [6.4 Routing](#64-routing)
  - [6.5 API integration](#65-api-integration)
  - [6.6 Real-time features](#66-real-time-features)
  - [6.7 Styling and UI](#67-styling-and-ui)
- [7. Testing](#7-testing)
  - [7.1 Strategy](#71-strategy)
  - [7.2 Backend testing](#72-backend-testing)
  - [7.3 Frontend testing](#73-frontend-testing)
  - [7.4 Test environment setup](#74-test-environment-setup)
  - [7.5 Running tests](#75-running-tests)
  - [7.6 Coverage](#76-coverage)
- [8. API Documentation (Reference)](#8-api-documentation-reference)
  - [8.1 API overview](#81-api-overview)
  - [8.2 Authentication](#82-authentication)
  - [8.3 Endpoint reference](#83-endpoint-reference)
  - [8.4 WebSocket events](#84-websocket-events)
  - [8.5 Error codes](#85-error-codes)
  - [8.6 Rate limiting](#86-rate-limiting)
- [9. Appendices](#9-appendices)
  - [A. Glossary](#a-glossary)
  - [B. Useful commands](#b-useful-commands)
  - [C. External resources](#c-external-resources)
  - [D. Contributing and style](#d-contributing-and-style)
  - [Navigation map](#navigation-map)

***

## 1. Introduction

### 1.1 Project overview

APIRecoater_Ethernet is a modern, intuitive web-based Human-Machine Interface (HMI) for the Aerosint SPD Recoater system. It replaces the default SwaggerUI with a user-friendly interface that enhances usability for operators, developers, and stakeholders in Laser Powder Bed Fusion workflows. The system provides real-time monitoring and control of recoater hardware, enables multi-layer print job execution, and offers robust file management capabilities for both industrial and laboratory environments.

### 1.2 Target audience

- Backend and frontend developers joining the project
- Test engineers validating behaviors against mocked or production hardware
- Operators or integrators consuming the REST/WebSocket API
- Industrial control system engineers working with recoater hardware

### 1.3 Prerequisites

- Backend: Python 3.9+, virtualenv or uv, pip, basic FastAPI and asyncio familiarity
- Frontend: Node.js v16+ LTS, npm or pnpm, basic Vue 3 and Pinia familiarity
- Optional: Local OPC UA server/hardware access, Docker for services where applicable
- Hardware: Access to Aerosint Recoater system (or mock mode for development)

### 1.4 Technology stack

- Backend: FastAPI, Pydantic, WebSocket, OPC UA client library, uvicorn, pytest
- Frontend: Vue 3 (Composition API), Pinia, Vue Router, Axios HTTP client, Vite
- Communication: REST API, WebSocket real-time updates, OPC UA industrial protocol
- Tooling: Python/JS test frameworks, ESLint, Prettier

***

## 2. Quick Start (Tutorials)

### 2.1 Clone and setup

```bash
# Backend env
cd backend
python -m venv venv
source venv/bin/activate   # Windows: venv\Scripts\activate
pip install -r requirements.txt
cp .env.example .env

# Frontend env
cd ../frontend
npm install   # or: pnpm install
```

### 2.2 Running the application

```bash
# Backend (from backend/)
uvicorn app.main:app --reload --host 0.0.0.0 --port 8000

# Frontend (from frontend/)
npm run dev
```

### 2.3 First steps

- Visit the frontend dev URL (http://localhost:5173)
- The frontend should connect to the backend at http://localhost:8000
- Ensure environment files (.env, frontend .env.*) are configured as needed
- Navigate to Status view to verify system connectivity
- Access Recoater view for hardware controls (mock mode available)

### 2.4 Verification

- REST: GET /status should return system status JSON
- WebSocket: Connect to ws://localhost:8000/ws and receive heartbeat/events
- OPC UA (if available): Validate basic read/write from hardware abstraction layer
- UI: Check all navigation routes load without errors
- Mock Mode: Verify drum, hopper, and leveler controls respond in mock mode

***

## 3. How-to Guides

Task recipes for common developer workflows:

### 3.1 Add a new REST endpoint
1. Define Pydantic models in `backend/app/api/*/models.py`
2. Create endpoint function in appropriate router file
3. Add route to FastAPI router with proper tags and documentation
4. Update frontend API service with new endpoint method
5. Test with both unit tests and integration tests

### 3.2 Add a new recoater command (backend to hardware)
1. Define command in appropriate infrastructure client (`recoater_client/`)
2. Add corresponding mock implementation in `mock_recoater_client/`
3. Create service layer method to orchestrate the command
4. Add API endpoint to expose the command via REST
5. Update frontend controls to trigger the new command

### 3.3 Integrate a new OPC UA variable mapping
1. Update OPC UA configuration in `backend/app/config/opcua_config.py`
2. Add variable mapping in OPC UA service
3. Update data models to include new variable
4. Modify status poller to collect new data
5. Update frontend status displays to show new information

### 3.4 Add a new WebSocket event and consume it in frontend
1. Define event model in backend WebSocket manager
2. Emit event from appropriate service/endpoint
3. Add event handler in frontend status store
4. Update UI components to react to new event
5. Test real-time behavior and error scenarios

### 3.5 Run the app fully offline with mocks
1. Set `USE_MOCK_CLIENT=true` in backend .env
2. Configure mock behavior in `mock_recoater_client/` modules
3. Start backend and frontend as normal
4. Verify all hardware operations use mock responses
5. Test complete workflows without physical hardware

### 3.6 Add a new page and state module in the frontend
1. Create new Vue component in `frontend/src/views/`
2. Add route configuration in `frontend/src/router/index.js`
3. Create Pinia store in `frontend/src/stores/`
4. Implement API service methods for data fetching
5. Add navigation link and test routing

### 3.7 Extend error handling with new error code and UI surface
1. Add error code to backend error registry
2. Update API endpoints to return structured error
3. Add error handling in frontend API interceptors
4. Create/update UI error display components
5. Test error propagation from backend to UI

### 3.8 Profile a slow service and add logging
1. Add timing decorators to service methods
2. Configure logging in backend with appropriate levels
3. Add performance metrics collection points
4. Use FastAPI middleware for request/response timing
5. Monitor logs and optimize bottlenecks

***

## 4. Architecture (Explanation)

### 4.1 System design

The APIRecoater_Ethernet system follows a modern client-server architecture with clear separation of concerns and comprehensive real-time communication capabilities, designed specifically for industrial 3D printing environments.

**Complete System Architecture - Big Picture:**

```mermaid
graph TB
    subgraph "User Interface Layer"
        OPERATOR["Operator Workstation<br/>Web Browser Interface"]
        ENGINEER["Engineer Terminal<br/>Development & Debugging"]
        MOBILE["Mobile Device<br/>Status Monitoring"]
    end
    
    subgraph "Frontend Application Layer"
        VUEJS["Vue.js 3 Frontend<br/>• Composition API<br/>• TypeScript Support<br/>• Responsive Design"]
        PINIA["Pinia State Management<br/>• Reactive State<br/>• Action Dispatching<br/>• Persistent Storage"]
        ROUTER["Vue Router<br/>• Route Guards<br/>• Lazy Loading<br/>• Navigation Management"]
        APISERVICE["API Service Layer<br/>• HTTP Client (Axios)<br/>• WebSocket Client<br/>• Error Interceptors"]
    end
    
    subgraph "Backend Application Layer"
        FASTAPI["FastAPI Server<br/>• Async/Await Support<br/>• Auto Documentation<br/>• Type Validation"]
        MIDDLEWARE["Middleware Stack<br/>• CORS Handler<br/>• Request Logging<br/>• Rate Limiting"]
        ROUTERS["API Routers<br/>• Print Management<br/>• Hardware Control<br/>• System Status"]
    end
    
    subgraph "Business Logic Layer"
        JOBMGMT["Job Management<br/>• Multi-material Coordination<br/>• Layer Processing<br/>• Error Recovery"]
        MONITORING["Monitoring Services<br/>• Real-time Data Collection<br/>• Performance Metrics<br/>• Health Checks"]
        COMMS["Communication Hub<br/>• WebSocket Management<br/>• Event Broadcasting<br/>• Subscription Filtering"]
    end
    
    subgraph "Infrastructure & Integration Layer"
        CLIEDITOR["CLI File Editor<br/>• ASCII/Binary Parsing<br/>• Layer Extraction<br/>• Preview Generation"]
        HWCLIENT["Hardware Client<br/>• REST API Integration<br/>• Async Operations<br/>• Error Handling"]
        OPCUA["OPC UA Service<br/>• Industrial Protocol<br/>• Variable Management<br/>• PLC Coordination"]
        MOCKCLIENT["Mock Client<br/>• Development Mode<br/>• Simulation Engine<br/>• Test Scenarios"]
    end
    
    subgraph "Industrial Network Infrastructure"
        FIREWALL["Industrial Firewall<br/>• Network Segmentation<br/>• Access Control<br/>• Intrusion Detection"]
        SWITCH["Managed Switch<br/>• VLAN Configuration<br/>• QoS Management<br/>• Port Security"]
        OPCSERVER["OPC UA Server<br/>• Data Acquisition<br/>• Historical Logging<br/>• Alarm Management"]
    end
    
    subgraph "Hardware & Control Systems"
        RECOATER["Aerosint Recoater<br/>• 3x Material Drums<br/>• Powder Leveling System<br/>• Blade Mechanisms<br/>• Pressure Controls"]
        PLC["PLC Controller<br/>• Motion Control<br/>• Safety Interlocks<br/>• I/O Management<br/>• Process Control"]
        SENSORS["Sensor Network<br/>• Pressure Transducers<br/>• Position Encoders<br/>• Temperature Sensors<br/>• Flow Meters"]
        ACTUATORS["Actuator Systems<br/>• Servo Motors<br/>• Pneumatic Cylinders<br/>• Proportional Valves<br/>• Safety Stops"]
    end
    
    subgraph "External Manufacturing Systems"
        MES["MES Integration<br/>• Production Planning<br/>• Work Order Management<br/>• Quality Control<br/>• Traceability"]
        SCADA["SCADA System<br/>• Plant Supervision<br/>• Historical Trending<br/>• Alarm Management<br/>• Reporting"]
        ERP["ERP System<br/>• Material Planning<br/>• Cost Tracking<br/>• Inventory Management<br/>• Order Processing"]
        LIMS["LIMS System<br/>• Quality Data<br/>• Test Results<br/>• Compliance Tracking<br/>• Batch Records"]
    end
    
    subgraph "Development & Operations"
        DEVTOOLS["Dev Environment<br/>• Hot Reload<br/>• Debug Tools<br/>• Test Suites<br/>• Mock Data"]
        CICD["CI/CD Pipeline<br/>• Automated Testing<br/>• Deployment<br/>• Version Control<br/>• Release Management"]
        MONITORING_OPS["Operations Monitoring<br/>• Application Metrics<br/>• Infrastructure Health<br/>• Performance Analysis<br/>• Alert Management"]
    end
    
    %% User Interface Connections
    OPERATOR --> VUEJS
    ENGINEER --> VUEJS
    MOBILE --> VUEJS
    
    %% Frontend Layer Connections
    VUEJS --> PINIA
    VUEJS --> ROUTER
    PINIA --> APISERVICE
    ROUTER --> APISERVICE
    
    %% Backend Connections
    APISERVICE -.->|"HTTPS/WSS"| FASTAPI
    FASTAPI --> MIDDLEWARE
    MIDDLEWARE --> ROUTERS
    
    %% Business Logic Connections
    ROUTERS --> JOBMGMT
    ROUTERS --> MONITORING
    ROUTERS --> COMMS
    COMMS --> MONITORING
    
    %% Infrastructure Connections
    JOBMGMT --> CLIEDITOR
    JOBMGMT --> HWCLIENT
    JOBMGMT --> OPCUA
    MONITORING --> HWCLIENT
    HWCLIENT -.->|"Dev Mode"| MOCKCLIENT
    
    %% Network Infrastructure
    HWCLIENT -.->|"Industrial Network"| FIREWALL
    OPCUA -.->|"OPC UA/TCP"| FIREWALL
    FIREWALL --> SWITCH
    SWITCH --> OPCSERVER
    
    %% Hardware Connections
    OPCSERVER --> PLC
    HWCLIENT -.->|"REST API"| RECOATER
    PLC --> SENSORS
    PLC --> ACTUATORS
    RECOATER --> SENSORS
    RECOATER --> ACTUATORS
    
    %% External System Integration
    OPCSERVER -.->|"Production Data"| MES
    MES -.->|"Work Orders"| ERP
    OPCSERVER -.->|"Process Data"| SCADA
    SCADA -.->|"Quality Data"| LIMS
    
    %% Development Integration
    VUEJS -.->|"Development"| DEVTOOLS
    FASTAPI -.->|"Deployment"| CICD
    FASTAPI -.->|"Monitoring"| MONITORING_OPS
```

**Comprehensive Data Flow Architecture:**

```mermaid
sequenceDiagram
    participant Operator as Operator
    participant UI as Vue.js Frontend
    participant State as Pinia Store
    participant API as API Service
    participant Backend as FastAPI
    participant Services as Business Services
    participant Infrastructure as Infrastructure
    participant Hardware as Hardware Systems
    participant PLC as PLC Controller
    participant MES as MES System
    
    Note over Operator, MES: Complete Production Workflow
    
    %% Job Initiation
    Operator->>UI: Upload CLI File & Start Job
    UI->>State: Dispatch Job Action
    State->>API: POST /api/cli/upload
    API->>Backend: HTTP Request
    Backend->>Services: Process CLI File
    Services->>Infrastructure: Parse & Cache File
    Infrastructure->>Services: File Ready
    Services->>Backend: Job Prepared
    Backend->>API: HTTP 200 + Job ID
    API->>State: Update Job State
    State->>UI: Reactive Update
    UI->>Operator: Job Started Confirmation
    
    %% Production Coordination
    Services->>Infrastructure: Setup OPC UA Variables
    Infrastructure->>PLC: Write Job Variables
    PLC->>Infrastructure: Acknowledge Setup
    Services->>Hardware: Initialize Hardware
    Hardware->>Services: Hardware Ready
    
    %% Layer Processing Loop
    loop Each Layer
        Services->>Infrastructure: Upload Layer Data
        Infrastructure->>Hardware: Send Layer Commands
        Hardware->>PLC: Coordinate Motion
        PLC->>Hardware: Execute Layer
        
        %% Real-time Status Updates
        par Status Broadcasting
            Hardware->>Services: Hardware Status
            PLC->>Infrastructure: Process Variables
            Infrastructure->>Services: Industrial Data
            Services->>Backend: Combined Status
            Backend->>API: WebSocket Broadcast
            API->>State: Real-time Update
            State->>UI: Live Progress
            UI->>Operator: Visual Feedback
        and External Reporting
            Services->>MES: Production Progress
            MES->>ERP: Material Consumption
        end
        
        Hardware->>PLC: Layer Complete Signal
        PLC->>Infrastructure: Layer Finished
        Infrastructure->>Services: Next Layer Ready
    end
    
    %% Job Completion
    Services->>Infrastructure: Job Complete
    Infrastructure->>PLC: Reset Variables
    Services->>MES: Final Production Data
    Services->>Backend: Job Finished
    Backend->>API: Completion Event
    API->>State: Job Complete
    State->>UI: Success Notification
    UI->>Operator: Job Complete
```

- **Client-server model** with a typed REST API and real-time WebSocket channel
- **Components**: frontend (views/stores/services), backend (presentation/service/infrastructure), hardware abstraction
- **Data flow**: UI → stores → services → API/WebSocket → services/HAL → hardware and back
- **Separation of concerns** with clear architectural boundaries
- **Industrial integration** with MES, SCADA, ERP, and LIMS systems
- **Development support** with hot reload, testing, and mock environments

### 4.2 Communication architecture

The system implements multiple communication patterns optimized for different use cases in industrial automation, creating a comprehensive ecosystem that bridges web technologies with industrial protocols.

**Complete Communication Ecosystem:**

```mermaid
graph TB
    subgraph "Frontend Layer"
        UI["Vue.js Components"]
        STORE["Pinia State Management"]
        APISERVICE["Axios HTTP Client"]
        WSCLIENT["WebSocket Client"]
    end
    
    subgraph "Backend Communication Layer"
        FASTAPI["FastAPI Application"]
        WSMANAGER["WebSocket Manager"]
        MIDDLEWARE["HTTP Middleware"]
        CORS["CORS Handler"]
    end
    
    subgraph "Business Services"
        JOBSERVICE["Job Management Service"]
        MONITORING["Monitoring Service"]
        OPCSERVICE["OPC UA Service"]
        DATAPOLLER["Status Poller"]
    end
    
    subgraph "Infrastructure Clients"
        HWCLIENT["Hardware REST Client"]
        OPCUACLIENT["OPC UA Client"]
        MOCKCLIENT["Mock Client (Dev Mode)"]
    end
    
    subgraph "Industrial Network Layer"
        ETHERNET["Industrial Ethernet"]
        OPCUASERVER["OPC UA Server"]
        MODBUS["Modbus TCP (Optional)"]
    end
    
    subgraph "Hardware Systems"
        RECOATER["Aerosint Recoater<br/>• Drum Controls<br/>• Leveler System<br/>• Blade Mechanisms"]
        PLC["PLC Controller<br/>• Motion Control<br/>• Safety Systems<br/>• I/O Management"]
        SENSORS["Sensor Network<br/>• Pressure Sensors<br/>• Position Sensors<br/>• Temperature Monitoring"]
        ACTUATORS["Actuator Systems<br/>• Servo Motors<br/>• Pneumatic Controls<br/>• Valve Controls"]
    end
    
    subgraph "External Systems"
        MES["MES System<br/>(Manufacturing Execution)"]
        SCADA["SCADA System<br/>(Supervisory Control)"]
        DATABASE["Production Database"]
        FILESERVER["File Server<br/>(CLI Files & Logs)"]
    end
    
    %% Frontend Connections
    UI --> STORE
    STORE --> APISERVICE
    STORE --> WSCLIENT
    
    %% HTTP Communication
    APISERVICE -.->|"HTTPS/REST<br/>Commands & Config"| FASTAPI
    FASTAPI --> MIDDLEWARE
    MIDDLEWARE --> CORS
    
    %% WebSocket Communication
    WSCLIENT -.->|"WSS<br/>Real-time Data"| WSMANAGER
    WSMANAGER --> DATAPOLLER
    
    %% Service Layer
    FASTAPI --> JOBSERVICE
    FASTAPI --> MONITORING
    WSMANAGER --> MONITORING
    JOBSERVICE --> OPCSERVICE
    MONITORING --> DATAPOLLER
    
    %% Infrastructure Layer
    JOBSERVICE --> HWCLIENT
    MONITORING --> HWCLIENT
    OPCSERVICE --> OPCUACLIENT
    
    %% Development Mode
    HWCLIENT -.->|"Mock Mode"| MOCKCLIENT
    
    %% Industrial Network
    HWCLIENT -.->|"HTTP/REST<br/>Hardware API"| ETHERNET
    OPCUACLIENT -.->|"OPC UA Protocol<br/>Industrial Data"| OPCUASERVER
    
    %% Hardware Connections
    ETHERNET --> RECOATER
    OPCUASERVER --> PLC
    PLC --> SENSORS
    PLC --> ACTUATORS
    RECOATER --> SENSORS
    RECOATER --> ACTUATORS
    
    %% External System Integration
    OPCUASERVER -.->|"Production Data"| MES
    DATABASE -.->|"Historical Data"| SCADA
    FILESERVER -.->|"CLI Files"| HWCLIENT
    
    %% Styling
    style UI fill:#e3f2fd
    style RECOATER fill:#ffebee
    style PLC fill:#ffebee
    style OPCUASERVER fill:#fff3e0
    style MES fill:#f1f8e9
    style SCADA fill:#f1f8e9
```

**Communication Protocol Details:**

```mermaid
sequenceDiagram
    participant Frontend as Vue.js Frontend
    participant Backend as FastAPI Backend
    participant Services as Business Services
    participant OPC as OPC UA Client
    participant Hardware as Recoater Hardware
    participant PLC as PLC System
    
    Note over Frontend, PLC: Multi-Protocol Communication Flow
    
    %% HTTP Command Flow
    Frontend->>Backend: HTTP POST /api/print/start
    Backend->>Services: Process Job Command
    Services->>Hardware: REST API Call
    Hardware->>Services: Command Response
    Services->>Backend: Job Status
    Backend->>Frontend: HTTP 200 + Job ID
    
    %% OPC UA Coordination
    Services->>OPC: Setup Job Variables
    OPC->>PLC: Write OPC UA Variables
    PLC->>OPC: Acknowledge Variables
    
    %% Real-time Status Loop
    loop Real-time Updates
        Hardware->>Services: Hardware Status
        PLC->>OPC: Process Variables
        OPC->>Services: Industrial Data
        Services->>Backend: Combined Status
        Backend->>Frontend: WebSocket Broadcast
    end
    
    %% Error Handling Flow
    alt Hardware Error
        Hardware->>Services: Error Response
        Services->>OPC: Set Error Flags
        OPC->>PLC: Error Variables
        PLC->>OPC: Safety Response
        Services->>Backend: Error Status
        Backend->>Frontend: Error Notification
    end
```

**Network Architecture and Security:**

```mermaid
graph LR
    subgraph "DMZ Network"
        WEBSERVER["Web Server<br/>Frontend Assets"]
        APIGATEWAY["API Gateway<br/>Rate Limiting"]
    end
    
    subgraph "Application Network"
        BACKEND["Backend Services<br/>FastAPI + WebSocket"]
        REDIS["Redis Cache<br/>Session Management"]
    end
    
    subgraph "Industrial Network (Isolated)"
        OPCUA["OPC UA Server<br/>Industrial Protocol"]
        HARDWARE["Hardware Controllers<br/>Recoater + PLC"]
        FIELDBUS["Fieldbus Network<br/>Sensors + Actuators"]
    end
    
    subgraph "Security Layer"
        FIREWALL["Industrial Firewall"]
        VPN["VPN Gateway"]
        CERTIFICATES["TLS/SSL Certificates"]
    end
    
    %% Network Connections
    WEBSERVER -.->|"HTTPS"| APIGATEWAY
    APIGATEWAY -.->|"Internal HTTP"| BACKEND
    BACKEND -.->|"Cache"| REDIS
    
    BACKEND -.->|"Secure Bridge"| FIREWALL
    FIREWALL -.->|"OPC UA/TCP"| OPCUA
    OPCUA -.->|"Industrial Ethernet"| HARDWARE
    HARDWARE -.->|"Fieldbus"| FIELDBUS
    
    VPN -.->|"Remote Access"| FIREWALL
    CERTIFICATES -.->|"Encryption"| OPCUA
    
    style WEBSERVER fill:#e8f5e8
    style BACKEND fill:#e3f2fd
    style OPCUA fill:#fff3e0
    style HARDWARE fill:#ffebee
    style FIREWALL fill:#fce4ec
```

- **REST** for command, control, and configuration
- **WebSocket** for status streaming, events, and live telemetry
- **OPC UA client** for industrial device communication and data exchange
- **Industrial Ethernet** for reliable hardware communication
- **Error propagation**: structured error model from hardware → OPC UA → services → backend → client interceptors → UI surfaces

### 4.3 Integration patterns

The system uses several integration patterns to ensure robust communication between different architectural layers, supporting both development workflows and production manufacturing environments.

**Enterprise Integration Architecture:**

```mermaid
graph TB
    subgraph "Development Environment"
        DEVPC["Developer Workstation<br/>• Hot Reload Development<br/>• Mock Hardware Mode<br/>• Unit Test Execution<br/>• Debug Tools"]
        TESTRIG["Test Hardware Rig<br/>• Isolated Test Environment<br/>• Safe Testing Procedures<br/>• Hardware Simulation<br/>• Integration Validation"]
        CICD["CI/CD Pipeline<br/>• Automated Testing<br/>• Code Quality Gates<br/>• Deployment Automation<br/>• Version Management"]
    end
    
    subgraph "Application Layer Integration"
        FACTORY["Dependency Factory<br/>• Configuration-Driven Creation<br/>• Mock/Real Client Selection<br/>• Lifecycle Management<br/>• Resource Cleanup"]
        ADAPTER["Protocol Adapters<br/>• HTTP to Hardware REST<br/>• WebSocket to Event Bus<br/>• OPC UA to Industrial Bus<br/>• Error Translation"]
        MEDIATOR["Service Mediator<br/>• Cross-Service Communication<br/>• Event Orchestration<br/>• Transaction Coordination<br/>• State Synchronization"]
    end
    
    subgraph "Hardware Abstraction Layer (HAL)"
        INTERFACE["Hardware Interface<br/>• Unified API Contract<br/>• Platform Independence<br/>• Error Standardization<br/>• Performance Monitoring"]
        REALCLIENT["Real Hardware Client<br/>• Aerosint API Integration<br/>• Network Resilience<br/>• Error Recovery<br/>• Performance Optimization"]
        MOCKCLIENT["Mock Hardware Client<br/>• Realistic Simulation<br/>• State Management<br/>• Error Injection<br/>• Development Speed"]
        PROXYCLIENT["Proxy Client<br/>• Remote Hardware Access<br/>• Network Bridging<br/>• Security Layer<br/>• Connection Pooling"]
    end
    
    subgraph "Data Integration Layer"
        TRANSFORM["Data Transformers<br/>• Format Conversion<br/>• Unit Standardization<br/>• Validation Rules<br/>• Schema Evolution"]
        CACHE["Intelligent Caching<br/>• CLI File Storage<br/>• Hardware State Cache<br/>• Performance Optimization<br/>• Memory Management"]
        QUEUE["Message Queues<br/>• Async Job Processing<br/>• Event Buffering<br/>• Retry Logic<br/>• Dead Letter Handling"]
    end
    
    subgraph "Production Hardware"
        RECOATER["Production Recoater<br/>• Multi-Drum System<br/>• Precision Control<br/>• Safety Interlocks<br/>• Quality Monitoring"]
        PLC["Production PLC<br/>• Real-time Control<br/>• Safety Systems<br/>• Process Optimization<br/>• Data Logging"]
        LABWARE["Lab Equipment<br/>• Material Testing<br/>• Quality Control<br/>• Calibration Tools<br/>• Measurement Systems"]
    end
    
    subgraph "Enterprise Systems"
        MES["Manufacturing Execution<br/>• Production Scheduling<br/>• Resource Allocation<br/>• Quality Management<br/>• Compliance Tracking"]
        ERP["Enterprise Resource Planning<br/>• Material Management<br/>• Cost Accounting<br/>• Supply Chain<br/>• Financial Integration"]
        PLM["Product Lifecycle Management<br/>• Design Revision Control<br/>• Change Management<br/>• Document Control<br/>• Regulatory Compliance"]
        QMS["Quality Management System<br/>• Test Results<br/>• Deviation Tracking<br/>• CAPA Management<br/>• Audit Trail"]
    end
    
    %% Development Connections
    DEVPC -.->|"Mock Mode"| MOCKCLIENT
    TESTRIG -.->|"Safe Testing"| PROXYCLIENT
    CICD -.->|"Deployment"| FACTORY
    
    %% Application Integration
    FACTORY --> ADAPTER
    ADAPTER --> MEDIATOR
    MEDIATOR --> INTERFACE
    
    %% HAL Connections
    INTERFACE --> REALCLIENT
    INTERFACE --> MOCKCLIENT
    INTERFACE --> PROXYCLIENT
    
    %% Data Layer
    ADAPTER --> TRANSFORM
    MEDIATOR --> CACHE
    MEDIATOR --> QUEUE
    
    %% Hardware Connections
    REALCLIENT -.->|"Production API"| RECOATER
    REALCLIENT -.->|"OPC UA"| PLC
    PROXYCLIENT -.->|"Remote Access"| LABWARE
    
    %% Enterprise Integration
    RECOATER -.->|"Production Data"| MES
    MES -.->|"Planning Data"| ERP
    PLC -.->|"Process Data"| PLM
    QUEUE -.->|"Quality Data"| QMS
    
    style DEVPC fill:#e8f5e8
    style MOCKCLIENT fill:#fff3e0
    style REALCLIENT fill:#ffebee
    style MES fill:#e3f2fd
    style ERP fill:#f3e5f5
```

**Configuration-Driven Dependency Injection:**

```mermaid
classDiagram
    class ConfigurationManager {
        +load_environment() Config
        +get_hardware_mode() HardwareMode
        +get_network_settings() NetworkConfig
        +validate_configuration() bool
    }
    
    class ClientFactory {
        -config: ConfigurationManager
        +create_hardware_client() IHardwareClient
        +create_opcua_client() IOPCUAClient
        +get_cache_client() ICacheClient
        +cleanup_resources() void
    }
    
    class IHardwareClient {
        <<interface>>
        +get_status() HardwareStatus
        +control_drums() DrumResponse
        +manage_files() FileResponse
        +handle_errors() ErrorResponse
    }
    
    class ProductionClient {
        -endpoint: string
        -timeout: int
        -retry_policy: RetryPolicy
        +get_status() HardwareStatus
        +control_drums() DrumResponse
        +manage_files() FileResponse
        +handle_errors() ErrorResponse
    }
    
    class MockClient {
        -simulation_state: SimulationState
        -behavior_config: MockConfig
        +get_status() HardwareStatus
        +control_drums() DrumResponse
        +manage_files() FileResponse
        +handle_errors() ErrorResponse
        +inject_errors() void
        +simulate_delays() void
    }
    
    class ProxyClient {
        -remote_endpoint: string
        -security_context: SecurityContext
        +get_status() HardwareStatus
        +control_drums() DrumResponse
        +manage_files() FileResponse
        +handle_errors() ErrorResponse
        +establish_tunnel() void
    }
    
    class APIEndpoint {
        -client: IHardwareClient
        +__init__(client: Depends[ClientFactory])
        +process_request() Response
    }
    
    ConfigurationManager --> ClientFactory
    ClientFactory --> IHardwareClient
    IHardwareClient <|-- ProductionClient
    IHardwareClient <|-- MockClient
    IHardwareClient <|-- ProxyClient
    APIEndpoint --> ClientFactory
    
    note for ClientFactory "Factory creates appropriate\nclient based on configuration:\n- PRODUCTION: ProductionClient\n- DEVELOPMENT: MockClient\n- REMOTE: ProxyClient"
    note for IHardwareClient "Polymorphic interface allows\nseamless switching between\nimplementations"
```

**Event-Driven Integration Architecture:**

```mermaid
sequenceDiagram
    participant Config as Configuration
    participant Factory as Client Factory
    participant Service as Business Service
    participant HAL as Hardware Abstraction
    participant Hardware as Hardware System
    participant Events as Event Bus
    participant Monitoring as Monitoring
    participant External as External Systems
    
    Note over Config, External: Adaptive Integration Flow
    
    %% Configuration-Driven Setup
    Config->>Factory: Load Environment Settings
    Factory->>Factory: Determine Hardware Mode
    
    alt Production Mode
        Factory->>HAL: Create Production Client
        HAL->>Hardware: Establish Connection
        Hardware->>HAL: Connection Confirmed
    else Development Mode
        Factory->>HAL: Create Mock Client
        HAL->>HAL: Initialize Simulation
    else Remote Mode
        Factory->>HAL: Create Proxy Client
        HAL->>Hardware: Tunnel Connection
        Hardware->>HAL: Secure Channel
    end
    
    %% Service Integration
    Service->>Factory: Request Hardware Client
    Factory->>Service: Inject Configured Client
    
    %% Event-Driven Operations
    Service->>HAL: Execute Operation
    HAL->>Hardware: Hardware Command
    Hardware->>HAL: Operation Result
    HAL->>Events: Publish Event
    
    par Event Distribution
        Events->>Monitoring: Performance Metrics
        Events->>External: Production Updates
        Events->>Service: Status Notification
    end
    
    %% Error Handling Integration
    alt Error Condition
        Hardware->>HAL: Error Response
        HAL->>Events: Error Event
        Events->>Monitoring: Alert Triggered
        Events->>External: Fault Notification
        HAL->>Service: Structured Exception
        Service->>Service: Recovery Logic
    end
```

- **Frontend-backend alignment** with shared models and error shapes
- **Hardware Abstraction Layer (HAL)** encapsulates device specifics and sim/mocked modes
- **Mock vs production modes** toggled via config flags and dependency injection
- **Deployment architecture variants**: local dev, lab, production
- **Enterprise integration** with MES, ERP, PLM, and QMS systems
- **Event-driven architecture** for loose coupling and scalability
- **Configuration-driven adaptation** for different deployment environments



***

## 5. Backend Development

### 5.1 Clean architecture overview

- Principles: separation of concerns, explicit boundaries, dependency inversion
- Layers: presentation (FastAPI), application/services (use cases), domain, infrastructure (adapters)
- Domain-driven design concepts for stable core types and aggregates
- SLAP (Single Level of Abstraction Principle) and KISS principles

### 5.2 Presentation layer

The presentation layer implements the RESTful API interface using FastAPI, providing a clean separation between HTTP concerns and business logic. This layer handles request validation, response formatting, error handling, and OpenAPI documentation generation.

**Core Responsibilities:**
- FastAPI app structure and routers per domain area
- Endpoint design: paths, verbs, idempotency, pagination/filtering conventions
- Request/response models with Pydantic, validation, and examples
- Route organization by feature with tags and summaries
- Middleware for auth, correlation IDs, CORS, compression
- Error handling and HTTP status codes with structured problem details
- OpenAPI metadata, tags, and descriptions

**API Organization Overview:**
The APIRecoater_Ethernet backend exposes a comprehensive REST API organized into logical domain areas. Each API module serves specific aspects of the recoater system:

- **Configuration APIs** - System settings and hardware configuration management
- **Status & Health APIs** - System monitoring, diagnostics, and health checks
- **Error Management APIs** - Operator error recovery and system reset capabilities
- **Print Job APIs** - Complete print workflow from CLI upload to multi-material orchestration
- **Hardware Control APIs** - Direct hardware control for drums, leveler, and blade systems

The following subsections provide detailed documentation for each API domain, including endpoint specifications, request/response models, error handling patterns, and practical usage examples.

#### 5.2.1 API Structure and Organization

The APIRecoater_Ethernet backend follows RESTful design principles with clear separation of concerns. Each domain area has its own router module, making the codebase maintainable and scalable.

**API Architecture Overview:**

```mermaid
graph TB
    subgraph "FastAPI Application"
        subgraph "System APIs"
            STATUS["/api/status<br/>System monitoring & health"]
            CONFIG["/api/config<br/>Configuration management"]
            ERRORS["/api/errors<br/>Error handling & recovery"]
        end
        
        subgraph "Print Job Management APIs"
            CLI["/api/print/cli<br/>CLI file upload & processing"]
            DRUM["/api/print/drum<br/>Drum file operations (legacy)"]
            JOB["/api/print/job<br/>Basic print job control (deprecated)"]
            LAYER["/api/print/layer<br/>Layer parameters & preview"]
            MULTI["/api/print/multimaterial<br/>Multi-material job orchestration"]
        end
        
        subgraph "Hardware Control APIs"
            DRUMS["/api/recoater/drums/{id}<br/>Drum motion & pressure control"]
            LEVELER["/api/recoater/leveler<br/>Leveler pressure & sensor management"]
            BLADE["/api/recoater/blade<br/>Blade screw positioning"]
        end
    end
    
    subgraph "Design Principles"
        REST["RESTful Design"]
        DI["Dependency Injection"]
        VALID["Request/Response Validation"]
        ERROR["Structured Error Handling"]
    end
    
    subgraph "External Systems"
        HW["Recoater Hardware"]
        PLC["PLC System"]
        OPCUA["OPC UA Protocol"]
    end
    
    STATUS --> HW
    CONFIG --> HW
    CLI --> HW
    MULTI --> OPCUA
    DRUMS --> HW
    LEVELER --> HW
    BLADE --> HW
    
    OPCUA --> PLC
    
    REST --> STATUS
    REST --> CONFIG
    REST --> CLI
    REST --> MULTI
    
    DI --> REST
    VALID --> REST
    ERROR --> REST
    
    style STATUS fill:#e1f5fe
    style CONFIG fill:#e1f5fe
    style ERRORS fill:#e1f5fe
    style CLI fill:#f3e5f5
    style MULTI fill:#f3e5f5
    style DRUMS fill:#e8f5e8
    style LEVELER fill:#e8f5e8
    style BLADE fill:#e8f5e8
```

**API Organization Principles:**

- **Domain-Driven Structure**: APIs are organized by business domain (system, print, hardware)
- **RESTful Resource Hierarchy**: URLs follow logical resource patterns (`/drums/{id}/blade/screws/{screw_id}`)
- **Consistent HTTP Verb Mapping**: GET (read), POST (create/action), PUT (update), DELETE (remove/cancel)
- **Dependency Injection**: All endpoints use FastAPI's `Depends()` for automatic client management
- **Unified Error Handling**: Consistent exception translation to appropriate HTTP status codes

#### 5.2.2 Configuration Management API

**File:** `backend/app/api/configuration.py`

Manages recoater system configuration settings including build space dimensions, resolution, and operational parameters.

**Key Endpoints:**

- **GET /config** - Retrieve current recoater configuration
  - Returns build space dimensions, ejection matrix size, gaps between drums, and resolution settings
  - Uses dependency injection with `get_recoater_client()` for hardware communication
  - Handles connection errors gracefully with appropriate HTTP status codes

- **PUT /config** - Update recoater configuration settings
  - Accepts `ConfigurationRequest` with optional fields for partial updates
  - Validates input using Pydantic models with field constraints
  - Converts nested models to dictionaries for hardware API compatibility

**Educational Note:** This API demonstrates FastAPI's dependency injection system, where `Depends(get_recoater_client)` automatically provides a configured client instance, handling initialization and error management transparently.

#### 5.2.3 System Status and Health API

**File:** `backend/app/api/status.py`

Provides comprehensive system monitoring capabilities for operational oversight and diagnostics.

**Key Endpoints:**

- **GET /status** - Comprehensive system status
  - Returns recoater hardware status, backend operational state, and connection status
  - Gracefully handles connection failures by returning partial status information
  - Includes error details for troubleshooting without raising exceptions

- **GET /status/health** - Simple health check
  - Lightweight endpoint for monitoring system availability
  - Returns boolean health status for both backend and hardware
  - Safe for frequent polling by monitoring systems

- **POST /status/state** - Server state management
  - Allows restart/shutdown operations on recoater server
  - Validates action parameters with clear error messages
  - Implements proper HTTP status codes for different error conditions

#### 5.2.4 Error Management API

**File:** `backend/app/api/errors.py`

Provides operator-facing error recovery mechanisms following the unified 7-variable OPC UA architecture.

**Key Endpoints:**

- **POST /errors/clear** - Clear all system error flags
  - Unified error clearing for both backend and PLC error states
  - Integrates with `MultiMaterialJobService` for coordinated error recovery
  - Returns success/failure status with descriptive messages

**Design Pattern:** This API demonstrates the Command Pattern, where error clearing is encapsulated as a discrete operation that can be invoked by operators when system recovery is needed.

#### 5.2.5 Print Job Management APIs

**File Path:** `backend/app/api/print/`

The print API module is organized into specialized sub-modules, each handling specific aspects of the printing workflow.

##### ******* CLI File Processing API

**File:** `backend/app/api/print/cli.py`

Handles Command Line Interface (CLI) file upload, parsing, caching, and layer-specific operations.

**Core Concepts:**
- **CLI Files:** ASCII or binary files containing layer-by-layer 3D printing instructions
- **File Caching:** Backend stores parsed CLI files in memory for quick access during multi-layer jobs
- **Layer Preview:** Renders individual layers as PNG images for operator verification

**Key Endpoints:**

- **POST /cli/upload** - Preview-only CLI upload
  - Accepts .cli files and parses them using the `Editor` class
  - Generates unique file_id for caching and reference
  - Uses `asyncio.to_thread()` to offload CPU-intensive parsing
  - Returns file metadata including total layers and file size

- **POST /cli/upload/{drum_id}** - Production CLI upload for specific drum
  - Caches parsed CLI file associated with a specific drum (0-2)
  - Enables multi-material workflow where each drum has its own geometry
  - Validates drum_id parameter with Path constraints

- **GET /cli/{file_id}/layer/{layer_num}/preview** - Layer preview generation
  - Renders specific layer as PNG image for visual verification
  - Uses 1-based layer numbering for user-friendly interface
  - Converts internal 0-based indexing automatically
  - Returns image with appropriate Content-Disposition headers

- **POST /cli/{file_id}/layer/{layer_num}/send/{drum_id}** - Single layer transmission
  - Sends individual layer from cached file to hardware
  - Generates ASCII CLI format for single layer
  - Updates drum cache with single-layer CLI for subsequent operations

- **POST /cli/{file_id}/layers/send/{drum_id}** - Layer range transmission
  - Sends multiple consecutive layers to hardware
  - Accepts `LayerRangeRequest` with start/end layer numbers
  - Optimizes transmission by combining layers into single CLI file

**Error Handling Pattern:** All endpoints use consistent error handling with specific exception types (`CliParsingError`, `RecoaterConnectionError`, `RecoaterAPIError`) mapped to appropriate HTTP status codes.

##### ******* Legacy Drum Management API

**File:** `backend/app/api/print/drum.py`

Provides legacy file operations directly with recoater hardware. Marked as deprecated in favor of the cached multi-material workflow.

**Key Endpoints:**

- **GET /drums/{drum_id}/geometry/preview** - Cache-based preview
  - Retrieves and renders cached CLI file for specific drum
  - Supports layer selection with query parameter
  - Validates layer range against available layers
  - **Note:** Reads from backend cache, not hardware

- **POST /drums/{drum_id}/geometry** - Direct hardware upload
  - Uploads files directly to recoater hardware
  - Bypasses backend caching system
  - Supports PNG or CLI file formats
  - **Warning:** Not compatible with multi-material cached workflow

- **GET /drums/{drum_id}/geometry** - Direct hardware download
  - Downloads geometry file directly from recoater as PNG
  - Bypasses backend cache entirely
  - Useful for verification but not recommended for production workflow

- **DELETE /drums/{drum_id}/geometry** - Hardware file deletion
  - Removes files from recoater hardware
  - Does not affect backend cache
  - **Caution:** May disrupt active print jobs

##### 5.2.5.3 Basic Print Job API (Deprecated)

**File:** `backend/app/api/print/job.py`

Provides basic print job control. Superseded by the multi-material job management system.

**Key Endpoints:**

- **POST /job** - Start basic print job
  - Creates printing job if hardware is ready
  - Returns job_id for tracking
  - **Limitation:** Does not support multi-material coordination

- **DELETE /job** - Cancel print job
  - Terminates current printing operation
  - Immediate cancellation without cleanup coordination

- **GET /job/status** - Basic job status
  - Returns simple printing state information
  - Limited status compared to multi-material job tracking

##### 5.2.5.4 Layer Management API

**File:** `backend/app/api/print/layer.py`

Manages layer-specific parameters and composite preview generation for multi-material printing.

**Key Endpoints:**

- **GET /layer/parameters** - Current layer parameters
  - Retrieves filling_id, speed, powder_saving, and offset settings
  - Returns hardware-specific parameter constraints

- **PUT /layer/parameters** - Update layer parameters
  - Accepts `LayerParametersRequest` with validation
  - Configures drum selection for filling material
  - Sets patterning speed and powder optimization flags

- **GET /layer/preview** - Composite layer preview
  - Generates combined preview showing all active drums
  - Overlays multiple drum geometries in single image
  - Validates layer availability across all drums
  - **Technical:** Uses `render_composite_layer_to_png()` for multi-drum visualization

##### ******* Multi-Material Job Orchestration API

**File:** `backend/app/api/print/multimaterial.py`

Provides comprehensive multi-material print job management with coordinated 3-drum printing operations.

**Core Concepts:**
- **Multi-Material Workflow:** Coordinated printing using multiple material drums
- **Background Tasks:** Long-running print jobs execute asynchronously
- **Drum Cache Management:** Per-drum CLI file storage and coordination
- **Error Recovery:** Operator-accessible error clearing mechanisms

**Key Endpoints:**

- **POST /cli/start-multimaterial-job** - Start coordinated print job
  - Requires cached CLI files for participating drums
  - Initiates background task using `asyncio.create_task()`
  - Returns immediately while job runs asynchronously
  - Stores task reference for proper cancellation management

- **GET /multimaterial-job/status** - Comprehensive job status
  - Returns `MultiMaterialJobStatusResponse` with detailed progress
  - Includes per-drum status, layer progress, and error states
  - Provides percentage completion and time estimates

- **POST /multimaterial-job/cancel** - Graceful job cancellation
  - Stops background task and cleans up resources
  - Coordinates with hardware to safely halt operations
  - Returns success/failure status with descriptive messages

- **POST /multimaterial-job/clear-error** - Error recovery
  - Clears error flags for operator intervention
  - Enables job resumption after error conditions
  - Integrates with OPC UA error flag management

- **GET /cli/drum-cache-status** - Cache status overview
  - Shows which drums have cached files
  - Reports layer counts and readiness status
  - Helps operators verify setup before starting jobs

- **POST /cli/clear-drum-cache/{drum_id}** - Selective cache clearing
  - Removes cached CLI file for specific drum
  - Enables setup changes without affecting other drums
  - Does not affect hardware-stored files

- **GET /multimaterial-job/drum-status/{drum_id}** - Per-drum status
  - Detailed status for individual drum in active job
  - Includes current layer, progress, and error information
  - Enables targeted troubleshooting and monitoring

#### 5.2.6 Recoater Hardware Control APIs

**File Path:** `backend/app/api/recoater_controls/`

Provides direct hardware control interfaces for drums, leveler, and blade components.

##### 5.2.6.1 Drum Control API

**File:** `backend/app/api/recoater_controls/drum.py`

Manages drum motion, ejection pressure, and suction pressure for each of the three drums.

**Motion Control Endpoints:**

- **GET /drums/{drum_id}/motion** - Motion status query
  - Returns current motion state and parameters
  - Includes position, speed, and movement mode information

- **POST /drums/{drum_id}/motion** - Motion command execution
  - Accepts `DrumMotionRequest` with mode-specific parameters
  - Supports absolute, relative, turns, speed, and homing modes
  - Validates speed constraints and distance limits

- **DELETE /drums/{drum_id}/motion** - Motion cancellation
  - Immediately stops current motion command
  - Provides emergency stop functionality

**Pressure Control Endpoints:**

- **GET /drums/{drum_id}/ejection** - Ejection pressure status
  - Returns current and target ejection pressure values
  - Supports pascal and bar pressure units

- **PUT /drums/{drum_id}/ejection** - Ejection pressure control
  - Sets target ejection pressure with unit specification
  - Validates pressure ranges for safety

- **GET /drums/{drum_id}/suction** - Suction pressure monitoring
  - Returns current suction pressure readings
  - Fixed unit (Pascal) for consistency

- **PUT /drums/{drum_id}/suction** - Suction pressure adjustment
  - Controls powder retention during drum operations
  - Prevents material spillage during motion

##### 5.2.6.2 Leveler Control API

**File:** `backend/app/api/recoater_controls/leveler.py`

Controls the powder leveling system and magnetic sensor monitoring.

**Key Endpoints:**

- **GET /leveler/pressure** - Pressure monitoring
  - Returns maximum, target, and current pressure values
  - Essential for powder surface quality control

- **PUT /leveler/pressure** - Pressure adjustment
  - Sets target leveling pressure in Pascal
  - Affects powder spreading uniformity

- **GET /leveler/sensor** - Magnetic sensor state
  - Returns binary sensor state for position detection
  - Used for leveler positioning and safety interlocks

##### 5.2.6.3 Blade Control API

**File:** `backend/app/api/recoater_controls/blade.py`

Manages blade screw positioning for powder scraping operations.

**Collective Screw Control:**

- **GET /drums/{drum_id}/blade/screws** - Screw information
  - Returns data for both screws of the specified drum's blade
  - Includes position, limits, and status information

- **GET /drums/{drum_id}/blade/screws/motion** - Motion status
  - Current motion command state for both screws
  - Shows active movements and target positions

- **POST /drums/{drum_id}/blade/screws/motion** - Synchronized motion
  - Moves both screws together with same parameters
  - Accepts `BladeMotionRequest` with mode and distance
  - Supports absolute, relative, and homing modes

- **DELETE /drums/{drum_id}/blade/screws/motion** - Motion cancellation
  - Stops both screws simultaneously
  - Emergency stop for blade operations

**Individual Screw Control:**

- **GET /drums/{drum_id}/blade/screws/{screw_id}** - Single screw info
  - Detailed information for specific screw (0 or 1)
  - Position, limits, and operational status

- **GET /drums/{drum_id}/blade/screws/{screw_id}/motion** - Individual motion status
  - Motion state for single screw
  - Enables independent screw monitoring

- **POST /drums/{drum_id}/blade/screws/{screw_id}/motion** - Independent motion
  - Moves single screw with specified distance
  - Accepts `BladeIndividualMotionRequest`
  - Enables fine-tuning of blade angle and pressure

- **DELETE /drums/{drum_id}/blade/screws/{screw_id}/motion** - Individual cancellation
  - Stops motion for specific screw only
  - Allows partial operation if one screw fails

#### 5.2.7 API Design Patterns and Best Practices

**Dependency Injection Pattern:**
All endpoints use FastAPI's `Depends()` system for automatic dependency management, ensuring consistent client initialization and error handling.

**Error Handling Strategy:**
- `RecoaterConnectionError` → HTTP 503 (Service Unavailable)
- `RecoaterAPIError` → HTTP 400 (Bad Request) or 502 (Bad Gateway)
- `CliParsingError` → HTTP 400 (Bad Request)
- Generic exceptions → HTTP 500 (Internal Server Error)

**Request/Response Model Validation:**
All endpoints use Pydantic models for automatic validation, serialization, and OpenAPI documentation generation. Key models include:

- `LayerParametersRequest/Response` - Layer configuration with field constraints
- `MultiMaterialJobStatusResponse` - Comprehensive job status with nested drum information
- `DrumMotionRequest` - Motion commands with mode-specific validation
- `ConfigurationRequest/Response` - System configuration with optional field updates

**RESTful Resource Design:**
- Resources follow hierarchical URL structure: `/drums/{id}/blade/screws/{screw_id}`
- HTTP verbs map to operations: GET (read), POST (create/action), PUT (update), DELETE (remove/cancel)
- Consistent response formats with success/error indicators
- Path parameters validated with FastAPI `Path()` constraints

**Asynchronous Operation Handling:**
Long-running operations (multi-material jobs, CLI parsing) use `asyncio` patterns:
- `asyncio.to_thread()` for CPU-bound operations
- `asyncio.create_task()` for background job execution
- Immediate API response with background processing
- Task reference storage for proper cancellation

**OpenAPI Documentation Integration:**
All endpoints include comprehensive metadata:
- Description fields explaining purpose and behavior
- Request/response schema examples
- Error response documentation
- Tag-based organization for logical grouping
- Parameter validation with constraints and descriptions

### 5.3 Service layer (business logic)

The service layer contains the core business logic and use case orchestration for the recoater system. This layer implements the application's business rules, coordinates between different components, and manages complex workflows like multi-material printing jobs.

**Core Responsibilities:**
- Service classes and use-case orchestration
- Business rules, invariants, and domain services
- Async patterns, cancellation, and coordination
- Cross-cutting: caching, retries, backoff, timeouts
- Event handling: domain events and integration events
- Mixin-based architecture with strategic multiple inheritance

**Service Architecture Overview:**

```mermaid
graph TB
    subgraph "Services Layer"
        subgraph "Communication"
            WSM["websocket_manager.py"]
        end
        
        subgraph "Job Management"
            MJS["multimaterial_job_service.py"]
            subgraph "Mixins"
                CLI["cli_caching_mixin.py"]
                COORD["coordination_mixin.py"]
                LAYER["layer_operations_mixin.py"]
            end
        end
        
        subgraph "Monitoring"
            DG["data_gatherer.py"]
            SP["status_poller.py"]
        end
        
        subgraph "OPC UA"
            OPC["opcua_service.py"]
            subgraph "OPC Mixins"
                OPCCOORD["coordination_mixin.py"]
                OPCMON["monitoring_mixin.py"]
                OPCSERV["server_mixin.py"]
            end
        end
    end
    
    MJS --> CLI
    MJS --> COORD
    MJS --> LAYER
    OPC --> OPCCOORD
    OPC --> OPCMON
    OPC --> OPCSERV
    WSM --> SP
    SP --> DG
```

#### 5.3.1 Communication Services

**WebSocket Connection Manager**
**File:** `backend/app/services/communication/websocket_manager.py`

Manages real-time WebSocket connections for live system updates and operator notifications.

**Core Concepts:**
- **Connection Lifecycle:** Accept, maintain, and cleanup WebSocket connections
- **Subscription Management:** Track what data types each client wants to receive
- **Message Filtering:** Send only relevant data to each connection based on subscriptions
- **Broadcast Coordination:** Efficiently distribute updates to multiple clients

**How the WebSocket Manager works:**

1. **Connection Establishment:** When a client connects to the WebSocket endpoint, the `WebSocketManager` accepts the connection and adds it to an internal connection registry. Each connection is assigned a unique identifier for tracking.

2. **Subscription Registration:** Clients can send subscription messages specifying which data types they want to receive (e.g., "status", "drum_motion", "print_job"). The manager maintains a mapping of connections to their subscriptions.

3. **Data Broadcasting:** When system events occur (hardware status changes, job progress updates), services call the manager's broadcast methods. The manager filters messages based on each connection's subscriptions and sends only relevant data.

4. **Connection Cleanup:** The manager automatically detects disconnected clients during broadcast attempts and removes them from the registry, preventing memory leaks.

**Sequence Diagram - WebSocket Subscription and Broadcasting:**

```mermaid
sequenceDiagram
    participant Client as WebSocket Client
    participant WSM as WebSocketManager
    participant Poller as StatusPoller
    participant Hardware as Hardware System
    
    Client->>WSM: connect()
    WSM->>Client: connection_id
    
    Client->>WSM: subscribe("status")
    WSM->>Client: ack
    
    Poller->>Hardware: get_status()
    Hardware->>Poller: status_data
    Poller->>WSM: status_data
    
    WSM->>Client: broadcast_status()
    
    Note over Client, Hardware: Only subscribed clients receive updates
```

**Why it's implemented this way:**

* **Subscription-Based Filtering:** Instead of broadcasting all data to all clients, the subscription system reduces network traffic and client processing overhead. Industrial operators may only need specific data types for their current task.

* **Automatic Cleanup:** Using try-catch blocks during broadcasts to detect disconnected clients prevents the common WebSocket problem of accumulating dead connections, which can cause memory leaks in long-running industrial applications.

* **Centralized Broadcasting:** Having a single manager coordinate all WebSocket communication prevents race conditions and ensures consistent message ordering across all connected clients.

* **Connection Registry:** Maintaining an internal registry with unique identifiers allows for efficient connection management and enables features like targeted messaging to specific clients.

**Alternatives considered:**

* **Direct Service Broadcasting:** Each service could manage its own WebSocket connections and broadcast directly. This was rejected because it would lead to code duplication, inconsistent error handling, and difficulty in implementing cross-service coordination features.

* **Database-Based Pub/Sub:** Using a message broker or database for pub/sub messaging. This was considered too heavy for the real-time requirements of industrial control, where low latency is critical and the additional network hop would introduce unnecessary delay.

* **Polling-Only Approach:** Clients could poll REST endpoints instead of using WebSockets. This was rejected because it would increase server load, reduce responsiveness for critical alerts, and make it harder to implement real-time features like live job progress monitoring.

#### 5.3.2 Job Management Services

**File Path:** `backend/app/services/job_management/`

Provides comprehensive multi-material job orchestration using a sophisticated mixin-based architecture following strategic multiple inheritance principles.

**How the Job Management Service works:**

1. **Service Composition:** The `MultiMaterialJobService` class combines three mixins using Python's multiple inheritance. The Method Resolution Order (MRO) ensures that methods are resolved in the correct sequence, with more specific implementations taking precedence.

2. **Job Initialization Phase:**
   - Validate that all required drums have cached CLI files
   - Setup OPC UA coordination variables for industrial protocol compliance
   - Initialize job tracking state and background task management
   - Perform hardware readiness checks before starting the print sequence

3. **Layer-by-Layer Execution Loop:**
   - For each layer in the job sequence:
     - Reset coordination flags to ensure clean state
     - Upload layer geometry to appropriate drums via hardware client
     - Signal "ready to print" via OPC UA to notify PLC
     - Wait for "layer complete" signal from hardware
     - Update progress tracking and notify WebSocket clients
     - Handle any error conditions that arise during processing

4. **Error Recovery and Operator Intervention:**
   - Pause job execution when errors are detected
   - Wait for operator to resolve issues and clear error flags
   - Resume from the failed layer rather than restarting entire job
   - Maintain audit trail of errors and recovery actions

**Sequence Diagram - Multi-Material Job Execution:**

```mermaid
sequenceDiagram
    participant API as API Endpoint
    participant Job as JobService
    participant OPC as OPCUAService
    participant Client as HardwareClient
    participant PLC as PLC/Hardware
    
    API->>Job: start_job()
    Job->>Job: validate_cache()
    Job->>OPC: setup_opcua_job()
    OPC->>PLC: write_vars(job_active)
    Job->>API: job_id
    
    loop Layer Processing
        Job->>Client: upload_layer()
        Client->>PLC: upload_file()
        Job->>OPC: signal_ready()
        OPC->>PLC: write_ready_flag()
        Job->>OPC: wait_complete()
        OPC->>PLC: poll_complete_flag()
        PLC->>OPC: layer_complete
        Job->>OPC: signal_complete()
        OPC->>PLC: write_complete_flag()
    end
    
    Job->>OPC: cleanup_job()
    OPC->>PLC: reset_vars()
```

**Mixin Architecture Design:**

```mermaid
classDiagram
    class MultiMaterialJobService {
        +start_job()
        +cancel_job()
        +get_job_status()
    }
    
    class LayerProcessingMixin {
        +cancel_job()
        +get_job_status()
        +_upload_layer_to_drum()
    }
    
    class OPCUACoordinationMixin {
        +setup_opcua_job()
        +wait_for_layer_completion()
        +clear_error_flags()
    }
    
    class CliCachingMixin {
        +cache_cli_file_for_drum()
        +get_cached_file_for_drum()
        +has_cached_files()
    }
    
    MultiMaterialJobService --|> LayerProcessingMixin
    MultiMaterialJobService --|> OPCUACoordinationMixin
    MultiMaterialJobService --|> CliCachingMixin
    
    note for MultiMaterialJobService "Uses strategic multiple inheritance\nfor mixin composition"
```

**7-Variable OPC UA Protocol Implementation:**
The coordination mixin implements industrial automation standards:
```
Variable Name              | Direction   | Purpose
=========================|=============|==============================
job_active                | Backend→PLC | Job lifecycle state
total_layers              | Backend→PLC | Job scope information
current_layer             | Backend→PLC | Progress tracking
recoater_ready_to_print   | Backend→PLC | Print readiness signal
recoater_layer_complete   | Backend→PLC | Layer completion signal
backend_error             | Backend→PLC | Backend error conditions
plc_error                 | PLC→Backend | Hardware error conditions
```

**Why it's implemented this way:**

* **Mixin-Based Architecture:** Using mixins allows for clean separation of concerns while avoiding deep inheritance hierarchies. Each mixin encapsulates a specific aspect of job management (file handling, OPC UA coordination, layer processing), making the code more modular and testable.

* **7-Variable OPC UA Protocol:** This industrial standard ensures reliable coordination between the backend and PLC. The bidirectional flag system prevents race conditions and ensures that each layer is fully processed before moving to the next.

* **Background Task Execution:** Long-running print jobs execute as asyncio tasks to prevent blocking the API. This allows operators to monitor progress and cancel jobs while maintaining system responsiveness.

* **Cached File Strategy:** Pre-uploading and caching CLI files for each drum enables efficient multi-material printing without repeated file parsing and network transfers during job execution.

**Alternatives considered:**

* **Monolithic Service Class:** All job management functionality could be implemented in a single large class. This was rejected because it would violate the Single Responsibility Principle and make testing individual aspects (caching, coordination, processing) much more difficult.

* **Microservice Architecture:** Job management could be split into separate services for file caching, OPC UA coordination, and layer processing. This was considered too complex for the current scope and would introduce unnecessary network overhead for tightly coupled operations.

* **Database-Based Job Queue:** Instead of in-memory job tracking, jobs could be persisted to a database with a separate worker process. This was deemed unnecessary for single-operator scenarios and would add complexity without significant benefit.

* **Direct Hardware Communication:** The service could communicate directly with hardware instead of using OPC UA coordination. This was rejected because it would bypass industrial automation standards and make integration with existing factory systems much more difficult.

#### 5.3.3 Monitoring Services

**File Path:** `backend/app/services/monitoring/`

**Data Gatherer Service:** Concurrent hardware data collection with error resilience
**Status Polling Service:** Background polling lifecycle management

**How the Monitoring Services work:**

1. **Subscription-Based Data Collection:** The `DataGatherer` service implements a subscription model where it only collects data types that are actively requested by WebSocket clients. This prevents unnecessary hardware polling when no clients are connected.

2. **Concurrent Data Collection:** Using `asyncio.gather()`, the service collects data from multiple hardware subsystems (drums, leveler, blade, print status) in parallel rather than sequentially, significantly reducing total collection time.

3. **Error Resilience:** Individual subsystem failures (e.g., drum communication timeout) don't prevent collection of data from other subsystems. The service implements graceful degradation by returning partial data sets.

4. **Intelligent Polling Optimization:** The `StatusPoller` dynamically adjusts polling intervals based on system activity and connected clients. During active print jobs, polling increases; when no clients are connected, polling stops entirely.

**Sequence Diagram - Monitoring Service Data Collection:**

```mermaid
sequenceDiagram
    participant WS as WebSocketClient
    participant Poller as StatusPoller
    participant Gatherer as DataGatherer
    participant Client as HardwareClient
    participant Hardware as Hardware System
    
    WS->>Poller: connect()
    Poller->>Poller: start_polling()
    
    loop Data Collection Cycle
        Poller->>Gatherer: gather_data()
        
        par Concurrent Collection
            Gatherer->>Client: get_drums()
            Client->>Hardware: drums_api
        and
            Gatherer->>Client: get_leveler()
            Client->>Hardware: leveler_api
        and
            Gatherer->>Client: get_status()
            Client->>Hardware: status_api
        end
        
        Hardware->>Client: drum_data
        Hardware->>Client: leveler_data
        Hardware->>Client: status_data
        Client->>Gatherer: combined_data
        Gatherer->>Poller: aggregated_data
        
        Poller->>WS: broadcast()
    end
```

**Why it's implemented this way:**

* **Subscription-Based Collection:** Industrial systems often have many subsystems, but operators typically monitor only specific areas. Collecting only requested data reduces network traffic, hardware load, and improves system responsiveness.

* **Concurrent Collection:** Hardware subsystems often have independent response times. Parallel collection using `asyncio.gather()` ensures that slow subsystems don't delay data from fast ones, improving overall system responsiveness.

* **Graceful Degradation:** In industrial environments, partial system failures are common (sensor disconnections, network issues). Continuing to provide available data helps operators understand what's working and what needs attention.

* **Dynamic Polling:** Constant polling wastes resources when no one is monitoring. Smart polling based on client connections and system activity optimizes resource usage while maintaining responsiveness when needed.

**Alternatives considered:**

* **Push-Based Hardware Events:** Hardware could push events directly to the backend instead of polling. This was rejected because most industrial hardware (including the recoater) uses traditional request-response protocols rather than event-driven architectures.

* **Fixed Polling Intervals:** Simple timer-based polling with fixed intervals. This was rejected because it wastes resources when no clients are connected and may be too slow during critical operations.

* **Sequential Data Collection:** Collecting data from each subsystem one at a time. This was rejected because it would increase total collection time linearly with the number of subsystems, making real-time monitoring less responsive.

**Educational Note:** This service demonstrates the **Observer Pattern** combined with **Concurrent Programming**. The polling service observes WebSocket connection changes to optimize its behavior, while the data gatherer uses asyncio to maximize throughput.

#### 5.3.4 OPC UA Services

**File Path:** `backend/app/services/opcua/`

Industrial automation integration using the same mixin composition pattern as job management.

**How the OPC UA Service works:**

1. **Service Composition Architecture:** The `OPCUAService` class combines three mixins using strategic multiple inheritance. The Method Resolution Order (MRO) is critical here - `CoordinationMixin` comes first to override base implementations in `ServerMixin` with business-logic-aware versions.

2. **OPC UA Server Infrastructure:** The `ServerMixin` provides the foundational OPC UA server using the `asyncua` library. It manages variable nodes, handles client connections, and provides the basic read/write operations that form the foundation of industrial communication.

3. **Business Logic Coordination:** The `CoordinationMixin` extends the base server with domain-specific methods for print job coordination. It implements the 7-variable protocol used for backend-PLC synchronization and provides higher-level abstractions for job state management.

4. **Health Monitoring Integration:** The `MonitoringMixin` adds cross-cutting health monitoring capabilities, tracking OPC UA server health, connection stability, and variable update frequencies for system reliability.

**Sequence Diagram - OPC UA Variable Coordination:**

```mermaid
sequenceDiagram
    participant Job as JobService
    participant OPC as OPCUAService
    participant Server as AsyncUAServer
    participant Client as PLC/Client
    
    Job->>OPC: setup_job()
    OPC->>Server: create_variables()
    Server->>Client: add_nodes()
    Client->>Server: nodes_created
    OPC->>Job: setup_complete
    
    Job->>OPC: signal_ready()
    OPC->>Server: write_variable("ready", True)
    Server->>Client: set_value()
    
    Job->>OPC: wait_complete()
    
    loop Polling
        OPC->>Server: poll_variable("complete")
        Server->>Client: get_value()
        Client->>Server: False
    end
    
    Client->>Server: True
    Server->>OPC: layer_complete
    OPC->>Job: layer_done
```

**Service Composition (Method Resolution Order Critical):**
```python
class OPCUAService(
    CoordinationMixin,    # First: Business logic, overrides write_variable
    ServerMixin,          # Second: Infrastructure, base write_variable
    MonitoringMixin       # Last: Cross-cutting health monitoring
):
```

**Key Components:**
- **Server Mixin:** Real OPC UA server using asyncua library with variable management
- **Coordination Mixin:** Business logic helpers for job state coordination
- **Monitoring Mixin:** Health monitoring and system reliability features

**Why it's implemented this way:**

* **Mixin Composition Pattern:** Using the same architectural pattern as the job management service creates consistency across the codebase and makes the system easier to understand and maintain. Developers working on one service can quickly understand the other.

* **Method Resolution Order (MRO) Strategy:** By placing `CoordinationMixin` first in the inheritance order, its `write_variable()` method takes precedence over the base implementation in `ServerMixin`. This allows the coordination mixin to add business logic (logging, validation, error handling) while still calling the underlying server functionality.

* **Industrial Protocol Compliance:** OPC UA is the standard for industrial automation communication. Implementing this service ensures compatibility with existing factory systems and follows established industrial practices for machine-to-machine communication.

* **Variable-Based State Management:** Using OPC UA variables for state coordination provides a standardized, reliable way to synchronize between the backend and PLC. The variable approach is more robust than custom protocols and is well-understood by industrial engineers.

**Alternatives considered:**

* **Direct TCP/IP Communication:** Custom protocol over TCP/IP sockets. This was rejected because it would require developing and maintaining a custom protocol when the industry-standard OPC UA already provides reliable industrial communication patterns.

* **REST API for PLC Communication:** Using HTTP REST calls for PLC coordination. This was rejected because REST is not suitable for real-time industrial coordination where deterministic timing and low latency are critical.

* **Message Queue Integration:** Using message brokers like RabbitMQ or Redis for coordination. This was considered too complex for the real-time requirements and would introduce additional infrastructure dependencies.

* **Single Monolithic OPC UA Class:** All OPC UA functionality in one large class. This was rejected in favor of the mixin approach to maintain separation of concerns and enable easier testing of individual aspects (server infrastructure vs. business coordination vs. health monitoring).

**Educational Note:** This service demonstrates the **Template Method Pattern** where the base `ServerMixin` provides the infrastructure template, and the `CoordinationMixin` customizes specific operations with business logic while maintaining the overall OPC UA server structure.

### 5.4 Infrastructure layer

The infrastructure layer handles external system integration, hardware communication, and technical concerns that support the business logic. This layer provides adapters, clients, and utilities that isolate the core application from external dependencies.

**Core Responsibilities:**
- External integrations: OPC UA client, WebSocket manager, file system
- CLI processing and batch jobs for multi-layer print workflows
- Mock implementations and fakes for offline development
- Data persistence: repositories and transaction boundaries
- Third-party service adapters with resilience patterns
- Hardware communication clients with error handling

**Infrastructure Architecture Overview:**

```mermaid
graph TB
    subgraph "Infrastructure Layer"
        subgraph "CLI Editor System"
            EDITOR["editor.py"]
            ASCIIPARSE["ascii_cli_parser.py"]
            BINARYPARSE["binary_cli_parser.py"]
            ASCIIGEN["ascii_cli_generator.py"]
            BINARYGEN["binary_cli_generator.py"]
            RENDER["cli_renderer.py"]
            MODELS["cli_models.py"]
        end
        
        subgraph "Real Hardware Client"
            CLIENT["client.py"]
            ASYNC["async_client.py"]
            PRINT["print_controls.py"]
            BLADE["blade_controls.py"]
            LEVELER["leveler_controls.py"]
            FILES["file_management.py"]
        end
        
        subgraph "Mock Hardware Client"
            MOCKCLIENT["mock_client.py"]
            MOCKASYNC["mock_async_client.py"]
            MOCKPRINT["mock_print_controls.py"]
            MOCKBLADE["mock_blade_controls.py"]
            MOCKLEVELER["mock_leveler_controls.py"]
            MOCKFILES["mock_file_management.py"]
        end
    end
    
    EDITOR --> ASCIIPARSE
    EDITOR --> BINARYPARSE
    EDITOR --> ASCIIGEN
    EDITOR --> BINARYGEN
    EDITOR --> RENDER
    EDITOR --> MODELS
    
    ASYNC --> CLIENT
    CLIENT --> PRINT
    CLIENT --> BLADE
    CLIENT --> LEVELER
    CLIENT --> FILES
    
    MOCKASYNC --> MOCKCLIENT
    MOCKCLIENT --> MOCKPRINT
    MOCKCLIENT --> MOCKBLADE
    MOCKCLIENT --> MOCKLEVELER
    MOCKCLIENT --> MOCKFILES
```

#### 5.4.1 CLI Editor System

**File Path:** `backend/infrastructure/cli_editor/`

Provides comprehensive CLI (Command Line Interface) file processing for 3D printing layer data.

**Core Concepts:**
- **CLI Files:** Layer-by-layer printing instructions containing geometry and parameters
- **Format Support:** Both ASCII and binary CLI formats for different hardware requirements
- **Mixin Architecture:** Strategic inheritance for parsing, generation, and rendering capabilities
- **Layer Processing:** Individual layer extraction, modification, and reconstruction

**How the CLI Editor System works:**

1. **File Format Detection:** When a CLI file is uploaded, the `Editor` class automatically detects whether it's ASCII or binary format by examining file headers and structure patterns. This allows the system to handle both formats transparently.

2. **Parsing Pipeline:** The appropriate parser (ASCII or binary) extracts the file structure into a standardized `ParsedCliFile` object containing header metadata and a list of `CliLayer` objects. Each layer contains geometry data and printing parameters.

3. **Layer Manipulation:** Individual layers can be extracted, modified, or combined. This is essential for multi-material printing where different drums need different subsets of layers from the original file.

4. **Generation and Export:** The system can reconstruct CLI files from the parsed data structure, enabling operations like single-layer extraction or multi-layer combination for specific printing scenarios.

5. **Preview Rendering:** The renderer converts layer geometry data into PNG images for operator verification. It supports both single-layer previews and composite multi-drum overlays.

**Sequence Diagram - CLI File Processing:**

```mermaid
sequenceDiagram
    participant API as API Endpoint
    participant Editor as CLI Editor
    participant Parser as Format Parser
    participant Renderer as CLI Renderer
    participant Cache as File Cache
    
    API->>Editor: upload(file_data)
    Editor->>Editor: detect_format()
    
    alt ASCII Format
        Editor->>Parser: parse_ascii()
        Parser->>Parser: extract_header()
        Parser->>Parser: parse_layers()
        Parser->>Editor: ParsedCliFile
    else Binary Format
        Editor->>Parser: parse_binary()
        Parser->>Parser: decode_binary()
        Parser->>Parser: extract_layers()
        Parser->>Editor: ParsedCliFile
    end
    
    Editor->>Cache: cache_file(parsed_file)
    Cache->>Cache: store(file_id)
    Editor->>API: file_id
    
    API->>Editor: preview(file_id, layer_num)
    Editor->>Cache: get_cached(file_id)
    Cache->>Editor: ParsedCliFile
    Editor->>Renderer: render_layer()
    Renderer->>Renderer: create_image()
    Renderer->>Renderer: apply_colors()
    Renderer->>Editor: png_data
    Editor->>API: png_response
```

##### ******* CLI Data Models and Exceptions

**File:** `backend/infrastructure/cli_editor/cli_models.py`

Defines core data structures for CLI file representation:
- **ParsedCliFile:** Complete CLI file with header and layers
- **CliLayer:** Individual layer with geometry data and parameters
- **LayerGeometry:** Spatial data and printing parameters
- **FileHeader:** Metadata and global settings

**File:** `backend/infrastructure/cli_editor/cli_exceptions.py`

CLI-specific exception hierarchy:
- **CliParsingError:** File format or parsing issues
- **CliGenerationError:** File creation or export problems
- **CliRenderingError:** Preview generation failures

##### ******* Parser Implementations

**ASCII CLI Parser**
**File:** `backend/infrastructure/cli_editor/ascii_cli_parser.py`

Handles human-readable ASCII CLI format:
- **Line-by-line parsing** with command interpretation
- **Geometry extraction** from coordinate data
- **Header processing** for global parameters
- **Error recovery** for malformed files

**Binary CLI Parser**
**File:** `backend/infrastructure/cli_editor/binary_cli_parser.py`

Processes compact binary CLI format:
- **Binary data structure** parsing with proper byte ordering
- **Efficient memory usage** for large files
- **Data validation** and integrity checking
- **Format version compatibility** handling

##### ******* Generator Implementations

**ASCII CLI Generator**
**File:** `backend/infrastructure/cli_editor/ascii_cli_generator.py`

Creates ASCII CLI files from layer data:
- **Command sequence generation** with proper formatting
- **Geometry serialization** to coordinate commands
- **Header construction** with metadata
- **Single-layer CLI creation** for individual layer processing

**Binary CLI Generator**
**File:** `backend/infrastructure/cli_editor/binary_cli_generator.py`

Produces binary CLI files:
- **Efficient binary encoding** with optimized structure
- **Compression support** for large layer datasets
- **Cross-platform compatibility** with endianness handling
- **Version management** for format evolution

##### 5.4.1.4 CLI Renderer and Visualization

**File:** `backend/infrastructure/cli_editor/cli_renderer.py`

Generates PNG previews from CLI layer data:
- **Layer visualization** with geometry rendering
- **Multi-drum composite previews** for multi-material jobs
- **Customizable rendering parameters** (colors, resolution, scaling)
- **Efficient image generation** using PIL/Pillow

**Key Methods:**
- `render_layer_to_png(layer, drum_id=None)` - Single layer preview
- `render_composite_layer_to_png(drum_layers)` - Multi-drum overlay preview
- `_create_layer_image(layer_data, params)` - Core rendering logic
- `_apply_drum_colors(image, drum_id)` - Color coding for drum identification

##### ******* Unified CLI Editor Interface

**File:** `backend/infrastructure/cli_editor/editor.py`

Main facade combining all CLI processing capabilities using mixin composition:

```python
class Editor(
    AsciiCliParser,     # ASCII parsing capabilities
    BinaryCliParser,    # Binary parsing capabilities
    CliRenderer,        # Preview generation
    CliGenerator        # File generation
):
```

**Unified Interface Methods:**
- `parse(file_data: bytes) -> ParsedCliFile` - Auto-detect and parse CLI files
- `generate_single_layer_ascii_cli(layer, header_lines)` - Create single-layer files
- `generate_ascii_cli_from_layer_range(layers, header_lines)` - Create multi-layer files
- `render_layer_to_png(layer, drum_id=None)` - Generate layer previews
- `render_composite_layer_to_png(drum_layers)` - Multi-material previews

**Why it's implemented this way:**

* **Mixin Composition Architecture:** The Editor class demonstrates the **Facade Pattern** combined with **Mixin Composition**. This provides a simplified interface to complex CLI processing subsystems while maintaining modular architecture. Each mixin handles one specific responsibility (parsing, generation, rendering), making the system easier to test and extend.

* **Format Abstraction:** Supporting both ASCII and binary formats through a unified interface allows the system to work with different hardware requirements without exposing format complexity to higher-level services. ASCII is human-readable for debugging, while binary is more efficient for large files.

* **Layer-Level Granularity:** Operating at the individual layer level enables advanced multi-material workflows where different drums may need different portions of the same print job. This granularity is essential for complex multi-material printing scenarios.

* **Preview Generation Integration:** Including rendering capabilities in the same system that handles parsing ensures consistency between what's parsed and what's displayed, reducing the risk of preview-reality mismatches.

**Alternatives considered:**

* **Separate Services for Each Format:** ASCII and binary processing could be completely separate services. This was rejected because it would lead to code duplication and make it harder to implement format-agnostic features like automatic detection.

* **Database Storage for Parsed Files:** Instead of in-memory caching, parsed CLI files could be stored in a database. This was considered unnecessary for the current scope and would add complexity without significant benefit for single-operator scenarios.

* **External CLI Processing Library:** Using an existing library for CLI file processing. This was rejected because CLI formats are often proprietary or highly specific to particular hardware, and building custom processing provides better control and optimization opportunities.

* **Simple File Pass-Through:** Just storing and forwarding CLI files without parsing. This was rejected because it would prevent advanced features like layer previews, single-layer extraction, and multi-material coordination.

**Educational Note:** The CLI Editor system demonstrates the **Strategy Pattern** where different parsing and generation strategies (ASCII vs Binary) can be selected based on file format, while the **Facade Pattern** provides a unified interface hiding the complexity of multiple processing strategies.

#### 5.4.2 Recoater Hardware Client

**File Path:** `backend/infrastructure/recoater_client/`

Provides hardware communication interfaces for the Aerosint recoater system.

**How the Recoater Hardware Client works:**

1. **Client Architecture:** The hardware client follows a modular design where the main client provides core communication infrastructure, while specialized control modules handle domain-specific operations (print, blade, leveler, file management).

2. **Synchronous to Asynchronous Adaptation:** The base hardware API is synchronous (blocking), but the async client wrapper uses `asyncio.to_thread()` to run synchronous operations in a thread pool, making them compatible with FastAPI's async framework.

3. **Error Handling Strategy:** Each client module implements consistent error handling by catching low-level network exceptions and translating them into domain-specific exceptions (`RecoaterConnectionError`, `RecoaterAPIError`) that higher layers can handle appropriately.

4. **Request/Response Validation:** All client methods validate both outgoing requests (parameter constraints, format validation) and incoming responses (status codes, data structure validation) to ensure data integrity.

**Sequence Diagram - Hardware Client Communication:**

```mermaid
sequenceDiagram
    participant API as API Endpoint
    participant Async as AsyncClient
    participant Sync as SyncClient
    participant HTTP as HTTP/REST
    participant Hardware as Hardware System
    
    API->>Async: drum_motion()
    Async->>Sync: to_thread()
    Sync->>Sync: validate_params()
    Sync->>HTTP: POST /drums/0/motion
    HTTP->>Hardware: HTTP Request
    
    alt Success Case
        Hardware->>HTTP: 200 OK
        HTTP->>Sync: Success Response
        Sync->>Sync: validate_response()
        Sync->>Async: result
        Async->>API: success
    else Error Case
        Hardware->>HTTP: 500 Error
        HTTP->>Sync: Error Response
        Sync->>Sync: raise RecoaterAPIError()
        Sync->>Async: exception
        Async->>API: HTTP 502
    end
```

##### ******* Main Hardware Client

**File:** `backend/infrastructure/recoater_client/client.py`

Core client for synchronous hardware communication:
- **REST API communication** with the recoater hardware
- **Error handling and retries** for network resilience
- **Response validation** and data transformation
- **Connection management** with timeout handling

**Key Methods:**
- `get_state()` - Retrieve overall system status
- `get_drums()` - Get information about all drums
- `get_drum(drum_id)` - Get specific drum information
- `health_check()` - Verify system connectivity

##### 5.4.2.2 Async Client Wrapper

**File:** `backend/infrastructure/recoater_client/async_client.py`

Asynchronous wrapper for non-blocking hardware operations:
- **Async/await integration** with FastAPI
- **Concurrent operations** support
- **Background task compatibility** for long-running jobs
- **Thread pool integration** for sync API calls

##### 5.4.2.3 Specialized Control Modules

**Print Controls**
**File:** `backend/infrastructure/recoater_client/print_controls.py`

Manages print job lifecycle and layer operations:
- **Job management:** start, cancel, status monitoring
- **Layer parameters:** filling_id, speed, powder_saving settings
- **Print coordination** with hardware state machine
- **Error detection and reporting** for print failures

**Blade Controls**
**File:** `backend/infrastructure/recoater_client/blade_controls.py`

Handles blade screw positioning and motion:
- **Screw information:** position, limits, status for each blade screw
- **Motion commands:** absolute, relative, and homing movements
- **Synchronized control:** move both screws together
- **Individual control:** independent screw positioning

**Leveler Controls**
**File:** `backend/infrastructure/recoater_client/leveler_controls.py`

Manages powder leveling system:
- **Pressure control:** target and current pressure monitoring
- **Sensor management:** magnetic sensor state and position detection
- **Safety interlocks** and pressure limit enforcement

##### 5.4.2.4 File Management

**File:** `backend/infrastructure/recoater_client/file_management.py`

Handles file operations with recoater hardware:
- **File upload:** CLI and geometry files to specific drums
- **File download:** Retrieve files from hardware storage
- **File deletion:** Clean up hardware storage
- **Content validation** and format verification

**Key Methods:**
- `upload_drum_geometry(drum_id, file_data, content_type)` - Upload files to drums
- `download_drum_geometry(drum_id)` - Retrieve files from drums
- `delete_drum_geometry(drum_id)` - Remove files from hardware

##### 5.4.2.5 Hardware Communication Exceptions

**File:** `backend/infrastructure/recoater_client/exceptions.py`

Defines hardware-specific exception hierarchy:
- **RecoaterConnectionError:** Network and connectivity issues
- **RecoaterAPIError:** Hardware API errors and invalid responses
- **RecoaterTimeoutError:** Operation timeout handling
- **RecoaterConfigurationError:** Hardware configuration problems

**Why it's implemented this way:**

* **Modular Client Design:** Separating the client into specialized modules (print, blade, leveler, file) follows the **Single Responsibility Principle**. Each module can be developed, tested, and maintained independently while sharing the common communication infrastructure.

* **Async Wrapper Pattern:** The async wrapper using `asyncio.to_thread()` allows the synchronous hardware client to work seamlessly with FastAPI's async framework without requiring a complete rewrite of the synchronous code. This **Adapter Pattern** enables gradual migration and maintains compatibility.

* **Consistent Error Translation:** Converting low-level network exceptions into domain-specific exceptions provides a clean abstraction boundary. Higher layers don't need to know about HTTP status codes or network details - they can focus on hardware-specific error handling.

* **Request/Response Validation:** Validating both requests and responses at the client level catches errors early and provides better error messages. This is especially important in industrial environments where clear diagnostics are essential for operators.

**Alternatives considered:**

* **Single Monolithic Client:** All hardware communication in one large client class. This was rejected because it would violate the Single Responsibility Principle and make testing individual hardware subsystems difficult.

* **Native Async Client:** Building an async client from scratch using aiohttp. This was rejected because the hardware API is inherently synchronous and building a native async client would require significant additional complexity without clear benefits.

* **Direct Async/Await Throughout:** Making all client methods truly async by rewriting the underlying communication. This was rejected to maintain simplicity and compatibility with the existing synchronous hardware API.

* **No Error Translation:** Letting network exceptions bubble up to higher layers. This was rejected because it would leak implementation details and make error handling inconsistent across different client modules.

#### 5.4.3 Mock Recoater Client

**File Path:** `backend/infrastructure/mock_recoater_client/`

Provides simulated hardware behavior for development and testing.

**How the Mock Recoater Client works:**

1. **Interface Compatibility Strategy:** The mock client implements identical interfaces to the real hardware client, following the **Adapter Pattern** and **Dependency Injection** principles. This allows seamless switching between mock and production modes through configuration changes.

2. **Realistic Behavior Simulation:** Rather than returning static responses, the mock client maintains internal state that changes over time. For example, when a motion command is sent, the mock tracks position changes and simulates movement completion after realistic time delays.

3. **Configurable Error Injection:** The mock client can be configured to simulate various error conditions (network timeouts, hardware failures, invalid responses) to test error handling and recovery scenarios without requiring actual hardware failures.

4. **State Persistence:** Mock state is maintained in memory during the application lifecycle, allowing for realistic multi-step operations like print jobs that span multiple API calls.

**Sequence Diagram - Mock vs Real Client Behavior:**

```mermaid
sequenceDiagram
    participant Service as API Service
    participant Factory as ClientFactory
    participant Mock as MockClient
    participant Real as RealClient
    participant Hardware as Hardware System
    
    Service->>Factory: get_client()
    Factory->>Factory: check_config()
    
    alt USE_MOCK = True
        Factory->>Mock: create_mock()
        Mock->>Factory: mock_instance
        Factory->>Service: mock_client
        
        Service->>Mock: drum_motion()
        Mock->>Mock: simulate_delay()
        Mock->>Mock: update_internal_state()
        Mock->>Service: simulated_response
    else USE_MOCK = False
        Factory->>Real: create_real()
        Real->>Factory: real_instance
        Factory->>Service: real_client
        
        Service->>Real: drum_motion()
        Real->>Hardware: HTTP_POST
        Hardware->>Real: actual_response
        Real->>Service: hardware_response
    end
```

##### ******* Mock Implementation Strategy

**Design Principles:**
- **Interface Compatibility:** Identical method signatures and return types
- **Realistic Behavior:** Simulate actual hardware timing and responses
- **State Management:** Maintain internal state to simulate hardware behavior
- **Error Simulation:** Configurable error conditions for testing

##### ******* Mock Client Components

**Main Mock Client**
**File:** `backend/infrastructure/mock_recoater_client/mock_client.py`

Core mock implementation with state simulation:
- **State management:** Simulate hardware state transitions
- **Response generation:** Create realistic API responses
- **Timing simulation:** Add delays to mimic hardware operations
- **Configuration options:** Adjustable behavior for different testing scenarios

**Async Mock Client**
**File:** `backend/infrastructure/mock_recoater_client/mock_async_client.py`

Asynchronous mock wrapper matching the real async client:
- **Async compatibility** with the service layer
- **Concurrent operation simulation**
- **Background task testing** support

##### 5.4.3.3 Mock Control Modules

**Mock Print Controls**
**File:** `backend/infrastructure/mock_recoater_client/mock_print_controls.py`

Simulates print job operations:
- **Job state simulation:** idle → printing → complete workflow
- **Layer processing simulation** with configurable timing
- **Error injection** for testing error handling
- **Progress tracking** with realistic updates

**Mock Hardware Controls**
Separate modules for drum, blade, and leveler mock implementations:
- **Mock Drum Controls:** Simulate motion, pressure, and positioning
- **Mock Blade Controls:** Simulate screw movements and blade operations
- **Mock Leveler Controls:** Simulate pressure control and sensor readings

**Mock File Management**
**File:** `backend/infrastructure/mock_recoater_client/mock_file_management.py`

Simulates file operations:
- **In-memory file storage** for uploaded files
- **File validation simulation** with error conditions
- **Upload/download timing** to match hardware behavior

**Why it's implemented this way:**

* **Identical Interface Implementation:** Following the **Liskov Substitution Principle**, mock clients can be used anywhere real clients are used without changing other code. This enables true offline development and comprehensive testing without hardware dependencies.

* **Realistic State Simulation:** Instead of static responses, maintaining realistic internal state helps catch timing-related bugs and provides more accurate testing of complex workflows like multi-layer printing jobs.

* **Configurable Error Injection:** Being able to simulate various error conditions systematically helps ensure that error handling code is thoroughly tested. This is especially important in industrial environments where robust error handling is critical.

* **Memory-Based State Management:** Using in-memory state instead of external storage keeps the mock lightweight and fast while still providing realistic behavior for testing multi-step operations.

**Alternatives considered:**

* **Static Response Mocks:** Simple mocks that return fixed responses. This was rejected because it wouldn't catch state-related bugs or provide realistic testing of complex workflows.

* **Database-Backed Mock State:** Persisting mock state to a database. This was considered unnecessary complexity for development and testing scenarios where session-based state is sufficient.

* **External Mock Server:** Running a separate mock server process. This was rejected because it would add deployment complexity and make it harder to configure different testing scenarios.

* **Partial Mocking:** Mocking only some components while using real hardware for others. This was rejected because it would make testing scenarios inconsistent and require partial hardware setup for development.

**Educational Note:** The mock client system demonstrates the **Factory Pattern** for client creation, the **State Pattern** for simulating hardware state transitions, and the **Strategy Pattern** for switching between mock and real implementations based on configuration.

#### 5.4.4 Infrastructure Design Patterns

**How Infrastructure Design Patterns work together:**

The infrastructure layer demonstrates several key design patterns working in harmony to create a flexible, testable, and maintainable system.

**Pattern Integration Overview:**

```mermaid
graph TB
    subgraph "Design Patterns Integration"
        subgraph "Client Factory (Factory Pattern)"
            CF["Configuration-driven instantiation"]
            MS["Mock vs Real client selection"]
            DI["Dependency injection compatibility"]
        end
        
        subgraph "CLI Editor (Facade + Strategy + Mixin)"
            FAC["Facade: Unified interface"]
            STRAT["Strategy: ASCII vs Binary"]
            MIX["Mixin: Compositional capabilities"]
        end
        
        subgraph "Hardware Clients (Adapter + Template)"
            ADP["Adapter: Sync-to-async conversion"]
            TEMP["Template Method: Common patterns"]
            DEC["Decorator: Error handling"]
        end
        
        subgraph "Mock Implementation (State + Factory)"
            STATE["State: Hardware simulation"]
            FACT["Factory: Mock vs real creation"]
            STRAT2["Strategy: Configurable behavior"]
        end
    end
    
    CF --> MS
    MS --> DI
    FAC --> STRAT
    STRAT --> MIX
    ADP --> TEMP
    TEMP --> DEC
    STATE --> FACT
    FACT --> STRAT2
```

**Adapter Pattern Implementation:**
Both real and mock clients implement the same interface, allowing runtime switching:
```python
# Configuration-driven client selection
if USE_MOCK_CLIENT:
    client = MockRecoaterClient()
else:
    client = RecoaterClient(hardware_endpoint)
```

**Mixin Composition in CLI Editor:**
Follows the same architectural pattern as service layer:
- **Single Responsibility:** Each mixin handles one aspect (parsing, generation, rendering)
- **Open/Closed Principle:** Easy to extend with new formats or capabilities
- **Composition over Inheritance:** Flexible combination of capabilities

**Error Handling Strategy:**
Consistent exception hierarchy across all infrastructure components:
- **Connection Errors:** Network and connectivity issues
- **API Errors:** Protocol and response validation problems
- **Timeout Errors:** Operation timing and performance issues
- **Configuration Errors:** Setup and initialization problems

**Resilience Patterns:**
- **Retry Logic:** Automatic retry with exponential backoff
- **Circuit Breaker:** Fail fast when hardware is unavailable
- **Graceful Degradation:** Continue operation with reduced functionality
- **Health Checks:** Regular connectivity validation

**Why these patterns are implemented this way:**

* **Factory Pattern for Client Creation:** Centralizing client instantiation logic allows for easy switching between mock and real implementations based on configuration. This is essential for supporting both development and production environments from the same codebase.

* **Adapter Pattern for Async Integration:** The sync-to-async adapter allows gradual migration and maintains compatibility with existing synchronous hardware APIs while supporting FastAPI's async framework. This prevents the need for complete rewrites.

* **Facade Pattern for Complexity Management:** The CLI Editor facade hides the complexity of multiple file formats and processing strategies behind a simple interface. This makes the system easier to use while maintaining internal flexibility.

* **Strategy Pattern for Format Handling:** Different processing strategies (ASCII vs Binary, Real vs Mock) can be selected at runtime based on file type or configuration. This enables the system to handle diverse requirements without code duplication.

**Alternatives considered:**

* **Single Large Client Class:** All infrastructure functionality in monolithic classes. This was rejected because it would violate separation of concerns and make testing individual components difficult.

* **Direct Hardware Integration:** Bypassing abstraction layers for direct hardware communication. This was rejected because it would make the system harder to test and less portable across different hardware configurations.

* **Configuration-Time Client Selection:** Choosing client implementation at build time rather than runtime. This was rejected because it would require separate builds for development and production, making deployment more complex.

* **No Mock Implementation:** Testing only with real hardware. This was rejected because it would make development slower, more expensive, and limit testing scenarios to available hardware configurations.

**Educational Note:** The infrastructure layer demonstrates how multiple design patterns can work together synergistically. The **Factory Pattern** enables flexible instantiation, the **Adapter Pattern** bridges interface mismatches, the **Facade Pattern** simplifies complex interactions, and the **Strategy Pattern** enables runtime behavior selection - all working together to create a robust, flexible system.

***

## 6. Frontend Development

### 6.1 Layered architecture

- Presentation: views and components using the Composition API
- Application: stores and business logic via Pinia
- Service: API and WebSocket communication modules
- Infrastructure: utilities, mappers, and external integrations
- Component hierarchy and dependency direction
- State management and normalization
- Routing and navigation structure

### 6.2 Component development

- Composition API patterns and lifecycle hooks
- Props, emits, and event conventions
- Slot usage for extensible UI
- Component organization: DrumControl, HopperControl, LevelerControl
- Status indicators and real-time data display

### 6.3 State management

- Pinia store setup and modular organization
- Actions, getters, and derived state
- Store composition and inter-store communication
- Print job state management
- Status polling and WebSocket integration

### 6.4 Routing

- Vue Router config and route-based code-splitting
- Route guards for auth/roles and data prefetching
- Navigation patterns and error routes
- Views: Configuration, Print, Recoater, Status, Debug

### 6.5 API integration

- HTTP client setup with base URL and interceptors
- Request typing, error handling, and retries/backoff
- Loading and empty states patterns
- Axios configuration and response handling

### 6.6 Real-time features

- WebSocket client setup and reconnect/backoff rules
- Event naming conventions and payload schemas
- State synchronization and UI updates
- Job reconnection notifications and error handling

### 6.7 Styling and UI

- CSS organization and component-scoped styles
- Shared design tokens and variables
- Responsive layouts and accessibility basics
- Industrial UI design patterns

***

## 7. Testing

### 7.1 Strategy

- Emphasize fast unit tests, targeted integration tests, and critical-path end-to-end tests
- Use mocks/fakes for hardware and network boundaries
- Gate merges on meaningful coverage thresholds (team-defined)
- Test both mock and production hardware scenarios

### 7.2 Backend testing

- Unit tests for services, validators, and mappers
- Integration tests for routers and infrastructure adapters
- API testing with HTTP clients and schema assertions
- Mock strategies for OPC UA, file system, and external services
- CLI file processing and multi-layer job testing

### 7.3 Frontend testing

- Component tests for views and UI logic
- Unit tests for stores and services
- E2E tests for primary user flows and error surfaces
- WebSocket connection and reconnection testing

### 7.4 Test environment setup

- Backend: test settings, fixtures, and in-memory/mocked adapters
- Frontend: test runner config, test utils, and DOM environment
- Mock OPC UA server for integration testing
- Test data and CLI file fixtures

### 7.5 Running tests

```bash
# Backend
cd backend
pytest -q

# Frontend
cd frontend
npm run test
# E2E (example)
npm run test:e2e
```

### 7.6 Coverage

```bash
# Backend
pytest --cov=app --cov-report=term-missing

# Frontend
npm run test -- --coverage
```

***

## 8. API Documentation (Reference)

### 8.1 API overview

- Base URL: http://localhost:8000 (development)
- RESTful design with OpenAPI/Swagger documentation
- Content types: application/json for REST, WebSocket for real-time
- Conventions for pagination, filtering, sorting, and errors

### 8.2 Authentication

- Currently no authentication required (industrial environment)
- Future: bearer tokens, API keys, or certificate-based auth
- WebSocket connections inherit HTTP authentication context

### 8.3 Endpoint reference

Organize endpoints by tags/domains:

#### Print Control APIs
- `/api/print/job/*` - Multi-layer job management
- `/api/print/layer/*` - Single layer operations
- `/api/print/cli/*` - CLI file upload and processing
- `/api/print/drum/*` - Drum selection and configuration

#### Recoater Control APIs
- `/api/recoater/drum/*` - Drum movement and positioning
- `/api/recoater/leveler/*` - Leveler height and movement
- `/api/recoater/blade/*` - Blade controls and settings

#### Status APIs
- `/api/status` - Overall system status
- `/api/status/detailed` - Detailed component status
- `/api/errors` - Error history and current errors

#### Configuration APIs
- `/api/configuration` - System configuration management
- `/api/configuration/opcua` - OPC UA settings

For each endpoint include:
- Method and path
- Purpose and required permissions
- Request schema with example
- Response schema with example
- Error conditions and codes

### 8.4 WebSocket events

For each event include:
- Event name and purpose
- Payload schema and example
- Client-side behavior and acknowledgements

Key events:
- `status_update` - Real-time system status
- `job_progress` - Multi-layer job progress
- `error_occurred` - Error notifications
- `connection_status` - Hardware connection changes

### 8.5 Error codes

- Centralized registry of error codes, messages, and HTTP status mapping
- Recommended recovery and UI guidance
- Hardware-specific error propagation
- Network and communication error handling

### 8.6 Rate limiting

- Limits per route or tag, burst behavior, and headers
- Client guidance for retries/backoff
- WebSocket connection limits and throttling

***

## 9. Appendices

### A. Glossary

- **CLI File**: Command Line Interface file containing layer-by-layer print instructions
- **Recoater**: Hardware system for powder spreading in additive manufacturing
- **OPC UA**: Open Platform Communications Unified Architecture for industrial communication
- **HMI**: Human-Machine Interface
- **LPBF**: Laser Powder Bed Fusion
- **Drum**: Powder container component of the recoater system
- **Leveler**: Component for powder surface leveling
- **Mock Mode**: Development mode with simulated hardware responses

### B. Useful commands

```bash
# Backend development
cd backend
uvicorn app.main:app --reload  # Development server
pytest --cov=app              # Run tests with coverage
python -m app.scripts.check_imports  # Check import dependencies

# Frontend development
cd frontend
npm run dev                    # Development server
npm run build                  # Production build
npm run lint                   # Lint and format
npm run test                   # Run tests

# Project utilities
.\run.bat                     # Windows: Start both backend and frontend
.\install_deps.bat            # Windows: Install all dependencies
```

### C. External resources

- Aerosint Recoater Hardware Documentation
- OPC UA Specification and Client Libraries
- FastAPI Documentation: https://fastapi.tiangolo.com/
- Vue.js 3 Guide: https://vuejs.org/guide/
- Pinia State Management: https://pinia.vuejs.org/

### D. Contributing and style

- Follow modular architecture principles with clear separation of concerns
- Maintain flat directory structure for modules with less than 10 files
- Use descriptive naming conventions for components and services
- Write tests for new features and bug fixes
- Update documentation for API changes
- Follow SLAP and KISS principles in code organization

***

## Navigation map

- **Tutorials**: Quick Start
- **How-to**: How-to Guides
- **Explanation**: Architecture
- **Reference**: API Documentation

Use this mapping to choose the right entry point for learning, doing, or looking up details.