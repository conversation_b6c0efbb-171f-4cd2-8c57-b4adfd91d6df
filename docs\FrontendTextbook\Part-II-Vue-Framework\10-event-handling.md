# Chapter 10: Event Handling and User Interaction

## Learning Objectives
- Master Vue.js event handling patterns
- Understand event modifiers and their use cases
- Implement custom events between components  
- Create interactive user interfaces
- Handle form validation and user input

## Advanced Event Handling

### Event Flow Visualization
```
User Action → DOM Event → Vue Handler → Component Logic → UI Update

Click Button → @click → handleClick() → updateState() → Template Re-render
```

### Event Modifiers in Detail

```vue
<template>
  <div class="event-demo">
    <!-- Prevent form submission -->
    <form @submit.prevent="handleSubmit">
      <input v-model="data" @keyup.enter="quickSubmit">
      <button type="submit">Submit</button>
    </form>
    
    <!-- Stop event bubbling -->
    <div @click="outerClick" class="outer">
      <button @click.stop="innerClick">Won't bubble</button>
    </div>
    
    <!-- Keyboard shortcuts -->
    <input @keyup.ctrl.enter="save" @keyup.esc="cancel" placeholder="Ctrl+Enter to save">
    
    <!-- Mouse button specific -->
    <div @click.right.prevent="showContextMenu">Right-click me</div>
    
    <!-- Fire only once -->
    <button @click.once="trackFirstClick">Track First Click</button>
  </div>
</template>
```

## Custom Events Between Components

### Parent-Child Communication

```vue
<!-- Child Component: DrumControl.vue -->
<template>
  <div class="drum-control">
    <button @click="start">Start Drum</button>
    <button @click="stop">Stop Drum</button>
  </div>
</template>

<script>
export default {
  emits: ['drum-started', 'drum-stopped'],
  setup(props, { emit }) {
    const start = () => {
      emit('drum-started', { drumId: props.drumId, timestamp: Date.now() })
    }
    
    const stop = () => {
      emit('drum-stopped', { drumId: props.drumId })
    }
    
    return { start, stop }
  }
}
</script>
```

```vue
<!-- Parent Component -->
<template>
  <div class="drum-dashboard">
    <DrumControl 
      v-for="drum in drums" 
      :key="drum.id"
      :drum-id="drum.id"
      @drum-started="handleDrumStarted"
      @drum-stopped="handleDrumStopped"
    />
  </div>
</template>

<script>
export default {
  setup() {
    const handleDrumStarted = (data) => {
      console.log(`Drum ${data.drumId} started at ${data.timestamp}`)
    }
    
    const handleDrumStopped = (data) => {
      console.log(`Drum ${data.drumId} stopped`)
    }
    
    return { handleDrumStarted, handleDrumStopped }
  }
}
</script>
```

## Form Handling Patterns

### Reactive Form Validation

```vue
<template>
  <form @submit.prevent="handleSubmit" class="drum-config-form">
    <div class="form-group">
      <input 
        v-model="form.temperature"
        :class="{ error: errors.temperature }"
        @blur="validateTemperature"
        type="number"
        placeholder="Temperature (100-300°C)"
      >
      <span v-if="errors.temperature" class="error-text">{{ errors.temperature }}</span>
    </div>
    
    <div class="form-group">
      <input 
        v-model="form.speed"
        :class="{ error: errors.speed }"
        @input="validateSpeed"
        type="number"
        placeholder="Speed (1-100%)"
      >
      <span v-if="errors.speed" class="error-text">{{ errors.speed }}</span>
    </div>
    
    <button :disabled="!isFormValid" type="submit">
      {{ isSubmitting ? 'Saving...' : 'Save Configuration' }}
    </button>
  </form>
</template>

<script>
export default {
  setup() {
    const form = reactive({
      temperature: '',
      speed: ''
    })
    
    const errors = reactive({})
    const isSubmitting = ref(false)
    
    const validateTemperature = () => {
      const temp = Number(form.temperature)
      if (!temp || temp < 100 || temp > 300) {
        errors.temperature = 'Temperature must be between 100-300°C'
      } else {
        delete errors.temperature
      }
    }
    
    const validateSpeed = () => {
      const speed = Number(form.speed)
      if (!speed || speed < 1 || speed > 100) {
        errors.speed = 'Speed must be between 1-100%'
      } else {
        delete errors.speed
      }
    }
    
    const isFormValid = computed(() => {
      return Object.keys(errors).length === 0 && 
             form.temperature && 
             form.speed
    })
    
    const handleSubmit = async () => {
      validateTemperature()
      validateSpeed()
      
      if (!isFormValid.value) return
      
      isSubmitting.value = true
      try {
        await saveConfiguration(form)
      } finally {
        isSubmitting.value = false
      }
    }
    
    return {
      form,
      errors,
      isSubmitting,
      isFormValid,
      validateTemperature,
      validateSpeed,
      handleSubmit
    }
  }
}
</script>
```

## Interactive Patterns

### Keyboard Navigation

```vue
<template>
  <div class="drum-list" @keydown="handleKeydown" tabindex="0">
    <div 
      v-for="(drum, index) in drums" 
      :key="drum.id"
      :class="{ selected: selectedIndex === index }"
      @click="selectDrum(index)"
    >
      {{ drum.name }}
    </div>
  </div>
</template>

<script>
export default {
  setup() {
    const selectedIndex = ref(0)
    const drums = ref([
      { id: 1, name: 'Drum 1' },
      { id: 2, name: 'Drum 2' },
      { id: 3, name: 'Drum 3' }
    ])
    
    const handleKeydown = (event) => {
      switch (event.key) {
        case 'ArrowUp':
          event.preventDefault()
          selectedIndex.value = Math.max(0, selectedIndex.value - 1)
          break
        case 'ArrowDown':
          event.preventDefault()
          selectedIndex.value = Math.min(drums.value.length - 1, selectedIndex.value + 1)
          break
        case 'Enter':
          event.preventDefault()
          selectDrum(selectedIndex.value)
          break
      }
    }
    
    const selectDrum = (index) => {
      selectedIndex.value = index
      console.log('Selected:', drums.value[index].name)
    }
    
    return { selectedIndex, drums, handleKeydown, selectDrum }
  }
}
</script>
```

## Real-World Example: Interactive Layer Preview

```vue
<template>
  <div class="interactive-preview">
    <!-- Drag and drop area -->
    <div 
      @drop.prevent="handleDrop"
      @dragover.prevent
      @dragenter.prevent="isDragging = true"
      @dragleave.prevent="isDragging = false"
      :class="{ dragging: isDragging }"
      class="drop-zone"
    >
      <p v-if="!hasFile">Drag CLI file here or click to upload</p>
      <input @change="handleFileSelect" type="file" accept=".cli" ref="fileInput" hidden>
      <button @click="$refs.fileInput.click()">Choose File</button>
    </div>
    
    <!-- Layer navigation -->
    <div v-if="hasFile" class="layer-navigation">
      <button @click="previousLayer" :disabled="currentLayer <= 1">
        ← Previous
      </button>
      
      <input 
        v-model.number="currentLayer"
        @keyup.enter="loadLayer"
        @input="debounceLayerChange"
        type="number"
        :min="1"
        :max="totalLayers"
      >
      
      <button @click="nextLayer" :disabled="currentLayer >= totalLayers">
        Next →
      </button>
      
      <button @click="playAnimation" class="play-btn">
        {{ isPlaying ? '⏸️' : '▶️' }}
      </button>
    </div>
    
    <!-- Preview canvas -->
    <canvas 
      v-if="hasFile"
      ref="canvas"
      @wheel.prevent="handleZoom"
      @mousedown="startPan"
      @mousemove="handlePan"
      @mouseup="endPan"
      width="800"
      height="600"
    ></canvas>
  </div>
</template>

<script>
export default {
  setup() {
    const hasFile = ref(false)
    const currentLayer = ref(1)
    const totalLayers = ref(100)
    const isDragging = ref(false)
    const isPlaying = ref(false)
    const canvas = ref(null)
    
    let debounceTimer = null
    let animationFrame = null
    
    const handleDrop = (event) => {
      isDragging.value = false
      const files = event.dataTransfer.files
      if (files.length > 0) {
        processFile(files[0])
      }
    }
    
    const handleFileSelect = (event) => {
      const file = event.target.files[0]
      if (file) processFile(file)
    }
    
    const processFile = (file) => {
      hasFile.value = true
      // File processing logic
    }
    
    const debounceLayerChange = () => {
      clearTimeout(debounceTimer)
      debounceTimer = setTimeout(loadLayer, 300)
    }
    
    const loadLayer = () => {
      // Load and render layer
      console.log(`Loading layer ${currentLayer.value}`)
    }
    
    const previousLayer = () => {
      if (currentLayer.value > 1) {
        currentLayer.value--
        loadLayer()
      }
    }
    
    const nextLayer = () => {
      if (currentLayer.value < totalLayers.value) {
        currentLayer.value++
        loadLayer()
      }
    }
    
    const playAnimation = () => {
      isPlaying.value = !isPlaying.value
      if (isPlaying.value) {
        startAnimation()
      } else {
        stopAnimation()
      }
    }
    
    const startAnimation = () => {
      const animate = () => {
        if (currentLayer.value < totalLayers.value) {
          currentLayer.value++
          loadLayer()
          animationFrame = setTimeout(animate, 200)
        } else {
          isPlaying.value = false
        }
      }
      animate()
    }
    
    const stopAnimation = () => {
      clearTimeout(animationFrame)
    }
    
    // Canvas interaction
    const handleZoom = (event) => {
      const zoom = event.deltaY > 0 ? 0.9 : 1.1
      // Zoom logic
    }
    
    const startPan = (event) => {
      // Pan start logic
    }
    
    const handlePan = (event) => {
      // Pan logic
    }
    
    const endPan = () => {
      // Pan end logic
    }
    
    return {
      hasFile,
      currentLayer,
      totalLayers,
      isDragging,
      isPlaying,
      canvas,
      handleDrop,
      handleFileSelect,
      debounceLayerChange,
      loadLayer,
      previousLayer,
      nextLayer,
      playAnimation,
      handleZoom,
      startPan,
      handlePan,
      endPan
    }
  }
}
</script>
```

## Best Practices

### Event Handler Organization
- Use descriptive function names
- Keep handlers focused and small
- Separate validation from submission logic
- Use computed properties for conditional logic

### Performance Considerations
- Debounce rapid input events
- Use event delegation for large lists
- Avoid heavy computations in event handlers
- Clean up event listeners on component unmount

## Key Takeaways

1. **Event modifiers** provide powerful, declarative event handling
2. **Custom events** enable clean parent-child communication
3. **Form validation** should be reactive and user-friendly
4. **Keyboard navigation** enhances accessibility
5. **Interactive patterns** create engaging user experiences

## Next Steps

Chapter 11 will cover **State Management with Pinia** for handling complex application state across components.

---

## 🚀 Mini Project: Advanced Event System

### Exercise 10.1: Create Interactive Task Management System

Build a comprehensive event-driven task management system. Create `src/components/InteractiveTaskBoard.vue`:

```vue
<template>
  <div 
    class="task-board"
    @keydown="handleGlobalKeyboard"
    tabindex="0"
    role="application"
    aria-label="Interactive Task Board"
  >
    <!-- Board Header with Actions -->
    <header class="board-header">
      <div class="board-title-section">
        <h1 class="board-title" contenteditable @blur="updateBoardTitle" ref="titleRef">
          {{ boardTitle }}
        </h1>
        <div class="board-stats">
          {{ tasks.length }} tasks • {{ completedTasks.length }} completed
        </div>
      </div>

      <div class="board-actions">
        <!-- Multi-select actions -->
        <div class="selection-actions" v-if="selectedTasks.size > 0">
          <span class="selection-count">
            {{ selectedTasks.size }} selected
          </span>
          <button 
            @click="bulkComplete"
            @keyup.enter="bulkComplete"
            class="bulk-btn complete"
            :disabled="!canBulkComplete"
          >
            ✓ Complete All
          </button>
          <button 
            @click="bulkDelete"
            @keyup.enter="bulkDelete"
            class="bulk-btn delete"
            :disabled="selectedTasks.size === 0"
          >
            🗑️ Delete Selected
          </button>
          <button 
            @click="clearSelection"
            @keyup.enter="clearSelection"
            class="bulk-btn cancel"
          >
            ✕ Clear Selection
          </button>
        </div>

        <!-- View controls -->
        <div class="view-controls">
          <button
            v-for="view in viewModes"
            :key="view.mode"
            @click="changeViewMode(view.mode)"
            :class="['view-btn', { 'active': currentView === view.mode }]"
            :aria-pressed="currentView === view.mode"
          >
            {{ view.icon }} {{ view.label }}
          </button>
        </div>

        <!-- Quick add button -->
        <button
          @click="toggleQuickAdd"
          @keyup.space="toggleQuickAdd"
          class="quick-add-btn"
          :class="{ 'active': showQuickAdd }"
          :aria-expanded="showQuickAdd"
        >
          ➕ Quick Add
        </button>
      </div>
    </header>

    <!-- Quick Add Form -->
    <section 
      class="quick-add-section" 
      v-show="showQuickAdd"
      @keydown.esc="hideQuickAdd"
    >
      <form @submit.prevent="handleQuickAdd" class="quick-add-form">
        <input
          ref="quickAddInput"
          v-model="quickAddText"
          type="text"
          placeholder="Type a task and press Enter..."
          class="quick-add-input"
          @keydown.esc="hideQuickAdd"
          @keydown.ctrl.enter="handleQuickAdd"
        />
        <div class="quick-add-controls">
          <button type="submit" class="quick-submit-btn">Add Task</button>
          <button type="button" @click="hideQuickAdd" class="quick-cancel-btn">Cancel</button>
        </div>
      </form>
    </section>

    <!-- Drag and Drop Zone -->
    <main 
      class="task-container"
      :class="{ 'drag-over': isDragOver }"
      @dragover.prevent="handleDragOver"
      @dragleave.prevent="handleDragLeave"
      @drop.prevent="handleFileDrop"
    >
      <!-- Grid View -->
      <div v-if="currentView === 'grid'" class="tasks-grid">
        <TransitionGroup name="task-grid" tag="div" class="task-grid-container">
          <div
            v-for="task in filteredTasks"
            :key="task.id"
            class="task-grid-item"
            :class="getTaskClasses(task)"
            @click="handleTaskClick(task, $event)"
            @dblclick="editTask(task)"
            @contextmenu.prevent="showTaskContext(task, $event)"
            @keydown="handleTaskKeyboard(task, $event)"
            :tabindex="task.id === focusedTaskId ? 0 : -1"
            :aria-selected="selectedTasks.has(task.id)"
            draggable="true"
            @dragstart="handleDragStart(task, $event)"
            @dragend="handleDragEnd"
          >
            <!-- Task selection checkbox -->
            <div class="task-selection">
              <input
                type="checkbox"
                :checked="selectedTasks.has(task.id)"
                @change="toggleTaskSelection(task.id)"
                @click.stop
                class="task-checkbox"
                :aria-label="`Select ${task.title}`"
              />
            </div>

            <!-- Task content -->
            <div class="task-content">
              <h3 class="task-title">{{ task.title }}</h3>
              <p class="task-description" v-if="task.description">
                {{ task.description }}
              </p>
              <div class="task-meta">
                <span v-if="task.category" class="task-category">
                  {{ getCategoryDisplay(task.category) }}
                </span>
                <span class="task-priority" :data-priority="task.priority">
                  {{ getPriorityDisplay(task.priority) }}
                </span>
                <span v-if="task.dueDate" class="task-due-date">
                  {{ formatDueDate(task.dueDate) }}
                </span>
              </div>
            </div>

            <!-- Task actions -->
            <div class="task-actions">
              <button
                @click.stop="toggleTaskComplete(task)"
                @keyup.enter="toggleTaskComplete(task)"
                @keyup.space="toggleTaskComplete(task)"
                class="task-action-btn complete"
                :class="{ 'completed': task.completed }"
                :aria-label="task.completed ? 'Mark incomplete' : 'Mark complete'"
              >
                {{ task.completed ? '✓' : '○' }}
              </button>
              <button
                @click.stop="editTask(task)"
                @keyup.enter="editTask(task)"
                class="task-action-btn edit"
                aria-label="Edit task"
              >
                ✏️
              </button>
              <button
                @click.stop="deleteTask(task)"
                @keyup.enter="deleteTask(task)"
                class="task-action-btn delete"
                aria-label="Delete task"
              >
                🗑️
              </button>
            </div>
          </div>
        </TransitionGroup>
      </div>

      <!-- List View -->
      <div v-else-if="currentView === 'list'" class="tasks-list">
        <table class="tasks-table" role="grid">
          <thead>
            <tr role="row">
              <th role="columnheader">
                <input
                  type="checkbox"
                  @change="toggleSelectAll"
                  :checked="allTasksSelected"
                  :indeterminate="someTasksSelected"
                  aria-label="Select all tasks"
                />
              </th>
              <th role="columnheader" @click="sortBy('title')" class="sortable">
                Title
                <span class="sort-indicator" v-if="sortField === 'title'">
                  {{ sortDirection === 'asc' ? '↑' : '↓' }}
                </span>
              </th>
              <th role="columnheader" @click="sortBy('priority')" class="sortable">
                Priority
              </th>
              <th role="columnheader" @click="sortBy('dueDate')" class="sortable">
                Due Date
              </th>
              <th role="columnheader">Status</th>
              <th role="columnheader">Actions</th>
            </tr>
          </thead>
          <tbody>
            <tr
              v-for="task in sortedTasks"
              :key="task.id"
              role="row"
              :class="getTaskClasses(task)"
              @click="handleTaskClick(task, $event)"
              @dblclick="editTask(task)"
              @contextmenu.prevent="showTaskContext(task, $event)"
            >
              <td role="gridcell">
                <input
                  type="checkbox"
                  :checked="selectedTasks.has(task.id)"
                  @change="toggleTaskSelection(task.id)"
                  @click.stop
                />
              </td>
              <td role="gridcell" class="task-title-cell">
                <div class="task-title">{{ task.title }}</div>
                <div class="task-description" v-if="task.description">
                  {{ task.description }}
                </div>
              </td>
              <td role="gridcell">
                <span class="priority-badge" :data-priority="task.priority">
                  {{ getPriorityDisplay(task.priority) }}
                </span>
              </td>
              <td role="gridcell">
                <span v-if="task.dueDate" class="due-date">
                  {{ formatDueDate(task.dueDate) }}
                </span>
                <span v-else class="no-due-date">No due date</span>
              </td>
              <td role="gridcell">
                <span class="status-badge" :class="{ 'completed': task.completed }">
                  {{ task.completed ? 'Complete' : 'Pending' }}
                </span>
              </td>
              <td role="gridcell">
                <div class="table-actions">
                  <button
                    @click.stop="toggleTaskComplete(task)"
                    class="table-action-btn"
                    :class="{ 'completed': task.completed }"
                  >
                    {{ task.completed ? '✓' : '○' }}
                  </button>
                  <button @click.stop="editTask(task)" class="table-action-btn">
                    ✏️
                  </button>
                  <button @click.stop="deleteTask(task)" class="table-action-btn delete">
                    🗑️
                  </button>
                </div>
              </td>
            </tr>
          </tbody>
        </table>
      </div>

      <!-- Kanban View -->
      <div v-else-if="currentView === 'kanban'" class="kanban-board">
        <div class="kanban-columns">
          <div
            v-for="column in kanbanColumns"
            :key="column.id"
            class="kanban-column"
            @dragover.prevent="handleColumnDragOver(column.id)"
            @drop.prevent="handleColumnDrop(column.id, $event)"
          >
            <header class="column-header">
              <h2 class="column-title">{{ column.title }}</h2>
              <span class="column-count">{{ getColumnTasks(column.id).length }}</span>
            </header>
            
            <div class="column-content">
              <TransitionGroup name="kanban-card" tag="div" class="kanban-cards">
                <div
                  v-for="task in getColumnTasks(column.id)"
                  :key="task.id"
                  class="kanban-card"
                  :class="getTaskClasses(task)"
                  draggable="true"
                  @dragstart="handleDragStart(task, $event)"
                  @dragend="handleDragEnd"
                  @click="handleTaskClick(task, $event)"
                  @dblclick="editTask(task)"
                >
                  <div class="card-content">
                    <h3 class="card-title">{{ task.title }}</h3>
                    <p class="card-description" v-if="task.description">
                      {{ task.description }}
                    </p>
                    <div class="card-meta">
                      <span class="card-priority" :data-priority="task.priority">
                        {{ getPriorityDisplay(task.priority) }}
                      </span>
                      <span v-if="task.dueDate" class="card-due-date">
                        {{ formatDueDate(task.dueDate) }}
                      </span>
                    </div>
                  </div>
                </div>
              </TransitionGroup>
            </div>
          </div>
        </div>
      </div>

      <!-- Empty state -->
      <div v-if="filteredTasks.length === 0" class="empty-state">
        <div class="empty-icon">📝</div>
        <h2 class="empty-title">No tasks found</h2>
        <p class="empty-description">
          {{ hasFilters ? 'Try adjusting your filters' : 'Create your first task to get started!' }}
        </p>
        <button @click="toggleQuickAdd" class="empty-action-btn">
          ➕ Add Your First Task
        </button>
      </div>
    </main>

    <!-- Context Menu -->
    <div
      ref="contextMenu"
      class="context-menu"
      v-show="showContextMenu"
      :style="contextMenuStyle"
      @click.stop
      @keydown.esc="hideContextMenu"
    >
      <button @click="contextEditTask" class="context-menu-item">
        ✏️ Edit Task
      </button>
      <button @click="contextDuplicateTask" class="context-menu-item">
        📋 Duplicate Task
      </button>
      <hr class="context-menu-divider">
      <button @click="contextToggleComplete" class="context-menu-item">
        {{ contextTask?.completed ? '○ Mark Incomplete' : '✓ Mark Complete' }}
      </button>
      <button @click="contextTogglePriority" class="context-menu-item">
        🎯 Toggle Priority
      </button>
      <hr class="context-menu-divider">
      <button @click="contextDeleteTask" class="context-menu-item danger">
        🗑️ Delete Task
      </button>
    </div>

    <!-- Floating Action Button -->
    <button
      class="floating-action-btn"
      @click="scrollToTop"
      v-show="showScrollToTop"
      @keyup.enter="scrollToTop"
      aria-label="Scroll to top"
    >
      ↑
    </button>

    <!-- Keyboard shortcuts help -->
    <div class="shortcuts-help" v-if="showShortcutsHelp">
      <h3>⌨️ Keyboard Shortcuts</h3>
      <div class="shortcuts-grid">
        <div class="shortcut-item">
          <kbd>N</kbd> <span>New task</span>
        </div>
        <div class="shortcut-item">
          <kbd>A</kbd> <span>Select all</span>
        </div>
        <div class="shortcut-item">
          <kbd>Escape</kbd> <span>Clear selection</span>
        </div>
        <div class="shortcut-item">
          <kbd>Delete</kbd> <span>Delete selected</span>
        </div>
        <div class="shortcut-item">
          <kbd>Space</kbd> <span>Toggle complete</span>
        </div>
        <div class="shortcut-item">
          <kbd>Enter</kbd> <span>Edit task</span>
        </div>
        <div class="shortcut-item">
          <kbd>1-3</kbd> <span>Switch view</span>
        </div>
        <div class="shortcut-item">
          <kbd>?</kbd> <span>Toggle this help</span>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup>
import { ref, reactive, computed, watch, onMounted, onUnmounted, nextTick } from 'vue'
import { useDateFormatting } from '../composables/useDateFormatting'
import { useTaskInteraction } from '../composables/useTaskInteraction'
import { useKeyboardShortcuts } from '../composables/useKeyboardShortcuts'

// Props
const props = defineProps({
  tasks: { type: Array, default: () => [] },
  categories: { type: Array, default: () => [] },
  filters: { type: Object, default: () => ({}) }
})

// Emits
const emit = defineEmits([
  'task-updated',
  'task-deleted',
  'task-created',
  'tasks-bulk-updated',
  'board-title-updated'
])

// Composables
const { formatDueDate } = useDateFormatting()
const {
  selectedTasks,
  focusedTaskId,
  toggleTaskSelection,
  clearSelection,
  selectAll,
  handleTaskClick
} = useTaskInteraction()

const {
  registerShortcut,
  unregisterShortcut
} = useKeyboardShortcuts()

// Reactive state
const boardTitle = ref('My Task Board')
const currentView = ref('grid')
const showQuickAdd = ref(false)
const quickAddText = ref('')
const isDragOver = ref(false)
const showContextMenu = ref(false)
const contextTask = ref(null)
const contextMenuStyle = ref({})
const showScrollToTop = ref(false)
const showShortcutsHelp = ref(false)
const sortField = ref('createdAt')
const sortDirection = ref('desc')

// Refs
const titleRef = ref(null)
const quickAddInput = ref(null)
const contextMenu = ref(null)

// View modes
const viewModes = [
  { mode: 'grid', label: 'Grid', icon: '⊞' },
  { mode: 'list', label: 'List', icon: '☰' },
  { mode: 'kanban', label: 'Kanban', icon: '📋' }
]

// Kanban columns
const kanbanColumns = [
  { id: 'pending', title: 'To Do' },
  { id: 'in-progress', title: 'In Progress' },
  { id: 'completed', title: 'Done' }
]

// Computed properties
const filteredTasks = computed(() => {
  let filtered = [...props.tasks]
  
  // Apply filters
  if (props.filters.search) {
    const search = props.filters.search.toLowerCase()
    filtered = filtered.filter(task => 
      task.title.toLowerCase().includes(search) ||
      (task.description && task.description.toLowerCase().includes(search))
    )
  }
  
  if (props.filters.category) {
    filtered = filtered.filter(task => task.category === props.filters.category)
  }
  
  if (props.filters.status) {
    filtered = filtered.filter(task => {
      switch (props.filters.status) {
        case 'completed': return task.completed
        case 'pending': return !task.completed
        case 'overdue': return !task.completed && task.dueDate && new Date(task.dueDate) < new Date()
        default: return true
      }
    })
  }
  
  if (props.filters.priority) {
    filtered = filtered.filter(task => task.priority === props.filters.priority)
  }
  
  return filtered
})

const sortedTasks = computed(() => {
  return [...filteredTasks.value].sort((a, b) => {
    let aValue = a[sortField.value]
    let bValue = b[sortField.value]
    
    // Handle different data types
    if (sortField.value === 'dueDate') {
      aValue = aValue ? new Date(aValue) : new Date('9999-12-31')
      bValue = bValue ? new Date(bValue) : new Date('9999-12-31')
    }
    
    if (sortField.value === 'priority') {
      const priorityOrder = { high: 3, medium: 2, low: 1 }
      aValue = priorityOrder[aValue] || 0
      bValue = priorityOrder[bValue] || 0
    }
    
    if (aValue < bValue) return sortDirection.value === 'asc' ? -1 : 1
    if (aValue > bValue) return sortDirection.value === 'asc' ? 1 : -1
    return 0
  })
})

const completedTasks = computed(() => 
  props.tasks.filter(task => task.completed)
)

const allTasksSelected = computed(() => 
  filteredTasks.value.length > 0 && 
  selectedTasks.value.size === filteredTasks.value.length
)

const someTasksSelected = computed(() => 
  selectedTasks.value.size > 0 && 
  selectedTasks.value.size < filteredTasks.value.length
)

const canBulkComplete = computed(() => 
  Array.from(selectedTasks.value).some(id => 
    !props.tasks.find(t => t.id === id)?.completed
  )
)

const hasFilters = computed(() => 
  Object.values(props.filters).some(filter => filter)
)

// Methods
const changeViewMode = (mode) => {
  currentView.value = mode
  clearSelection()
}

const toggleQuickAdd = () => {
  showQuickAdd.value = !showQuickAdd.value
  if (showQuickAdd.value) {
    nextTick(() => {
      quickAddInput.value?.focus()
    })
  }
}

const hideQuickAdd = () => {
  showQuickAdd.value = false
  quickAddText.value = ''
}

const handleQuickAdd = () => {
  if (quickAddText.value.trim()) {
    const newTask = {
      title: quickAddText.value.trim(),
      completed: false,
      createdAt: new Date().toISOString()
    }
    emit('task-created', newTask)
    quickAddText.value = ''
    hideQuickAdd()
  }
}

const updateBoardTitle = (event) => {
  const newTitle = event.target.textContent.trim()
  if (newTitle && newTitle !== boardTitle.value) {
    boardTitle.value = newTitle
    emit('board-title-updated', newTitle)
  }
}

const getTaskClasses = (task) => ({
  'task-selected': selectedTasks.value.has(task.id),
  'task-completed': task.completed,
  'task-focused': task.id === focusedTaskId.value,
  'task-overdue': !task.completed && task.dueDate && new Date(task.dueDate) < new Date(),
  [`priority-${task.priority}`]: task.priority
})

const getCategoryDisplay = (categoryId) => {
  const category = props.categories.find(c => c.id === categoryId)
  return category ? `${category.emoji} ${category.name}` : categoryId
}

const getPriorityDisplay = (priority) => {
  const displays = { high: '🔴 High', medium: '🟡 Medium', low: '🟢 Low' }
  return displays[priority] || priority
}

const toggleTaskComplete = (task) => {
  emit('task-updated', {
    ...task,
    completed: !task.completed,
    completedAt: !task.completed ? new Date().toISOString() : null
  })
}

const editTask = (task) => {
  // Emit event to open edit modal/form
  emit('task-edit-requested', task)
}

const deleteTask = (task) => {
  if (confirm(`Delete "${task.title}"?`)) {
    emit('task-deleted', task)
  }
}

const bulkComplete = () => {
  const tasksToUpdate = Array.from(selectedTasks.value)
    .map(id => props.tasks.find(t => t.id === id))
    .filter(task => task && !task.completed)
    .map(task => ({
      ...task,
      completed: true,
      completedAt: new Date().toISOString()
    }))
  
  if (tasksToUpdate.length > 0) {
    emit('tasks-bulk-updated', tasksToUpdate)
  }
}

const bulkDelete = () => {
  const taskCount = selectedTasks.value.size
  if (confirm(`Delete ${taskCount} selected task${taskCount !== 1 ? 's' : ''}?`)) {
    const tasksToDelete = Array.from(selectedTasks.value)
      .map(id => props.tasks.find(t => t.id === id))
      .filter(Boolean)
    
    tasksToDelete.forEach(task => emit('task-deleted', task))
    clearSelection()
  }
}

const toggleSelectAll = () => {
  if (allTasksSelected.value) {
    clearSelection()
  } else {
    selectAll(filteredTasks.value.map(t => t.id))
  }
}

const sortBy = (field) => {
  if (sortField.value === field) {
    sortDirection.value = sortDirection.value === 'asc' ? 'desc' : 'asc'
  } else {
    sortField.value = field
    sortDirection.value = 'asc'
  }
}

// Context menu
const showTaskContext = (task, event) => {
  contextTask.value = task
  contextMenuStyle.value = {
    left: `${event.clientX}px`,
    top: `${event.clientY}px`
  }
  showContextMenu.value = true
  
  // Hide context menu when clicking elsewhere
  const hideContext = () => {
    showContextMenu.value = false
    document.removeEventListener('click', hideContext)
  }
  document.addEventListener('click', hideContext)
}

const hideContextMenu = () => {
  showContextMenu.value = false
  contextTask.value = null
}

const contextEditTask = () => {
  if (contextTask.value) {
    editTask(contextTask.value)
    hideContextMenu()
  }
}

const contextDuplicateTask = () => {
  if (contextTask.value) {
    const duplicatedTask = {
      ...contextTask.value,
      id: undefined, // Will be assigned new ID
      title: `${contextTask.value.title} (Copy)`,
      completed: false,
      createdAt: new Date().toISOString(),
      completedAt: null
    }
    emit('task-created', duplicatedTask)
    hideContextMenu()
  }
}

const contextToggleComplete = () => {
  if (contextTask.value) {
    toggleTaskComplete(contextTask.value)
    hideContextMenu()
  }
}

const contextTogglePriority = () => {
  if (contextTask.value) {
    const priorities = ['low', 'medium', 'high']
    const currentIndex = priorities.indexOf(contextTask.value.priority)
    const nextPriority = priorities[(currentIndex + 1) % priorities.length]
    
    emit('task-updated', {
      ...contextTask.value,
      priority: nextPriority
    })
    hideContextMenu()
  }
}

const contextDeleteTask = () => {
  if (contextTask.value) {
    deleteTask(contextTask.value)
    hideContextMenu()
  }
}

// Drag and drop
const handleDragStart = (task, event) => {
  event.dataTransfer.setData('text/plain', JSON.stringify(task))
  event.dataTransfer.effectAllowed = 'move'
}

const handleDragEnd = () => {
  // Cleanup after drag operation
}

const handleDragOver = (event) => {
  isDragOver.value = true
}

const handleDragLeave = () => {
  isDragOver.value = false
}

const handleFileDrop = (event) => {
  isDragOver.value = false
  
  // Handle file imports
  const files = Array.from(event.dataTransfer.files)
  if (files.length > 0) {
    emit('files-dropped', files)
  }
}

const handleColumnDragOver = (columnId) => {
  // Handle kanban column drag over
}

const handleColumnDrop = (columnId, event) => {
  try {
    const taskData = JSON.parse(event.dataTransfer.getData('text/plain'))
    
    // Update task status based on column
    const updates = {
      pending: { completed: false },
      'in-progress': { completed: false, status: 'in-progress' },
      completed: { completed: true, completedAt: new Date().toISOString() }
    }
    
    if (updates[columnId]) {
      emit('task-updated', { ...taskData, ...updates[columnId] })
    }
  } catch (error) {
    console.error('Failed to process drop:', error)
  }
}

const getColumnTasks = (columnId) => {
  switch (columnId) {
    case 'pending':
      return filteredTasks.value.filter(task => !task.completed && !task.status)
    case 'in-progress':
      return filteredTasks.value.filter(task => !task.completed && task.status === 'in-progress')
    case 'completed':
      return filteredTasks.value.filter(task => task.completed)
    default:
      return []
  }
}

// Keyboard navigation
const handleGlobalKeyboard = (event) => {
  // Only handle if no input is focused
  if (event.target.tagName === 'INPUT' || event.target.tagName === 'TEXTAREA') {
    return
  }
  
  switch (event.key) {
    case 'n':
    case 'N':
      event.preventDefault()
      toggleQuickAdd()
      break
      
    case 'a':
    case 'A':
      if (event.ctrlKey || event.metaKey) {
        event.preventDefault()
        toggleSelectAll()
      }
      break
      
    case 'Escape':
      if (showContextMenu.value) {
        hideContextMenu()
      } else if (selectedTasks.value.size > 0) {
        clearSelection()
      } else if (showQuickAdd.value) {
        hideQuickAdd()
      }
      break
      
    case 'Delete':
    case 'Backspace':
      if (selectedTasks.value.size > 0) {
        event.preventDefault()
        bulkDelete()
      }
      break
      
    case '1':
      changeViewMode('grid')
      break
    case '2':
      changeViewMode('list')
      break
    case '3':
      changeViewMode('kanban')
      break
      
    case '?':
      showShortcutsHelp.value = !showShortcutsHelp.value
      break
  }
}

const handleTaskKeyboard = (task, event) => {
  switch (event.key) {
    case ' ':
      event.preventDefault()
      toggleTaskComplete(task)
      break
      
    case 'Enter':
      event.preventDefault()
      editTask(task)
      break
      
    case 'Delete':
      event.preventDefault()
      deleteTask(task)
      break
  }
}

const scrollToTop = () => {
  window.scrollTo({ top: 0, behavior: 'smooth' })
}

// Lifecycle
onMounted(() => {
  // Register keyboard shortcuts
  registerShortcut('ctrl+n', () => toggleQuickAdd())
  registerShortcut('ctrl+a', () => toggleSelectAll())
  registerShortcut('delete', () => selectedTasks.value.size > 0 && bulkDelete())
  
  // Listen for scroll to show/hide scroll to top button
  const handleScroll = () => {
    showScrollToTop.value = window.scrollY > 300
  }
  
  window.addEventListener('scroll', handleScroll)
  
  // Cleanup
  onUnmounted(() => {
    window.removeEventListener('scroll', handleScroll)
    unregisterShortcut('ctrl+n')
    unregisterShortcut('ctrl+a')
    unregisterShortcut('delete')
  })
})
</script>

<style scoped>
.task-board {
  min-height: 100vh;
  background: var(--color-background);
  position: relative;
}

.board-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 2rem;
  background: var(--color-background-soft);
  border-bottom: 1px solid var(--color-border);
  flex-wrap: wrap;
  gap: 1rem;
}

.board-title-section {
  flex: 1;
  min-width: 200px;
}

.board-title {
  margin: 0;
  font-size: 2rem;
  font-weight: 700;
  color: var(--color-text);
  border: 2px solid transparent;
  padding: 0.25rem;
  border-radius: 4px;
  outline: none;
}

.board-title:focus {
  border-color: var(--color-primary);
  background: var(--color-background);
}

.board-stats {
  color: var(--color-text-light);
  font-size: 0.875rem;
  margin-top: 0.25rem;
}

.board-actions {
  display: flex;
  align-items: center;
  gap: 1rem;
  flex-wrap: wrap;
}

.selection-actions {
  display: flex;
  align-items: center;
  gap: 0.5rem;
  padding: 0.5rem 1rem;
  background: var(--color-primary-light);
  border-radius: 8px;
  border: 1px solid var(--color-primary);
}

.selection-count {
  font-size: 0.875rem;
  font-weight: 500;
  color: var(--color-primary-dark);
}

.bulk-btn {
  padding: 0.25rem 0.75rem;
  border: none;
  border-radius: 4px;
  font-size: 0.875rem;
  cursor: pointer;
  transition: all 0.2s ease;
}

.bulk-btn.complete { background: var(--color-success); color: white; }
.bulk-btn.delete { background: var(--color-danger); color: white; }
.bulk-btn.cancel { background: var(--color-secondary); color: white; }

.view-controls {
  display: flex;
  border: 1px solid var(--color-border);
  border-radius: 6px;
  overflow: hidden;
}

.view-btn {
  padding: 0.5rem 1rem;
  border: none;
  background: var(--color-background);
  color: var(--color-text);
  cursor: pointer;
  transition: all 0.2s ease;
  border-right: 1px solid var(--color-border);
}

.view-btn:last-child {
  border-right: none;
}

.view-btn.active {
  background: var(--color-primary);
  color: white;
}

.quick-add-btn {
  padding: 0.75rem 1.5rem;
  background: var(--color-primary);
  color: white;
  border: none;
  border-radius: 6px;
  font-size: 0.875rem;
  font-weight: 500;
  cursor: pointer;
  transition: all 0.2s ease;
}

.quick-add-btn:hover {
  background: var(--color-primary-dark);
}

.quick-add-section {
  padding: 1rem 2rem;
  background: var(--color-background);
  border-bottom: 1px solid var(--color-border);
}

.quick-add-form {
  display: flex;
  gap: 1rem;
  align-items: center;
  max-width: 600px;
}

.quick-add-input {
  flex: 1;
  padding: 0.75rem;
  border: 2px solid var(--color-border);
  border-radius: 4px;
  font-size: 1rem;
}

.quick-add-input:focus {
  outline: none;
  border-color: var(--color-primary);
}

.quick-add-controls {
  display: flex;
  gap: 0.5rem;
}

.quick-submit-btn {
  padding: 0.75rem 1.5rem;
  background: var(--color-primary);
  color: white;
  border: none;
  border-radius: 4px;
  cursor: pointer;
}

.quick-cancel-btn {
  padding: 0.75rem 1rem;
  background: var(--color-secondary);
  color: white;
  border: none;
  border-radius: 4px;
  cursor: pointer;
}

.task-container {
  padding: 2rem;
  min-height: 400px;
  transition: background-color 0.2s ease;
}

.task-container.drag-over {
  background: var(--color-background-soft);
}

/* Grid View Styles */
.tasks-grid {
  display: grid;
  grid-template-columns: repeat(auto-fill, minmax(300px, 1fr));
  gap: 1.5rem;
}

.task-grid-item {
  background: var(--color-background);
  border: 2px solid var(--color-border);
  border-radius: 8px;
  padding: 1rem;
  transition: all 0.2s ease;
  cursor: pointer;
  position: relative;
}

.task-grid-item:hover {
  border-color: var(--color-primary);
  transform: translateY(-2px);
  box-shadow: 0 4px 12px rgba(0,0,0,0.1);
}

.task-grid-item.task-selected {
  border-color: var(--color-primary);
  background: var(--color-primary-light);
}

.task-grid-item.task-completed {
  opacity: 0.7;
}

.task-grid-item.task-overdue {
  border-color: var(--color-danger);
}

.task-selection {
  position: absolute;
  top: 0.5rem;
  left: 0.5rem;
}

.task-checkbox {
  width: 1.25rem;
  height: 1.25rem;
}

.task-content {
  margin: 0.5rem 0 2rem 0;
}

.task-title {
  margin: 0 0 0.5rem 0;
  font-size: 1.125rem;
  font-weight: 600;
  color: var(--color-text);
}

.task-description {
  margin: 0 0 0.75rem 0;
  color: var(--color-text-light);
  font-size: 0.875rem;
  line-height: 1.4;
}

.task-meta {
  display: flex;
  flex-wrap: wrap;
  gap: 0.5rem;
  font-size: 0.75rem;
}

.task-category,
.task-priority,
.task-due-date {
  padding: 0.25rem 0.5rem;
  border-radius: 4px;
  background: var(--color-background-soft);
  color: var(--color-text);
}

.task-actions {
  position: absolute;
  bottom: 0.5rem;
  right: 0.5rem;
  display: flex;
  gap: 0.25rem;
}

.task-action-btn {
  width: 2rem;
  height: 2rem;
  border: none;
  border-radius: 4px;
  background: var(--color-background-soft);
  cursor: pointer;
  transition: all 0.2s ease;
  display: flex;
  align-items: center;
  justify-content: center;
}

.task-action-btn:hover {
  background: var(--color-primary);
  color: white;
}

/* List View Styles */
.tasks-table {
  width: 100%;
  border-collapse: collapse;
  background: var(--color-background);
  border-radius: 8px;
  overflow: hidden;
  box-shadow: 0 2px 8px rgba(0,0,0,0.1);
}

.tasks-table th {
  background: var(--color-background-soft);
  padding: 1rem;
  text-align: left;
  font-weight: 600;
  color: var(--color-text);
  border-bottom: 1px solid var(--color-border);
}

.tasks-table th.sortable {
  cursor: pointer;
  user-select: none;
}

.tasks-table th.sortable:hover {
  background: var(--color-primary-light);
}

.sort-indicator {
  margin-left: 0.5rem;
  color: var(--color-primary);
}

.tasks-table td {
  padding: 1rem;
  border-bottom: 1px solid var(--color-border);
  vertical-align: top;
}

.tasks-table tr:hover {
  background: var(--color-background-soft);
}

.tasks-table tr.task-selected {
  background: var(--color-primary-light);
}

.task-title-cell .task-title {
  font-weight: 500;
  margin-bottom: 0.25rem;
}

.task-title-cell .task-description {
  font-size: 0.875rem;
  color: var(--color-text-light);
}

.priority-badge,
.status-badge {
  padding: 0.25rem 0.75rem;
  border-radius: 12px;
  font-size: 0.75rem;
  font-weight: 500;
}

.priority-badge[data-priority="high"] { background: var(--color-danger-light); color: var(--color-danger-dark); }
.priority-badge[data-priority="medium"] { background: var(--color-warning-light); color: var(--color-warning-dark); }
.priority-badge[data-priority="low"] { background: var(--color-success-light); color: var(--color-success-dark); }

.status-badge {
  background: var(--color-warning-light);
  color: var(--color-warning-dark);
}

.status-badge.completed {
  background: var(--color-success-light);
  color: var(--color-success-dark);
}

.table-actions {
  display: flex;
  gap: 0.25rem;
}

.table-action-btn {
  width: 2rem;
  height: 2rem;
  border: none;
  border-radius: 4px;
  background: var(--color-background-soft);
  cursor: pointer;
  transition: all 0.2s ease;
}

/* Kanban View Styles */
.kanban-board {
  height: calc(100vh - 300px);
  overflow-x: auto;
}

.kanban-columns {
  display: flex;
  gap: 1.5rem;
  min-width: max-content;
  height: 100%;
}

.kanban-column {
  width: 300px;
  background: var(--color-background-soft);
  border-radius: 8px;
  display: flex;
  flex-direction: column;
}

.column-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 1rem;
  border-bottom: 1px solid var(--color-border);
}

.column-title {
  margin: 0;
  font-size: 1.125rem;
  font-weight: 600;
  color: var(--color-text);
}

.column-count {
  background: var(--color-primary);
  color: white;
  padding: 0.25rem 0.5rem;
  border-radius: 12px;
  font-size: 0.75rem;
  font-weight: 500;
}

.column-content {
  flex: 1;
  padding: 1rem;
  overflow-y: auto;
}

.kanban-cards {
  display: flex;
  flex-direction: column;
  gap: 1rem;
}

.kanban-card {
  background: var(--color-background);
  border: 1px solid var(--color-border);
  border-radius: 6px;
  padding: 1rem;
  cursor: pointer;
  transition: all 0.2s ease;
}

.kanban-card:hover {
  border-color: var(--color-primary);
  transform: translateY(-1px);
  box-shadow: 0 2px 8px rgba(0,0,0,0.1);
}

.card-content .card-title {
  margin: 0 0 0.5rem 0;
  font-size: 1rem;
  font-weight: 500;
  color: var(--color-text);
}

.card-content .card-description {
  margin: 0 0 0.75rem 0;
  font-size: 0.875rem;
  color: var(--color-text-light);
  line-height: 1.4;
}

.card-meta {
  display: flex;
  justify-content: space-between;
  align-items: center;
  font-size: 0.75rem;
}

/* Context Menu */
.context-menu {
  position: fixed;
  background: var(--color-background);
  border: 1px solid var(--color-border);
  border-radius: 6px;
  box-shadow: 0 4px 12px rgba(0,0,0,0.15);
  z-index: 1000;
  min-width: 200px;
  overflow: hidden;
}

.context-menu-item {
  display: block;
  width: 100%;
  padding: 0.75rem 1rem;
  border: none;
  background: none;
  text-align: left;
  cursor: pointer;
  transition: background-color 0.2s ease;
  color: var(--color-text);
}

.context-menu-item:hover {
  background: var(--color-background-soft);
}

.context-menu-item.danger {
  color: var(--color-danger);
}

.context-menu-divider {
  margin: 0;
  border: none;
  border-top: 1px solid var(--color-border);
}

/* Empty State */
.empty-state {
  text-align: center;
  padding: 4rem 2rem;
}

.empty-icon {
  font-size: 4rem;
  margin-bottom: 1rem;
}

.empty-title {
  font-size: 1.5rem;
  margin-bottom: 0.5rem;
  color: var(--color-text);
}

.empty-description {
  color: var(--color-text-light);
  margin-bottom: 2rem;
}

.empty-action-btn {
  padding: 0.75rem 1.5rem;
  background: var(--color-primary);
  color: white;
  border: none;
  border-radius: 6px;
  cursor: pointer;
  font-size: 0.875rem;
}

/* Floating Action Button */
.floating-action-btn {
  position: fixed;
  bottom: 2rem;
  right: 2rem;
  width: 3rem;
  height: 3rem;
  border-radius: 50%;
  background: var(--color-primary);
  color: white;
  border: none;
  cursor: pointer;
  box-shadow: 0 4px 12px rgba(0,0,0,0.15);
  transition: all 0.2s ease;
  z-index: 100;
}

.floating-action-btn:hover {
  transform: translateY(-2px);
  box-shadow: 0 6px 16px rgba(0,0,0,0.2);
}

/* Keyboard Shortcuts Help */
.shortcuts-help {
  position: fixed;
  top: 50%;
  left: 50%;
  transform: translate(-50%, -50%);
  background: var(--color-background);
  border: 1px solid var(--color-border);
  border-radius: 8px;
  padding: 2rem;
  box-shadow: 0 8px 24px rgba(0,0,0,0.15);
  z-index: 1000;
  max-width: 400px;
}

.shortcuts-help h3 {
  margin: 0 0 1rem 0;
  color: var(--color-text);
}

.shortcuts-grid {
  display: grid;
  grid-template-columns: 1fr 1fr;
  gap: 0.75rem;
}

.shortcut-item {
  display: flex;
  align-items: center;
  gap: 0.5rem;
}

.shortcut-item kbd {
  background: var(--color-background-soft);
  border: 1px solid var(--color-border);
  border-radius: 4px;
  padding: 0.25rem 0.5rem;
  font-size: 0.75rem;
  font-family: monospace;
  min-width: 2rem;
  text-align: center;
}

.shortcut-item span {
  font-size: 0.875rem;
  color: var(--color-text);
}

/* Transitions */
.task-grid-enter-active,
.task-grid-leave-active {
  transition: all 0.3s ease;
}

.task-grid-enter-from {
  opacity: 0;
  transform: translateY(-20px) scale(0.95);
}

.task-grid-leave-to {
  opacity: 0;
  transform: translateX(20px) scale(0.95);
}

.kanban-card-enter-active,
.kanban-card-leave-active {
  transition: all 0.3s ease;
}

.kanban-card-enter-from {
  opacity: 0;
  transform: translateY(-10px);
}

.kanban-card-leave-to {
  opacity: 0;
  transform: translateX(10px);
}

/* Responsive Design */
@media (max-width: 768px) {
  .board-header {
    flex-direction: column;
    align-items: stretch;
  }
  
  .board-actions {
    justify-content: center;
  }
  
  .tasks-grid {
    grid-template-columns: 1fr;
  }
  
  .quick-add-form {
    flex-direction: column;
    align-items: stretch;
  }
  
  .kanban-columns {
    flex-direction: column;
  }
  
  .kanban-column {
    width: 100%;
    max-height: 400px;
  }
  
  .shortcuts-grid {
    grid-template-columns: 1fr;
  }
}
</style>
```

### Exercise 10.2: Create Task Interaction Composables

Create the supporting composables for your interactive system:

#### `src/composables/useTaskInteraction.js`:

```javascript
import { ref, reactive, computed } from 'vue'

export function useTaskInteraction() {
  // Selection state
  const selectedTasks = ref(new Set())
  const focusedTaskId = ref(null)
  const lastSelectedTaskId = ref(null)

  // Interaction state
  const dragState = reactive({
    isDragging: false,
    draggedTask: null,
    dragStartPosition: { x: 0, y: 0 },
    currentPosition: { x: 0, y: 0 }
  })

  // Selection methods
  const toggleTaskSelection = (taskId, event = null) => {
    const newSelected = new Set(selectedTasks.value)
    
    if (event?.shiftKey && lastSelectedTaskId.value) {
      // Range selection with Shift key
      // This would require task list to implement range selection
      console.log('Range selection from', lastSelectedTaskId.value, 'to', taskId)
    } else if (event?.ctrlKey || event?.metaKey) {
      // Multi-selection with Ctrl/Cmd
      if (newSelected.has(taskId)) {
        newSelected.delete(taskId)
      } else {
        newSelected.add(taskId)
      }
    } else {
      // Single selection
      if (newSelected.has(taskId) && newSelected.size === 1) {
        newSelected.clear()
      } else {
        newSelected.clear()
        newSelected.add(taskId)
      }
    }
    
    selectedTasks.value = newSelected
    lastSelectedTaskId.value = taskId
    focusedTaskId.value = taskId
  }

  const selectAll = (taskIds) => {
    selectedTasks.value = new Set(taskIds)
  }

  const clearSelection = () => {
    selectedTasks.value.clear()
    selectedTasks.value = new Set() // Trigger reactivity
    focusedTaskId.value = null
    lastSelectedTaskId.value = null
  }

  const isTaskSelected = (taskId) => {
    return selectedTasks.value.has(taskId)
  }

  // Focus management
  const focusTask = (taskId) => {
    focusedTaskId.value = taskId
  }

  const focusNextTask = (taskIds, currentId) => {
    const currentIndex = taskIds.indexOf(currentId)
    if (currentIndex < taskIds.length - 1) {
      focusedTaskId.value = taskIds[currentIndex + 1]
    }
  }

  const focusPreviousTask = (taskIds, currentId) => {
    const currentIndex = taskIds.indexOf(currentId)
    if (currentIndex > 0) {
      focusedTaskId.value = taskIds[currentIndex - 1]
    }
  }

  // Click handling with modifier support
  const handleTaskClick = (task, event) => {
    event.preventDefault()
    toggleTaskSelection(task.id, event)
  }

  // Drag and drop support
  const startDrag = (task, event) => {
    dragState.isDragging = true
    dragState.draggedTask = task
    dragState.dragStartPosition = {
      x: event.clientX,
      y: event.clientY
    }
  }

  const updateDragPosition = (event) => {
    if (dragState.isDragging) {
      dragState.currentPosition = {
        x: event.clientX,
        y: event.clientY
      }
    }
  }

  const endDrag = () => {
    dragState.isDragging = false
    dragState.draggedTask = null
    dragState.dragStartPosition = { x: 0, y: 0 }
    dragState.currentPosition = { x: 0, y: 0 }
  }

  // Computed properties
  const hasSelection = computed(() => selectedTasks.value.size > 0)
  const selectionCount = computed(() => selectedTasks.value.size)
  const selectedTaskIds = computed(() => Array.from(selectedTasks.value))

  return {
    // State
    selectedTasks,
    focusedTaskId,
    lastSelectedTaskId,
    dragState,

    // Methods
    toggleTaskSelection,
    selectAll,
    clearSelection,
    isTaskSelected,
    focusTask,
    focusNextTask,
    focusPreviousTask,
    handleTaskClick,
    startDrag,
    updateDragPosition,
    endDrag,

    // Computed
    hasSelection,
    selectionCount,
    selectedTaskIds
  }
}
```

#### `src/composables/useKeyboardShortcuts.js`:

```javascript
import { ref, onMounted, onUnmounted } from 'vue'

export function useKeyboardShortcuts() {
  const shortcuts = ref(new Map())
  const isEnabled = ref(true)

  const parseShortcut = (shortcut) => {
    const keys = shortcut.toLowerCase().split('+')
    return {
      ctrl: keys.includes('ctrl'),
      alt: keys.includes('alt'),
      shift: keys.includes('shift'),
      meta: keys.includes('meta') || keys.includes('cmd'),
      key: keys[keys.length - 1]
    }
  }

  const matchesShortcut = (event, shortcut) => {
    const parsed = parseShortcut(shortcut)
    
    return (
      event.ctrlKey === parsed.ctrl &&
      event.altKey === parsed.alt &&
      event.shiftKey === parsed.shift &&
      event.metaKey === parsed.meta &&
      event.key.toLowerCase() === parsed.key
    )
  }

  const handleKeyDown = (event) => {
    if (!isEnabled.value) return
    
    // Don't trigger shortcuts when typing in inputs
    if (event.target.tagName === 'INPUT' || 
        event.target.tagName === 'TEXTAREA' ||
        event.target.contentEditable === 'true') {
      return
    }

    for (const [shortcut, handler] of shortcuts.value) {
      if (matchesShortcut(event, shortcut)) {
        event.preventDefault()
        handler(event)
        break
      }
    }
  }

  const registerShortcut = (shortcut, handler) => {
    shortcuts.value.set(shortcut, handler)
  }

  const unregisterShortcut = (shortcut) => {
    shortcuts.value.delete(shortcut)
  }

  const enableShortcuts = () => {
    isEnabled.value = true
  }

  const disableShortcuts = () => {
    isEnabled.value = false
  }

  const getRegisteredShortcuts = () => {
    return Array.from(shortcuts.value.keys())
  }

  onMounted(() => {
    document.addEventListener('keydown', handleKeyDown)
  })

  onUnmounted(() => {
    document.removeEventListener('keydown', handleKeyDown)
  })

  return {
    registerShortcut,
    unregisterShortcut,
    enableShortcuts,
    disableShortcuts,
    getRegisteredShortcuts,
    isEnabled
  }
}
```

### Success Criteria
- [ ] Interactive task board with multiple view modes working
- [ ] Advanced event handling with modifiers functional
- [ ] Drag and drop system operational
- [ ] Keyboard shortcuts system working
- [ ] Context menus responsive
- [ ] Multi-selection with modifiers working
- [ ] Custom events between components functional
- [ ] Accessibility features implemented

### 💡 Key Learning Points

1. **Advanced Event Patterns**: Complex event handling with modifiers and custom events
2. **User Interaction Design**: Creating intuitive interfaces with multiple interaction modes
3. **Keyboard Navigation**: Building accessible applications with comprehensive keyboard support
4. **Drag and Drop**: Implementing modern interaction patterns
5. **Component Communication**: Custom events and sophisticated data flow

### 🎯 Looking Ahead

In Chapter 11, you'll explore state management with Pinia, learning how to manage complex application state across multiple components effectively!

**Your interactive event system is complete** - experience the power of Vue.js event handling!