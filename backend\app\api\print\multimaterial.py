"""
Multi-Material Job Endpoints
=============================

This module provides REST API endpoints for managing multi-material print jobs
in the Aerosint SPD Recoater system. It handles job lifecycle, drum cache management,
and real-time status monitoring for coordinated 3-drum printing operations.

Endpoints:
- POST /cli/start-multimaterial-job: Start a multi-material print job using cached CLI files.
- GET /multimaterial-job/status: Get the current status of the active multi-material job.
- POST /multimaterial-job/cancel: Cancel the active multi-material job.
- GET /cli/drum-cache-status: Get status of cached CLI files for all drums.
- POST /cli/clear-drum-cache: Clear cached CLI files for all drums.
- POST /cli/clear-drum-cache/{drum_id}: Clear cached CLI file for a specific drum.
"""
import logging
from fastapi import APIRouter, HTTPException, Depends, Path

from app.dependencies import get_multilayer_job_manager
from app.services.job_management import MultiMaterialJobService, MultiMaterialJobError
from .models import (
    MultiMaterialJobResponse,
    MultiMaterialJobStatusResponse,
    DrumStatusResponse,
)

logger = logging.getLogger(__name__)
router = APIRouter()


@router.post("/cli/start-multimaterial-job", response_model=MultiMaterialJobResponse)
async def start_multimaterial_job(
    job_manager: MultiMaterialJobService = Depends(get_multilayer_job_manager),
) -> MultiMaterialJobResponse:
    """
    Start a multi-material print job (per-drum cached workflow only).

    Requires that one or more drums have cached CLI files uploaded via
    POST /cli/upload/{drum_id}. Begins layer-by-layer orchestration in the
    background and returns immediately.
    """
    try:
        # Require cached per-drum files
        if not job_manager.has_cached_files():
            raise HTTPException(
                status_code=400,
                detail="No CLI files cached. Upload files to cache first using /cli/upload/{drum_id}",
            )

        # Kick off the long-running job in the background so the API returns immediately
        import asyncio
        task = asyncio.create_task(job_manager.start_layer_by_layer_job())
        # Store the task reference for proper cancellation
        job_manager._background_task = task
        return MultiMaterialJobResponse(
            success=True,
            job_id="layer-by-layer-job",
            message="Layer-by-layer printing job started",
        )

    except MultiMaterialJobError as e:
        logger.error(f"Multi-material job error: {e}")
        raise HTTPException(status_code=400, detail=str(e))
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"Unexpected error starting multi-material job: {e}")
        raise HTTPException(status_code=500, detail=f"Internal error: {e}")


@router.get("/multimaterial-job/status", response_model=MultiMaterialJobStatusResponse)
async def get_multimaterial_job_status(
    job_manager: MultiMaterialJobService = Depends(get_multilayer_job_manager),
) -> MultiMaterialJobStatusResponse:
    """Get the current status of the active multi-material job."""
    try:
        status = await job_manager.get_job_status()
        return MultiMaterialJobStatusResponse(**status)
    except Exception as e:
        logger.error(f"Unexpected error getting multi-material job status: {e}")
        raise HTTPException(status_code=500, detail=f"Internal error: {e}")


@router.post("/multimaterial-job/cancel", response_model=MultiMaterialJobResponse)
async def cancel_multimaterial_job(
    job_manager: MultiMaterialJobService = Depends(get_multilayer_job_manager),
) -> MultiMaterialJobResponse:
    """Cancel the active multi-material job."""
    try:
        cancelled = await job_manager.cancel_job()
        if cancelled:
            return MultiMaterialJobResponse(success=True, message="Multi-material job cancelled successfully")
        else:
            return MultiMaterialJobResponse(success=False, message="Failed to cancel multi-material job")
    except Exception as e:
        logger.error(f"Unexpected error cancelling multi-material job: {e}")
        raise HTTPException(status_code=500, detail=f"Internal error: {e}")

@router.get("/cli/drum-cache-status")
async def get_drum_cache_status(
    job_manager: MultiMaterialJobService = Depends(get_multilayer_job_manager),
):
    """
    Get status of cached CLI files for all drums.
    Returns information about which drums have files cached and their layer counts.
    """
    try:
        cache_status = {}
        max_layers = 0

        for drum_id in [0, 1, 2]:
            drum_data = job_manager.get_cached_file_for_drum(drum_id)
            if drum_data is not None:
                cache_status[drum_id] = {
                    'filename': drum_data['filename'],
                    'layer_count': drum_data['layer_count'],
                    'cached': True
                }
                max_layers = max(max_layers, drum_data['layer_count'])
            else:
                cache_status[drum_id] = {
                    'filename': None,
                    'layer_count': 0,
                    'cached': False
                }

        return {
            'drums': cache_status,
            'max_layers': max_layers,
            'has_cached_files': job_manager.has_cached_files(),
            'ready_to_start': job_manager.has_cached_files()
        }

    except Exception as e:
        logger.error(f"Unexpected error getting drum cache status: {e}")
        raise HTTPException(status_code=500, detail=f"Internal error: {e}")


@router.post("/cli/clear-drum-cache/{drum_id}", response_model=MultiMaterialJobResponse)
async def clear_drum_cache_for_drum(
    drum_id: int = Path(..., ge=0, le=2, description="The drum's ID (0-2)"),
    job_manager: MultiMaterialJobService = Depends(get_multilayer_job_manager),
) -> MultiMaterialJobResponse:
    """
    Clear cached CLI file for a specific drum (0, 1, or 2).
    Intended for the cached multi-material workflow. This does not delete any files
    on the recoater hardware; it only clears the backend cache.
    """
    try:
        job_manager.clear_drum_cache_for_drum(drum_id)
        return MultiMaterialJobResponse(success=True, message=f"Cleared cached CLI file for drum {drum_id}")
    except ValueError as ve:
        raise HTTPException(status_code=400, detail=str(ve))
    except Exception as e:
        logger.error(f"Unexpected error clearing drum cache for drum {drum_id}: {e}")
        raise HTTPException(status_code=500, detail=f"Internal error: {e}")


@router.get("/multimaterial-job/drum-status/{drum_id}", response_model=DrumStatusResponse)
async def get_drum_status(
    drum_id: int = Path(..., ge=0, le=2, description="The drum's ID (0-2)"),
    job_manager: MultiMaterialJobService = Depends(get_multilayer_job_manager),
) -> DrumStatusResponse:
    """Get status for a specific drum in the multi-material job."""
    try:
        status = job_manager.get_drum_status(drum_id)
        if status is None:
            raise HTTPException(status_code=404, detail=f"No active job or drum {drum_id} not found")
        return DrumStatusResponse(**status)
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"Unexpected error getting drum {drum_id} status: {e}")
        raise HTTPException(status_code=500, detail=f"Internal error: {e}")



# @router.post("/multimaterial-job/clear-error", response_model=MultiMaterialJobResponse)
# async def clear_multimaterial_job_errors(
#     job_manager: MultiMaterialJobService = Depends(get_multilayer_job_manager),
# ) -> MultiMaterialJobResponse:
#     """Clear error flags for operator error recovery."""
#     try:
#         cleared = await job_manager.clear_error_flags()
#         if cleared:
#             return MultiMaterialJobResponse(success=True, message="Error flags cleared successfully")
#         else:
#             return MultiMaterialJobResponse(success=False, message="Failed to clear error flags")
#     except Exception as e:
#         logger.error(f"Unexpected error clearing error flags: {e}")
#         raise HTTPException(status_code=500, detail=f"Internal error: {e}")