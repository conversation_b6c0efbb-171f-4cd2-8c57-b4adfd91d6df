# Chapter 6: Introduction to Vue.js

## Learning Objectives
By the end of this chapter, you will understand:
- What Vue.js is and why it's powerful for industrial applications
- The difference between traditional DOM manipulation and reactive frameworks
- Vue.js 3's key features and architectural concepts
- How Vue.js fits into modern web development
- Setting up a Vue.js development environment

## What is Vue.js?

Vue.js is a **progressive JavaScript framework** for building user interfaces. Let's break this down:

### Progressive Framework
```
Traditional Development:
HTML → CSS → JavaScript (manually update DOM)
   ↓       ↓        ↓
Static   Styling  Manual Updates

Vue.js Development:
Template → Styles → Reactive Logic
    ↓        ↓          ↓
Dynamic   Scoped   Automatic Updates
```

### Real-World Analogy
Think of Vue.js like a **smart thermostat** vs a manual temperature control:

**Manual Control (Traditional JavaScript):**
- You constantly check the temperature
- Manually adjust heating/cooling
- Track all changes yourself
- Easy to miss updates or create inconsistencies

**Smart Thermostat (Vue.js):**
- Automatically monitors temperature
- Reacts to changes instantly
- Maintains desired state without manual intervention
- Prevents inconsistencies through automation

## Why Vue.js for Industrial Applications?

Our 3D printing system benefits from Vue.js because:

### 1. **Reactive Data Management**
```javascript
// Traditional JavaScript - Manual Updates
let drumStatus = 'idle'
function updateDrumDisplay() {
  document.getElementById('drum-status').textContent = drumStatus
  document.getElementById('drum-indicator').className = drumStatus
  // Easy to forget updates or miss elements!
}

// Vue.js - Automatic Updates
const drumStatus = ref('idle')
// Template automatically updates when drumStatus changes!
// No manual DOM manipulation needed
```

### 2. **Component-Based Architecture**
```
Industrial Control Panel:
┌─────────────────────────────────────┐
│  Main Control Dashboard             │
│  ┌─────────────┐  ┌─────────────┐   │
│  │ Drum Control│  │ Job Progress│   │
│  │ Component   │  │ Component   │   │
│  └─────────────┘  └─────────────┘   │
│  ┌─────────────┐  ┌─────────────┐   │
│  │ Status      │  │ Layer       │   │
│  │ Component   │  │ Preview     │   │
│  └─────────────┘  └─────────────┘   │
└─────────────────────────────────────┘

Each component is:
- Self-contained
- Reusable
- Maintainable
- Testable
```

### 3. **Real-time State Management**
For industrial systems, we need instant updates:

```javascript
// When hardware status changes:
recoaterStatus.value = 'connected'  // ← Single change
// Automatically updates:
// - Connection indicator
// - Available controls
// - Status messages
// - Button states
// - Background colors
```

## Vue.js 3 Key Features

### 1. **Composition API**
Vue.js 3 introduces a more powerful way to organize component logic:

```javascript
// Vue 2 Options API (older style)
export default {
  data() {
    return {
      drumId: 0,
      isLoading: false
    }
  },
  methods: {
    loadPreview() {
      // logic here
    }
  }
}

// Vue 3 Composition API (modern style)
export default {
  setup() {
    const drumId = ref(0)
    const isLoading = ref(false)
    
    const loadPreview = () => {
      // logic here
    }
    
    return { drumId, isLoading, loadPreview }
  }
}
```

### 2. **Reactivity System**
Vue.js tracks dependencies automatically:

```javascript
// Reactive Variables
const selectedDrum = ref(0)           // Reactive primitive
const drumConfig = reactive({         // Reactive object
  temperature: 200,
  speed: 50
})

// Computed Properties (auto-update when dependencies change)
const drumDisplayName = computed(() => {
  return `Drum ${selectedDrum.value + 1}`
})

// Watchers (run code when values change)
watch(selectedDrum, (newDrum, oldDrum) => {
  console.log(`Switched from drum ${oldDrum} to ${newDrum}`)
})
```

### 3. **Template Syntax**
Vue.js uses HTML-like templates with special directives:

```vue
<template>
  <!-- Data binding -->
  <h2>{{ drumDisplayName }}</h2>
  
  <!-- Conditional rendering -->
  <div v-if="isLoading" class="loading">
    Loading preview...
  </div>
  
  <!-- Event handling -->
  <button @click="loadPreview" :disabled="isLoading">
    Load Preview
  </button>
  
  <!-- List rendering -->
  <ul>
    <li v-for="layer in layers" :key="layer.id">
      Layer {{ layer.number }}
    </li>
  </ul>
</template>
```

## Vue.js Development Workflow

### 1. **Single File Components (.vue files)**
Everything for a component in one file:

```vue
<template>
  <!-- HTML structure -->
  <div class="drum-control">
    <h3>{{ title }}</h3>
    <button @click="toggleDrum">
      {{ drumStatus }}
    </button>
  </div>
</template>

<script>
// JavaScript logic
import { ref } from 'vue'

export default {
  name: 'DrumControl',
  setup() {
    const title = ref('Drum Controller')
    const drumStatus = ref('idle')
    
    const toggleDrum = () => {
      drumStatus.value = drumStatus.value === 'idle' ? 'active' : 'idle'
    }
    
    return { title, drumStatus, toggleDrum }
  }
}
</script>

<style scoped>
/* CSS styles (scoped to this component only) */
.drum-control {
  border: 1px solid #ccc;
  padding: 1rem;
  border-radius: 4px;
}

button {
  background: #007bff;
  color: white;
  border: none;
  padding: 0.5rem 1rem;
  border-radius: 4px;
  cursor: pointer;
}
</style>
```

### 2. **Development Tools**

#### **Vite (Build Tool)**
```
Traditional Build Process:
Source Code → Complex Bundling → Slow Reload
     ↓              ↓              ↓
   Minutes       Frustration    Lost Focus

Vite Process:
Source Code → Instant Updates → Hot Reload
     ↓             ↓             ↓
  Seconds      Smooth Flow   Maintained Focus
```

#### **Vue DevTools**
Browser extension that shows:
- Component hierarchy
- Reactive data states
- Event timeline
- Performance metrics

## Setting Up Vue.js Environment

### 1. **Prerequisites**
- Node.js (v16 or higher)
- Package manager (npm/yarn)
- Code editor (VS Code recommended)

### 2. **Project Structure**
```
frontend/
├── src/
│   ├── components/     # Reusable components
│   ├── views/         # Page-level components
│   ├── stores/        # State management
│   ├── services/      # API communication
│   └── main.js        # Application entry point
├── public/            # Static assets
└── package.json       # Dependencies
```

### 3. **Key Dependencies**
```json
{
  "dependencies": {
    "vue": "^3.3.0",        // Core Vue.js framework
    "pinia": "^2.1.0",      // State management
    "axios": "^1.4.0"       // HTTP requests
  },
  "devDependencies": {
    "vite": "^4.3.0",       // Build tool
    "@vitejs/plugin-vue": "^4.2.0"  // Vue support for Vite
  }
}
```

## Vue.js vs Traditional JavaScript

Let's see how Vue.js simplifies common tasks:

### Task: Update UI when data changes

**Traditional JavaScript:**
```javascript
// Manual DOM manipulation
let layerCount = 1
let maxLayers = 10

function updateLayerDisplay() {
  // Update multiple places manually
  document.getElementById('current-layer').textContent = layerCount
  document.getElementById('layer-input').value = layerCount
  document.getElementById('progress-bar').style.width = `${(layerCount/maxLayers)*100}%`
  
  // Update button states
  document.getElementById('prev-btn').disabled = layerCount <= 1
  document.getElementById('next-btn').disabled = layerCount >= maxLayers
}

// Call manually every time data changes
updateLayerDisplay()
```

**Vue.js:**
```javascript
// Reactive data
const layerCount = ref(1)
const maxLayers = ref(10)

// Computed properties (auto-update)
const progressPercent = computed(() => (layerCount.value / maxLayers.value) * 100)
const canGoPrev = computed(() => layerCount.value > 1)
const canGoNext = computed(() => layerCount.value < maxLayers.value)

// Template automatically updates when data changes!
```

```vue
<template>
  <div>Current Layer: {{ layerCount }}</div>
  <input v-model="layerCount" type="number">
  <div class="progress-bar" :style="{ width: progressPercent + '%' }"></div>
  <button :disabled="!canGoPrev" @click="layerCount--">Previous</button>
  <button :disabled="!canGoNext" @click="layerCount++">Next</button>
</template>
```

## Vue.js Application Lifecycle

```
Application Startup:
1. main.js creates Vue app
2. Mounts app to DOM element
3. Loads initial components
4. Establishes reactivity system

Component Lifecycle:
1. Created - component logic initialized
2. Mounted - added to DOM
3. Updated - reactive data changed
4. Unmounted - removed from DOM
```

### In Our 3D Printing Application:
```javascript
// main.js - Application entry point
import { createApp } from 'vue'
import { createPinia } from 'pinia'
import App from './App.vue'

const app = createApp(App)
app.use(createPinia())  // State management
app.mount('#app')       // Mount to DOM
```

## Industrial Application Benefits

### 1. **Reliability**
- Automatic UI updates prevent inconsistencies
- Component isolation reduces bugs
- Reactive system catches state changes

### 2. **Maintainability**
- Clear separation of concerns
- Reusable components
- Declarative templates easy to understand

### 3. **Scalability**
- Component-based architecture grows with requirements
- State management handles complex data flows
- Performance optimizations built-in

### 4. **Developer Experience**
- Hot reload for instant feedback
- DevTools for debugging
- TypeScript support for large codebases

## Practice Exercises

### Exercise 1: Basic Reactive Data
Create a simple Vue component that displays drum temperature and allows updates:

```vue
<template>
  <div class="temperature-display">
    <h3>Drum Temperature</h3>
    <p>Current: {{ temperature }}°C</p>
    <button @click="increaseTemp">+</button>
    <button @click="decreaseTemp">-</button>
  </div>
</template>

<script>
import { ref } from 'vue'

export default {
  setup() {
    const temperature = ref(200)
    
    const increaseTemp = () => {
      if (temperature.value < 300) {
        temperature.value += 10
      }
    }
    
    const decreaseTemp = () => {
      if (temperature.value > 100) {
        temperature.value -= 10
      }
    }
    
    return { temperature, increaseTemp, decreaseTemp }
  }
}
</script>
```

### Exercise 2: Computed Properties
Add a computed property that shows temperature status:

```javascript
const temperatureStatus = computed(() => {
  if (temperature.value < 150) return 'Too Cold'
  if (temperature.value > 250) return 'Too Hot'
  return 'Optimal'
})
```

### Exercise 3: Conditional Rendering
Display warnings based on temperature:

```vue
<template>
  <div v-if="temperatureStatus === 'Too Hot'" class="warning">
    ⚠️ Temperature too high!
  </div>
  <div v-else-if="temperatureStatus === 'Too Cold'" class="info">
    ❄️ Temperature too low
  </div>
  <div v-else class="success">
    ✅ Temperature optimal
  </div>
</template>
```

## Key Takeaways

1. **Vue.js is Reactive**: Data changes automatically update the UI
2. **Component-Based**: Build complex UIs from simple, reusable pieces
3. **Progressive**: Start simple, add complexity as needed
4. **Developer-Friendly**: Great tooling and documentation
5. **Industrial-Ready**: Reliable, maintainable, and scalable

## Next Steps

In the next chapter, we'll dive deeper into Vue.js **Composition API** and learn how to:
- Organize complex component logic
- Share functionality between components
- Handle lifecycle events
- Manage component state effectively

The Composition API is the foundation that makes our layer preview functionality possible!

---

## Quick Reference

### Essential Vue.js Concepts
- **Reactive Data**: `ref()`, `reactive()`
- **Computed Properties**: `computed()`
- **Event Handling**: `@click`, `@input`
- **Conditional Rendering**: `v-if`, `v-show`
- **List Rendering**: `v-for`
- **Two-way Binding**: `v-model`

### Industrial Application Patterns
- Real-time status updates
- Component-based control panels
- Reactive state management
- Automatic UI synchronization

---

## 🚀 Mini Project: Task Dashboard - Vue.js Conversion

### Exercise 6.1: Vue.js Project Setup

Convert your vanilla JavaScript Task Dashboard to Vue.js. First, set up a Vue.js development environment:

#### Step 1: Create Vue.js Project Structure

```
task-dashboard-vue/
├── index.html
├── package.json
├── vite.config.js
├── src/
│   ├── main.js
│   ├── App.vue
│   ├── assets/
│   │   └── main.css
│   ├── components/
│   │   ├── TaskForm.vue
│   │   ├── TaskList.vue
│   │   ├── TaskCard.vue
│   │   ├── TaskFilters.vue
│   │   └── AppHeader.vue
│   ├── composables/
│   │   ├── useTasks.js
│   │   ├── useTheme.js
│   │   └── useStorage.js
│   └── utils/
│       └── helpers.js
```

#### Step 2: Configure Vite for Vue.js

Create `vite.config.js`:

```javascript
import { defineConfig } from 'vite'
import vue from '@vitejs/plugin-vue'

export default defineConfig({
  plugins: [vue()],
  resolve: {
    alias: {
      '@': '/src'
    }
  },
  server: {
    port: 3000,
    open: true
  }
})
```

Create `package.json`:

```json
{
  "name": "task-dashboard-vue",
  "version": "1.0.0",
  "type": "module",
  "scripts": {
    "dev": "vite",
    "build": "vite build",
    "preview": "vite preview"
  },
  "dependencies": {
    "vue": "^3.3.0"
  },
  "devDependencies": {
    "@vitejs/plugin-vue": "^4.2.0",
    "vite": "^4.3.0"
  }
}
```

### Exercise 6.2: Create Root Vue Application

Create `src/main.js`:

```javascript
import { createApp } from 'vue'
import App from './App.vue'
import './assets/main.css'

const app = createApp(App)

app.mount('#app')
```

Create `src/App.vue`:

```vue
<template>
  <div id="app" class="app-container" :data-theme="currentTheme">
    <!-- Skip navigation for accessibility -->
    <a href="#main-content" class="skip-nav">Skip to main content</a>
    
    <!-- Application header -->
    <AppHeader 
      :stats="taskStats"
      :theme="currentTheme"
      @toggle-theme="toggleTheme"
    />

    <!-- Main application content -->
    <main id="main-content" class="main-content" role="main">
      <!-- Task creation form -->
      <TaskForm @task-created="handleTaskCreated" />
      
      <!-- Task filtering and controls -->
      <TaskFilters 
        v-model:search="filters.search"
        v-model:category="filters.category"
        v-model:status="filters.status"
        v-model:priority="filters.priority"
        @clear-completed="clearCompletedTasks"
        @export-tasks="exportTasks"
      />
      
      <!-- Progress indicator -->
      <div class="progress-container" role="progressbar" 
           :aria-valuenow="progressPercentage" 
           aria-valuemin="0" 
           aria-valuemax="100"
           aria-label="Overall task completion progress">
        <div class="progress-bar">
          <div class="progress-fill" :style="{ width: progressPercentage + '%' }"></div>
        </div>
        <span class="progress-text">{{ progressPercentage }}% Complete</span>
      </div>

      <!-- Task list display -->
      <TaskList 
        :tasks="filteredTasks"
        @task-updated="handleTaskUpdated"
        @task-deleted="handleTaskDeleted"
      />
    </main>

    <!-- Application footer -->
    <footer class="app-footer" role="contentinfo">
      <div class="footer-content">
        <p class="footer-text">
          Built with ❤️ using Vue.js 3 | 
          <button @click="openDataModal" class="link-button">
            Manage Data
          </button>
        </p>
        <div class="footer-stats">
          <span>Last updated: {{ lastUpdated }}</span>
        </div>
      </div>
    </footer>

    <!-- Data management modal -->
    <DataModal 
      v-if="showDataModal"
      @close="closeDataModal"
      @import-tasks="handleImportTasks"
      @export-tasks="exportTasks"
      @clear-all="clearAllTasks"
    />

    <!-- Notification system -->
    <NotificationContainer 
      :notifications="notifications"
      @dismiss="dismissNotification"
    />
  </div>
</template>

<script setup>
import { ref, computed, onMounted, onUnmounted } from 'vue'
import AppHeader from './components/AppHeader.vue'
import TaskForm from './components/TaskForm.vue'
import TaskFilters from './components/TaskFilters.vue'
import TaskList from './components/TaskList.vue'
import DataModal from './components/DataModal.vue'
import NotificationContainer from './components/NotificationContainer.vue'

// Composables
import { useTasks } from './composables/useTasks'
import { useTheme } from './composables/useTheme'
import { useNotifications } from './composables/useNotifications'

// Use composables
const { 
  tasks, 
  filters, 
  addTask, 
  updateTask, 
  deleteTask, 
  clearCompleted,
  clearAll,
  exportTasks: exportTasksData,
  importTasks,
  filteredTasks,
  taskStats
} = useTasks()

const { currentTheme, toggleTheme } = useTheme()
const { notifications, showNotification, dismissNotification } = useNotifications()

// Local state
const showDataModal = ref(false)
const lastUpdated = ref(new Date().toLocaleTimeString())

// Computed properties
const progressPercentage = computed(() => {
  if (tasks.value.length === 0) return 0
  const completed = tasks.value.filter(task => task.completed).length
  return Math.round((completed / tasks.value.length) * 100)
})

// Event handlers
const handleTaskCreated = (taskData) => {
  addTask(taskData)
  showNotification(`Task "${taskData.title}" created successfully!`, 'success')
  updateLastUpdated()
}

const handleTaskUpdated = (taskData) => {
  updateTask(taskData)
  const action = taskData.completed ? 'completed' : 'updated'
  showNotification(`Task "${taskData.title}" ${action}!`, 'success')
  updateLastUpdated()
}

const handleTaskDeleted = (task) => {
  deleteTask(task.id)
  showNotification(`Task "${task.title}" deleted!`, 'success')
  updateLastUpdated()
}

const clearCompletedTasks = () => {
  const completedCount = tasks.value.filter(t => t.completed).length
  if (completedCount === 0) {
    showNotification('No completed tasks to clear.', 'info')
    return
  }
  
  if (confirm(`Delete ${completedCount} completed task(s)?`)) {
    clearCompleted()
    showNotification(`${completedCount} completed tasks cleared!`, 'success')
    updateLastUpdated()
  }
}

const clearAllTasks = () => {
  if (confirm('Are you sure you want to delete ALL tasks? This cannot be undone.')) {
    if (confirm('This will permanently delete all your tasks. Are you absolutely sure?')) {
      clearAll()
      showNotification('All tasks cleared!', 'success')
      closeDataModal()
      updateLastUpdated()
    }
  }
}

const exportTasks = (format = 'json') => {
  try {
    exportTasksData(format)
    showNotification('Tasks exported successfully!', 'success')
  } catch (error) {
    showNotification('Failed to export tasks', 'error')
  }
}

const handleImportTasks = async (file) => {
  try {
    const importedCount = await importTasks(file)
    showNotification(`${importedCount} tasks imported successfully!`, 'success')
    closeDataModal()
    updateLastUpdated()
  } catch (error) {
    showNotification('Failed to import tasks. Please check the file format.', 'error')
  }
}

const openDataModal = () => {
  showDataModal.value = true
}

const closeDataModal = () => {
  showDataModal.value = false
}

const updateLastUpdated = () => {
  lastUpdated.value = new Date().toLocaleTimeString()
}

// Keyboard shortcuts
const handleKeyboardShortcuts = (e) => {
  // Escape: Close modal
  if (e.key === 'Escape' && showDataModal.value) {
    closeDataModal()
  }
  
  // Ctrl/Cmd + K: Focus search
  if ((e.ctrlKey || e.metaKey) && e.key === 'k') {
    e.preventDefault()
    const searchInput = document.querySelector('#task-search')
    if (searchInput) {
      searchInput.focus()
    }
  }
}

// Lifecycle
onMounted(() => {
  document.addEventListener('keydown', handleKeyboardShortcuts)
  updateLastUpdated()
})

onUnmounted(() => {
  document.removeEventListener('keydown', handleKeyboardShortcuts)
})
</script>

<style>
/* Global styles from your CSS file will be imported in main.js */
</style>
```

### Exercise 6.3: Create Vue.js Composables

Create `src/composables/useTasks.js` - the heart of your reactive task management:

```javascript
import { ref, reactive, computed, watch } from 'vue'
import { useStorage } from './useStorage'
import { generateId, formatDate } from '../utils/helpers'

export function useTasks() {
  // Reactive state
  const tasks = ref([])
  const filters = reactive({
    search: '',
    category: '',
    status: '',
    priority: ''
  })

  const categories = ref([
    { id: 'work', name: 'Work', emoji: '🏢', color: '#007bff' },
    { id: 'personal', name: 'Personal', emoji: '🏠', color: '#28a745' },
    { id: 'learning', name: 'Learning', emoji: '📚', color: '#ffc107' },
    { id: 'health', name: 'Health', emoji: '💪', color: '#dc3545' }
  ])

  // Storage composable
  const { save, load } = useStorage('taskDashboard')

  // Computed properties
  const filteredTasks = computed(() => {
    return tasks.value.filter(task => {
      // Search filter
      if (filters.search) {
        const searchTerm = filters.search.toLowerCase()
        const matchesSearch = 
          task.title.toLowerCase().includes(searchTerm) ||
          (task.description && task.description.toLowerCase().includes(searchTerm))
        
        if (!matchesSearch) return false
      }
      
      // Category filter
      if (filters.category && task.category !== filters.category) {
        return false
      }
      
      // Status filter
      if (filters.status) {
        if (filters.status === 'completed' && !task.completed) return false
        if (filters.status === 'pending' && task.completed) return false
        if (filters.status === 'overdue' && !isTaskOverdue(task)) return false
      }
      
      // Priority filter
      if (filters.priority && task.priority !== filters.priority) {
        return false
      }
      
      return true
    })
  })

  const taskStats = computed(() => {
    const total = tasks.value.length
    const completed = tasks.value.filter(t => t.completed).length
    const percentage = total > 0 ? Math.round((completed / total) * 100) : 0
    
    return {
      total,
      completed,
      pending: total - completed,
      percentage
    }
  })

  // Helper functions
  const isTaskOverdue = (task) => {
    if (!task.dueDate || task.completed) return false
    
    const dueDate = new Date(task.dueDate)
    const today = new Date()
    today.setHours(23, 59, 59, 999)
    
    return dueDate < today
  }

  const validateTask = (taskData) => {
    const errors = []
    
    if (!taskData.title || taskData.title.trim().length === 0) {
      errors.push('Task title is required')
    }
    
    if (taskData.title && taskData.title.length < 3) {
      errors.push('Task title must be at least 3 characters')
    }
    
    if (taskData.dueDate) {
      const dueDate = new Date(taskData.dueDate)
      const today = new Date()
      today.setHours(0, 0, 0, 0)
      
      if (dueDate < today) {
        errors.push('Due date cannot be in the past')
      }
    }
    
    return errors
  }

  // Task operations
  const addTask = (taskData) => {
    const errors = validateTask(taskData)
    if (errors.length > 0) {
      throw new Error(errors.join(', '))
    }

    const newTask = {
      id: generateId(),
      title: taskData.title.trim(),
      description: taskData.description ? taskData.description.trim() : '',
      category: taskData.category || '',
      priority: taskData.priority || 'medium',
      dueDate: taskData.dueDate || null,
      completed: false,
      createdAt: new Date().toISOString(),
      completedAt: null
    }

    tasks.value.unshift(newTask)
    saveToStorage()
    return newTask
  }

  const updateTask = (updatedTask) => {
    const index = tasks.value.findIndex(t => t.id === updatedTask.id)
    if (index === -1) {
      throw new Error('Task not found')
    }

    // If toggling completion status
    if (tasks.value[index].completed !== updatedTask.completed) {
      updatedTask.completedAt = updatedTask.completed 
        ? new Date().toISOString() 
        : null
    }

    tasks.value[index] = { ...tasks.value[index], ...updatedTask }
    saveToStorage()
    return tasks.value[index]
  }

  const deleteTask = (taskId) => {
    const index = tasks.value.findIndex(t => t.id === taskId)
    if (index === -1) {
      throw new Error('Task not found')
    }

    const deletedTask = tasks.value.splice(index, 1)[0]
    saveToStorage()
    return deletedTask
  }

  const clearCompleted = () => {
    const completedTasks = tasks.value.filter(t => t.completed)
    tasks.value = tasks.value.filter(t => !t.completed)
    saveToStorage()
    return completedTasks.length
  }

  const clearAll = () => {
    const taskCount = tasks.value.length
    tasks.value = []
    saveToStorage()
    return taskCount
  }

  // Import/Export
  const exportTasks = (format = 'json') => {
    const data = {
      tasks: tasks.value,
      categories: categories.value,
      exportedAt: new Date().toISOString(),
      version: '1.0'
    }
    
    let content, filename, mimeType
    
    if (format === 'json') {
      content = JSON.stringify(data, null, 2)
      filename = `tasks-${formatDate(new Date(), 'filename')}.json`
      mimeType = 'application/json'
    } else {
      content = tasksToText()
      filename = `tasks-${formatDate(new Date(), 'filename')}.txt`
      mimeType = 'text/plain'
    }
    
    downloadFile(content, filename, mimeType)
  }

  const importTasks = async (file) => {
    if (!file) {
      throw new Error('No file provided')
    }

    const text = await readFile(file)
    const data = JSON.parse(text)
    
    if (!data.tasks || !Array.isArray(data.tasks)) {
      throw new Error('Invalid file format')
    }
    
    const validTasks = data.tasks.filter(task => isValidTask(task))
    
    if (validTasks.length === 0) {
      throw new Error('No valid tasks found in file')
    }
    
    // Assign new IDs to avoid conflicts
    const importedTasks = validTasks.map(task => ({
      ...task,
      id: generateId(),
      createdAt: task.createdAt || new Date().toISOString()
    }))
    
    tasks.value.push(...importedTasks)
    saveToStorage()
    
    return importedTasks.length
  }

  // Storage operations
  const saveToStorage = () => {
    const data = {
      tasks: tasks.value,
      lastUpdated: new Date().toISOString()
    }
    save(data)
  }

  const loadFromStorage = () => {
    const data = load()
    if (data && data.tasks) {
      tasks.value = data.tasks
    } else {
      // Load sample data if no saved data
      loadSampleData()
    }
  }

  const loadSampleData = () => {
    const sampleTasks = [
      {
        id: generateId(),
        title: "Complete Task Dashboard Vue.js Conversion",
        description: "Convert the vanilla JavaScript task dashboard to use Vue.js 3 with Composition API",
        category: "learning",
        priority: "high",
        dueDate: "2024-02-01",
        completed: false,
        createdAt: new Date().toISOString(),
        completedAt: null
      },
      {
        id: generateId(),
        title: "Practice Vue.js Reactivity",
        description: "Understand reactive data binding and computed properties",
        category: "learning",
        priority: "medium",
        dueDate: "2024-01-25",
        completed: true,
        createdAt: new Date(Date.now() - 86400000).toISOString(),
        completedAt: new Date().toISOString()
      }
    ]
    
    tasks.value = sampleTasks
    saveToStorage()
  }

  // Utility functions
  const tasksToText = () => {
    let text = 'TASK DASHBOARD EXPORT\n'
    text += '===================\n\n'
    
    tasks.value.forEach((task, index) => {
      text += `${index + 1}. ${task.title}\n`
      if (task.description) {
        text += `   Description: ${task.description}\n`
      }
      text += `   Status: ${task.completed ? 'Completed' : 'Pending'}\n`
      text += `   Priority: ${task.priority}\n`
      if (task.category) {
        const category = categories.value.find(c => c.id === task.category)
        text += `   Category: ${category ? category.name : task.category}\n`
      }
      if (task.dueDate) {
        text += `   Due Date: ${formatDate(task.dueDate)}\n`
      }
      text += `   Created: ${formatDate(task.createdAt)}\n`
      text += '\n'
    })
    
    return text
  }

  const isValidTask = (task) => {
    return task && 
           typeof task.title === 'string' && 
           task.title.length > 0 &&
           typeof task.completed === 'boolean'
  }

  const readFile = (file) => {
    return new Promise((resolve, reject) => {
      const reader = new FileReader()
      reader.onload = e => resolve(e.target.result)
      reader.onerror = reject
      reader.readAsText(file)
    })
  }

  const downloadFile = (content, filename, mimeType) => {
    const blob = new Blob([content], { type: mimeType })
    const url = URL.createObjectURL(blob)
    const link = document.createElement('a')
    link.href = url
    link.download = filename
    document.body.appendChild(link)
    link.click()
    document.body.removeChild(link)
    URL.revokeObjectURL(url)
  }

  // Auto-save when tasks change
  watch(tasks, saveToStorage, { deep: true })

  // Initialize
  loadFromStorage()

  // Return the composable API
  return {
    // Reactive state
    tasks,
    filters,
    categories,
    
    // Computed properties
    filteredTasks,
    taskStats,
    
    // Actions
    addTask,
    updateTask,
    deleteTask,
    clearCompleted,
    clearAll,
    exportTasks,
    importTasks,
    
    // Utilities
    isTaskOverdue,
    validateTask
  }
}
```

### Exercise 6.4: Create Theme Composable

Create `src/composables/useTheme.js`:

```javascript
import { ref, watch } from 'vue'
import { useStorage } from './useStorage'

export function useTheme() {
  const currentTheme = ref('light')
  const { save, load } = useStorage('theme')

  // Initialize theme
  const initializeTheme = () => {
    // Try to load saved theme
    const savedTheme = load()
    if (savedTheme) {
      currentTheme.value = savedTheme
    } else {
      // Check system preference
      const prefersDark = window.matchMedia('(prefers-color-scheme: dark)').matches
      currentTheme.value = prefersDark ? 'dark' : 'light'
    }
    
    applyTheme(currentTheme.value)
  }

  const toggleTheme = () => {
    currentTheme.value = currentTheme.value === 'light' ? 'dark' : 'light'
  }

  const setTheme = (theme) => {
    if (['light', 'dark'].includes(theme)) {
      currentTheme.value = theme
    }
  }

  const applyTheme = (theme) => {
    document.documentElement.setAttribute('data-theme', theme)
  }

  // Watch for theme changes and apply them
  watch(currentTheme, (newTheme) => {
    applyTheme(newTheme)
    save(newTheme)
  })

  // Listen for system theme changes
  const mediaQuery = window.matchMedia('(prefers-color-scheme: dark)')
  mediaQuery.addEventListener('change', (e) => {
    if (!load()) { // Only auto-change if no saved preference
      currentTheme.value = e.matches ? 'dark' : 'light'
    }
  })

  // Initialize theme on composable creation
  initializeTheme()

  return {
    currentTheme,
    toggleTheme,
    setTheme
  }
}
```

### Exercise 6.5: Create Storage Composable

Create `src/composables/useStorage.js`:

```javascript
import { ref } from 'vue'

export function useStorage(key, defaultValue = null) {
  const storedValue = ref(defaultValue)

  const save = (value) => {
    try {
      storedValue.value = value
      localStorage.setItem(key, JSON.stringify(value))
      return true
    } catch (error) {
      console.error(`Error saving to localStorage (${key}):`, error)
      return false
    }
  }

  const load = () => {
    try {
      const item = localStorage.getItem(key)
      if (item) {
        const parsed = JSON.parse(item)
        storedValue.value = parsed
        return parsed
      }
      return defaultValue
    } catch (error) {
      console.error(`Error loading from localStorage (${key}):`, error)
      return defaultValue
    }
  }

  const remove = () => {
    try {
      localStorage.removeItem(key)
      storedValue.value = defaultValue
      return true
    } catch (error) {
      console.error(`Error removing from localStorage (${key}):`, error)
      return false
    }
  }

  const clear = () => {
    try {
      localStorage.clear()
      storedValue.value = defaultValue
      return true
    } catch (error) {
      console.error('Error clearing localStorage:', error)
      return false
    }
  }

  return {
    storedValue,
    save,
    load,
    remove,
    clear
  }
}
```

### Exercise 6.6: Create Notification Composable

Create `src/composables/useNotifications.js`:

```javascript
import { ref } from 'vue'

export function useNotifications() {
  const notifications = ref([])
  let nextId = 1

  const showNotification = (message, type = 'info', duration = 5000) => {
    const notification = {
      id: nextId++,
      message,
      type,
      timestamp: Date.now()
    }

    notifications.value.push(notification)

    // Auto-dismiss after duration
    if (duration > 0) {
      setTimeout(() => {
        dismissNotification(notification.id)
      }, duration)
    }

    return notification.id
  }

  const dismissNotification = (id) => {
    const index = notifications.value.findIndex(n => n.id === id)
    if (index > -1) {
      notifications.value.splice(index, 1)
    }
  }

  const clearAllNotifications = () => {
    notifications.value = []
  }

  return {
    notifications,
    showNotification,
    dismissNotification,
    clearAllNotifications
  }
}
```

### Success Criteria
- [ ] Vue.js project structure created
- [ ] Main App.vue component implemented
- [ ] Task management composable created
- [ ] Theme management composable working
- [ ] Storage composable functional
- [ ] Notification system implemented
- [ ] Reactive data binding operational
- [ ] Component communication established

### 💡 Key Learning Points

1. **Reactive Programming**: Understanding how Vue.js automatically updates the UI
2. **Composition API**: Organizing logic in reusable composable functions
3. **Component Architecture**: Breaking complex UI into manageable pieces
4. **State Management**: Managing application data reactively
5. **Vue.js Ecosystem**: Using Vite for development and build tooling

### 🎯 Looking Ahead

In Chapter 7, you'll dive deeper into the Composition API, learning advanced patterns for component logic organization and reusability. The foundation you've built here sets the stage for more sophisticated Vue.js development patterns.

**Your Vue.js conversion is complete** - experience the power of reactive programming!