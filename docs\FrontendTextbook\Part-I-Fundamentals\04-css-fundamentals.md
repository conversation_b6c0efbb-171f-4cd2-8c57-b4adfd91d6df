# Chapter 4: CSS Fundamentals
## Industrial Interface Design and Styling

```
╔══════════════════════════════════════════════════════════════════════════════════════╗
║                            CHAPTER 4: CSS FUNDAMENTALS                               ║
║                       Professional Styling for Vue.js Apps                           ║
╚══════════════════════════════════════════════════════════════════════════════════════╝
```

## Chapter Objectives

By the end of this chapter, you will understand:
- CSS architecture patterns in Vue.js applications
- Industrial design principles for professional interfaces
- Modern layout techniques (Flexbox, CSS Grid)
- Responsive design for various screen sizes
- CSS variables and theming systems
- Performance optimization for large applications

## CSS Architecture in Vue.js Applications

### Scoped vs Global Styles

Vue.js provides powerful CSS organization through scoped styles:

```
┌─────────────────────────────────────────────────────────────────────────────────────┐
│                              CSS SCOPE ARCHITECTURE                                │
├─────────────────────────────────────────────────────────────────────────────────────┤
│                                                                                     │
│ Global Styles              Component Styles           Utility Classes              │
│      │                           │                           │                     │
│      ▼                           ▼                           ▼                     │
│ ┌─────────────┐            ┌─────────────┐             ┌─────────────┐             │
│ │ reset.css   │            │ <style      │             │ utilities   │             │
│ │ variables   │            │ scoped>     │             │ .text-sm    │             │
│ │ typography  │            │             │             │ .mt-4       │             │
│ │ base colors │            │ Component-  │             │ .flex       │             │
│ │             │            │ specific    │             │ .grid       │             │
│ │ Affects     │            │ styles only │             │             │             │
│ │ entire app  │            │             │             │ Reusable    │             │
│ └─────────────┘            └─────────────┘             └─────────────┘             │
│                                                                                     │
│ Used for:                  Used for:                   Used for:                   │
│ • Brand colors             • Component layout          • Common patterns           │
│ • Typography scale         • State-specific styles     • Spacing helpers          │
│ • Layout primitives        • Animations               • Responsive utilities      │
│                                                                                     │
└─────────────────────────────────────────────────────────────────────────────────────┘
```

**Traditional CSS (Global Scope Problems):**
```css
/* global.css - affects entire application */
.btn {
  padding: 10px;
  background: blue;
}

.form-input {
  border: 1px solid #ccc;
}

/* Problem: These styles affect ALL buttons and inputs */
/* Risk: Styling conflicts between components */
```

**Vue.js Scoped CSS (Component Isolation):**
```vue
<!-- PrintView.vue -->
<template>
  <div class="layer-preview">
    <button class="btn btn-primary">Load Preview</button>
    <input class="form-input" type="number">
  </div>
</template>

<style scoped>
/* These styles ONLY apply to this component */
.layer-preview {
  padding: 20px;
  background: white;
  border-radius: 8px;
}

.btn {
  padding: 12px 24px;
  border: none;
  border-radius: 6px;
  font-weight: 600;
  cursor: pointer;
  transition: all 0.3s ease;
}

.btn-primary {
  background-color: var(--primary-color);
  color: white;
}

.btn-primary:hover {
  background-color: var(--primary-color-dark);
  transform: translateY(-1px);
  box-shadow: 0 4px 8px rgba(0, 123, 255, 0.3);
}

.btn:disabled {
  opacity: 0.6;
  cursor: not-allowed;
  transform: none;
}

.form-input {
  width: 100%;
  padding: 10px 12px;
  border: 2px solid var(--border-color);
  border-radius: 4px;
  font-size: 14px;
  transition: border-color 0.3s ease;
}

.form-input:focus {
  outline: none;
  border-color: var(--primary-color);
  box-shadow: 0 0 0 3px rgba(0, 123, 255, 0.1);
}

.form-input:invalid {
  border-color: var(--danger-color);
}
</style>
```

## 📦 CSS Box Model for Precise Control

Understanding the box model is crucial for industrial interfaces that require pixel-perfect layouts:

```
┌─────────────────────────────────────────────────────────────────────────────────────┐
│                                CSS BOX MODEL                                        │
├─────────────────────────────────────────────────────────────────────────────────────┤
│                                                                                     │
│                               MARGIN                                                │
│  ┌──────────────────────────────────────────────────────────────────────────────┐   │
│  │                             BORDER                                           │   │
│  │  ┌──────────────────────────────────────────────────────────────────────┐    │   │
│  │  │                           PADDING                                    │    │   │
│  │  │  ┌─────────────────────────────────────────────────────────────┐     │    │   │
│  │  │  │                     CONTENT                                 │     │    │   │
│  │  │  │               (width × height)                              │     │    │   │
│  │  │  │                                                             │     │    │   │
│  │  │  │  ┌─────────────────────────────────────────────────────┐    │     │    │   │
│  │  │  │  │            Actual Content Area                      │    │     │    │   │
│  │  │  │  │         • Text                                      │    │     │    │   │
│  │  │  │  │         • Images                                    │    │     │    │   │
│  │  │  │  │         • Other elements                            │    │     │    │   │
│  │  │  │  └─────────────────────────────────────────────────────┘    │     │    │   │
│  │  │  └─────────────────────────────────────────────────────────────┘     │    │   │
│  │  └──────────────────────────────────────────────────────────────────────┘    │   │
│  └──────────────────────────────────────────────────────────────────────────────┘   │
│                                                                                     │
└─────────────────────────────────────────────────────────────────────────────────────┘
```

**Applied to Our Industrial Controls:**
```css
.control-panel {
  /* Content dimensions */
  width: 300px;
  height: auto;
  
  /* Padding: Space inside the element */
  padding: 20px 24px;  /* 20px top/bottom, 24px left/right */
  
  /* Border: Visual boundary */
  border: 2px solid var(--panel-border-color);
  border-radius: 8px;
  
  /* Margin: Space outside the element */
  margin: 16px 0;  /* 16px top/bottom, 0 left/right */
  
  /* Box sizing: Include padding and border in width calculation */
  box-sizing: border-box;
}

.form-group {
  /* Consistent spacing between form elements */
  margin-bottom: 1.5rem;
  
  /* Flexbox for label/input alignment */
  display: flex;
  flex-direction: column;
  gap: 0.5rem;
}

.form-label {
  font-weight: 600;
  color: var(--label-color);
  font-size: 0.875rem;
  margin-bottom: 0.25rem;
}

.form-input {
  /* Precise sizing for industrial consistency */
  width: 100%;
  min-height: 44px;  /* Touch-friendly minimum */
  padding: 0.75rem 1rem;
  
  /* Visual hierarchy */
  border: 2px solid var(--input-border-color);
  background-color: var(--input-background-color);
  
  /* Typography */
  font-family: var(--font-family-mono);  /* Monospace for numbers */
  font-size: 1rem;
  line-height: 1.5;
}
```

## 📐 Modern Layout with Flexbox

Flexbox is essential for creating responsive industrial interfaces:

```css
/* Main container: Flexible layout */
.preview-controls {
  display: flex;
  flex-direction: row;
  flex-wrap: wrap;
  gap: 1rem;
  align-items: flex-start;
  justify-content: space-between;
  
  /* Responsive behavior */
  @media (max-width: 768px) {
    flex-direction: column;
    align-items: stretch;
  }
}

/* Form groups: Flexible items */
.form-group {
  display: flex;
  flex-direction: column;
  flex: 0 0 auto;  /* Don't grow, don't shrink, auto basis */
  min-width: 200px;
  gap: 0.5rem;
}

/* Button group: Always at the end */
.button-group {
  display: flex;
  gap: 0.75rem;
  margin-left: auto;  /* Push to the right */
  
  @media (max-width: 768px) {
    margin-left: 0;
    justify-content: stretch;
  }
}

/* Specific flexbox patterns for our layer preview */
.layer-input-container {
  display: flex;
  align-items: center;
  gap: 1rem;
}

.layer-input {
  flex: 0 0 120px;  /* Fixed width for number input */
}

.range-display {
  flex: 1;  /* Take remaining space */
  font-size: 0.875rem;
  color: var(--text-muted);
}
```

**Flexbox Visual Guide:**
```
┌─────────────────────────────────────────────────────────────────────────────────────┐
│                              FLEXBOX CONTAINER                                     │
│  flex-direction: row, justify-content: space-between                               │
├─────────────────────────────────────────────────────────────────────────────────────┤
│                                                                                     │
│  ┌─────────────┐     ┌─────────────┐     ┌─────────────┐     ┌─────────────┐       │
│  │ Drum Select │     │ Layer Input │     │             │     │ Load Button │       │
│  │             │     │ (conditional│     │             │     │             │       │
│  │ flex: 0 0   │     │  rendering) │     │   AUTO      │     │ margin-left │       │
│  │ auto        │     │             │     │   SPACE     │     │ auto        │       │
│  │             │     │ flex: 0 0   │     │             │     │             │       │
│  │             │     │ auto        │     │             │     │             │       │
│  └─────────────┘     └─────────────┘     └─────────────┘     └─────────────┘       │
│                                                                                     │
└─────────────────────────────────────────────────────────────────────────────────────┘
```

## 🎨 CSS Variables and Design Systems

Professional applications require consistent theming:

```css
/* root-level variables for consistency */
:root {
  /* Color System */
  --primary-color: #007bff;
  --primary-color-dark: #0056b3;
  --primary-color-light: #66b3ff;
  
  --secondary-color: #6c757d;
  --success-color: #28a745;
  --warning-color: #ffc107;
  --danger-color: #dc3545;
  --info-color: #17a2b8;
  
  /* Neutral Colors */
  --gray-50: #f8f9fa;
  --gray-100: #e9ecef;
  --gray-200: #dee2e6;
  --gray-300: #ced4da;
  --gray-400: #adb5bd;
  --gray-500: #6c757d;
  --gray-600: #495057;
  --gray-700: #343a40;
  --gray-800: #212529;
  --gray-900: #000000;
  
  /* Typography */
  --font-family-sans: 'Inter', system-ui, -apple-system, sans-serif;
  --font-family-mono: 'JetBrains Mono', 'Fira Code', monospace;
  
  --font-size-xs: 0.75rem;   /* 12px */
  --font-size-sm: 0.875rem;  /* 14px */
  --font-size-base: 1rem;    /* 16px */
  --font-size-lg: 1.125rem;  /* 18px */
  --font-size-xl: 1.25rem;   /* 20px */
  
  --font-weight-normal: 400;
  --font-weight-medium: 500;
  --font-weight-semibold: 600;
  --font-weight-bold: 700;
  
  /* Spacing Scale */
  --space-xs: 0.25rem;   /* 4px */
  --space-sm: 0.5rem;    /* 8px */
  --space-md: 1rem;      /* 16px */
  --space-lg: 1.5rem;    /* 24px */
  --space-xl: 2rem;      /* 32px */
  --space-2xl: 3rem;     /* 48px */
  
  /* Border Radius */
  --radius-sm: 0.25rem;  /* 4px */
  --radius-md: 0.5rem;   /* 8px */
  --radius-lg: 0.75rem;  /* 12px */
  
  /* Shadows */
  --shadow-sm: 0 1px 2px rgba(0, 0, 0, 0.05);
  --shadow-md: 0 4px 6px rgba(0, 0, 0, 0.1);
  --shadow-lg: 0 10px 15px rgba(0, 0, 0, 0.1);
  
  /* Transitions */
  --transition-fast: 150ms ease;
  --transition-base: 250ms ease;
  --transition-slow: 350ms ease;
}

/* Industrial theme variant */
[data-theme="industrial"] {
  --primary-color: #ff6b35;
  --background-color: var(--gray-50);
  --text-color: var(--gray-800);
  --border-color: var(--gray-300);
}

/* Dark theme variant */
[data-theme="dark"] {
  --primary-color: #66b3ff;
  --background-color: var(--gray-900);
  --text-color: var(--gray-100);
  --border-color: var(--gray-600);
}
```

### Vue.js Integration with CSS Variables

```vue
<template>
  <div 
    class="layer-preview" 
    :style="{
      '--dynamic-progress': progressPercentage + '%',
      '--status-color': statusColor,
      '--connection-state': connectionOpacity
    }"
  >
    <div class="progress-bar"></div>
    <div class="status-indicator"></div>
    <button class="connection-button">Connect</button>
  </div>
</template>

<script setup>
import { ref, computed } from 'vue'

const progressPercentage = ref(45)
const isConnected = ref(true)
const currentStatus = ref('loading')

// Computed CSS values
const statusColor = computed(() => {
  switch (currentStatus.value) {
    case 'success': return 'var(--success-color)'
    case 'error': return 'var(--danger-color)'
    case 'loading': return 'var(--warning-color)'
    default: return 'var(--gray-400)'
  }
})

const connectionOpacity = computed(() => isConnected.value ? '1' : '0.5')
</script>

<style scoped>
.layer-preview {
  background: var(--background-color);
  border: 1px solid var(--border-color);
  border-radius: var(--radius-md);
  padding: var(--space-lg);
}

.progress-bar {
  width: 100%;
  height: 8px;
  background: var(--gray-200);
  border-radius: var(--radius-sm);
  position: relative;
  overflow: hidden;
}

.progress-bar::after {
  content: '';
  position: absolute;
  left: 0;
  top: 0;
  height: 100%;
  width: var(--dynamic-progress);
  background: var(--primary-color);
  transition: width var(--transition-base);
}

.status-indicator {
  width: 12px;
  height: 12px;
  border-radius: 50%;
  background: var(--status-color);
  transition: background-color var(--transition-base);
}

.connection-button {
  opacity: var(--connection-state);
  transition: opacity var(--transition-base);
}
</style>
```

## 📱 Responsive Design for Industrial Applications

Industrial applications often run on various devices and screen sizes:

```css
/* Mobile-first approach */
.print-view {
  padding: var(--space-sm);
}

.print-controls {
  flex-direction: column;
  gap: var(--space-sm);
}

/* Tablet and larger screens */
@media (min-width: 768px) {
  .print-view {
    padding: var(--space-lg);
  }
  
  .print-controls {
    flex-direction: row;
    gap: var(--space-md);
  }
}

/* Desktop and larger screens */
@media (min-width: 1024px) {
  .print-view {
    max-width: 1200px;
    margin: 0 auto;
  }
  
  .form-group {
    min-width: 200px;
  }
}

/* Extra large screens (industrial monitors) */
@media (min-width: 1440px) {
  :root {
    --font-size-base: 16px; /* Larger text for better readability */
  }
  
  .print-controls {
    gap: var(--space-lg);
  }
}

/* High DPI displays */
@media (-webkit-min-device-pixel-ratio: 2), (min-resolution: 192dpi) {
  .status-indicator {
    border: 0.5px solid var(--border-color); /* Sharper borders */
  }
}

/* Print styles */
@media print {
  .print-controls {
    display: none; /* Hide interactive elements */
  }
  
  .preview-image {
    max-width: 100%;
    break-inside: avoid;
  }
}
```

## 🎛️ Component-Specific Styling Patterns

### Button Component System

```css
/* Base button styles */
.btn {
  display: inline-flex;
  align-items: center;
  justify-content: center;
  gap: var(--space-sm);
  
  padding: var(--space-sm) var(--space-md);
  border: 1px solid transparent;
  border-radius: var(--radius-md);
  
  font-family: var(--font-family-sans);
  font-size: var(--font-size-sm);
  font-weight: var(--font-weight-medium);
  line-height: 1.5;
  text-decoration: none;
  
  cursor: pointer;
  transition: all var(--transition-fast);
  
  /* Focus management */
  &:focus {
    outline: 2px solid var(--primary-color);
    outline-offset: 2px;
  }
  
  /* Disabled state */
  &:disabled {
    opacity: 0.6;
    cursor: not-allowed;
    transform: none !important;
  }
}

/* Button variants */
.btn-primary {
  background-color: var(--primary-color);
  color: white;
  
  &:hover:not(:disabled) {
    background-color: var(--primary-color-dark);
    transform: translateY(-1px);
    box-shadow: var(--shadow-md);
  }
}

.btn-secondary {
  background-color: transparent;
  color: var(--primary-color);
  border-color: var(--primary-color);
  
  &:hover:not(:disabled) {
    background-color: var(--primary-color);
    color: white;
  }
}

.btn-danger {
  background-color: var(--danger-color);
  color: white;
  
  &:hover:not(:disabled) {
    background-color: #c82333;
  }
}

/* Button sizes */
.btn-sm {
  padding: var(--space-xs) var(--space-sm);
  font-size: var(--font-size-xs);
}

.btn-lg {
  padding: var(--space-md) var(--space-lg);
  font-size: var(--font-size-lg);
}

/* Loading state */
.btn-loading {
  position: relative;
  color: transparent;
  
  &::after {
    content: '';
    position: absolute;
    width: 16px;
    height: 16px;
    border: 2px solid currentColor;
    border-radius: 50%;
    border-top-color: transparent;
    animation: btn-loading-spin 1s linear infinite;
  }
}

@keyframes btn-loading-spin {
  to { transform: rotate(360deg); }
}
```

### Form Input System

```css
/* Base input styles */
.form-input {
  width: 100%;
  padding: var(--space-sm) var(--space-md);
  
  border: 2px solid var(--gray-300);
  border-radius: var(--radius-md);
  background-color: white;
  
  font-family: var(--font-family-sans);
  font-size: var(--font-size-base);
  line-height: 1.5;
  
  transition: border-color var(--transition-fast), box-shadow var(--transition-fast);
  
  /* Focus state */
  &:focus {
    outline: none;
    border-color: var(--primary-color);
    box-shadow: 0 0 0 3px rgba(0, 123, 255, 0.1);
  }
  
  /* Error state */
  &:invalid,
  &.error {
    border-color: var(--danger-color);
    
    &:focus {
      box-shadow: 0 0 0 3px rgba(220, 53, 69, 0.1);
    }
  }
  
  /* Success state */
  &.success {
    border-color: var(--success-color);
  }
  
  /* Disabled state */
  &:disabled {
    background-color: var(--gray-100);
    border-color: var(--gray-200);
    color: var(--gray-500);
    cursor: not-allowed;
  }
}

/* Input variants */
.form-input-sm {
  padding: var(--space-xs) var(--space-sm);
  font-size: var(--font-size-sm);
}

.form-input-lg {
  padding: var(--space-md) var(--space-lg);
  font-size: var(--font-size-lg);
}

/* Number inputs for industrial applications */
.form-input[type="number"] {
  font-family: var(--font-family-mono);
  text-align: right;
}

/* Select inputs */
.form-select {
  @extend .form-input;
  cursor: pointer;
  
  &:not([multiple]) {
    background-image: url("data:image/svg+xml,..."); /* Dropdown arrow */
    background-position: right var(--space-sm) center;
    background-repeat: no-repeat;
    background-size: 16px;
    padding-right: calc(var(--space-md) + 24px);
  }
}
```

## 🎓 Performance and Optimization

### CSS Performance Best Practices

```css
/* Use CSS custom properties for repeated values */
:root {
  --border-radius: 8px;
  --transition-duration: 0.3s;
}

/* Avoid expensive properties in animations */
.smooth-animation {
  /* Good: Use transform and opacity */
  transform: translateX(0);
  opacity: 1;
  transition: transform var(--transition-duration), opacity var(--transition-duration);
}

.smooth-animation.hidden {
  transform: translateX(-100%);
  opacity: 0;
}

/* Use will-change for optimization */
.animated-element {
  will-change: transform;
}

/* Remove will-change after animation */
.animation-complete {
  will-change: auto;
}

/* Use containment for performance */
.isolated-component {
  contain: layout style paint;
}

/* Efficient selectors */
.btn-primary { /* Good: Class selector */ }
button.btn { /* Okay: Less specific */ }
div > button.btn { /* Avoid: Too specific */ }
```

### Critical CSS and Code Splitting

```css
/* Critical CSS (above-the-fold) */
.header,
.navigation,
.main-content {
  /* Essential styles for initial render */
}

/* Non-critical CSS (can be loaded asynchronously) */
.modal,
.tooltip,
.dropdown {
  /* Styles for interactive elements */
}
```

## 🎯 Real-World Example: Complete Component Styling

Here's how our layer preview component looks
with all the CSS patterns we've learned:

```vue
<template>
  <div class="layer-preview-container">
    <!-- Header Section -->
    <header class="preview-header">
      <h2 class="preview-title">Layer Preview System</h2>
      <div class="connection-status" :class="{ connected: isConnected, disconnected: !isConnected }">
        <span class="status-indicator"></span>
        <span class="status-text">{{ isConnected ? 'Connected' : 'Disconnected' }}</span>
      </div>
    </header>

    <!-- Main Controls -->
    <main class="preview-controls">
      <!-- Drum Selection -->
      <section class="control-section">
        <h3 class="section-title">Drum Selection</h3>
        <div class="drum-grid">
          <button
            v-for="drum in drums"
            :key="drum.id"
            :class="['drum-button', { active: selectedDrum === drum.id }]"
            @click="selectDrum(drum.id)"
            :disabled="!isConnected"
          >
            <span class="drum-icon">{{ drum.icon }}</span>
            <span class="drum-label">{{ drum.name }}</span>
            <span class="drum-status" :class="drum.status">{{ drum.status }}</span>
          </button>
        </div>
      </section>

      <!-- Layer Controls -->
      <section v-if="selectedDrum !== null" class="control-section">
        <h3 class="section-title">Layer Navigation</h3>
        <div class="layer-controls">
          <div class="layer-input-group">
            <label for="layer-input" class="input-label">Layer Number:</label>
            <input
              id="layer-input"
              v-model.number="selectedLayer"
              type="number"
              :min="1"
              :max="maxLayers"
              class="layer-input"
              :disabled="isLoading"
            >
            <span class="layer-range">/ {{ maxLayers }}</span>
          </div>

          <div class="layer-navigation">
            <button
              @click="previousLayer"
              :disabled="selectedLayer <= 1 || isLoading"
              class="nav-button prev-button"
            >
              <span class="button-icon">⬅</span>
              Previous
            </button>

            <button
              @click="nextLayer"
              :disabled="selectedLayer >= maxLayers || isLoading"
              class="nav-button next-button"
            >
              Next
              <span class="button-icon">➡</span>
            </button>
          </div>
        </div>
      </section>

      <!-- Action Buttons -->
      <section class="control-section">
        <div class="action-buttons">
          <button
            @click="loadPreview"
            :disabled="!canLoadPreview"
            :class="['action-button primary-button', { loading: isLoading }]"
          >
            <span v-if="isLoading" class="loading-spinner"></span>
            <span class="button-text">{{ isLoading ? 'Loading...' : 'Load Preview' }}</span>
          </button>

          <button
            @click="clearPreview"
            :disabled="isLoading"
            class="action-button secondary-button"
          >
            Clear
          </button>
        </div>
      </section>
    </main>

    <!-- Preview Display -->
    <aside class="preview-display">
      <div v-if="isLoading" class="loading-placeholder">
        <div class="loading-spinner large"></div>
        <p>Loading preview...</p>
      </div>

      <div v-else-if="previewError" class="error-placeholder">
        <div class="error-icon">⚠️</div>
        <h3>Preview Error</h3>
        <p>{{ previewError }}</p>
        <button @click="retryLoad" class="retry-button">Retry</button>
      </div>

      <div v-else-if="previewImageUrl" class="preview-content">
        <img
          :src="previewImageUrl"
          :alt="`Layer ${selectedLayer} preview`"
          class="preview-image"
          @load="onImageLoad"
          @error="onImageError"
        >
        <div class="preview-info">
          <span class="layer-info">Layer {{ selectedLayer }} of {{ maxLayers }}</span>
          <span class="drum-info">{{ getSelectedDrumName() }}</span>
        </div>
      </div>

      <div v-else class="empty-placeholder">
        <div class="empty-icon">🖼️</div>
        <h3>No Preview Available</h3>
        <p>Select a drum and layer to load a preview</p>
      </div>
    </aside>
  </div>
</template>
```

```css
/* ===========================================
   LAYER PREVIEW COMPONENT STYLES
   =========================================== */

/* Container and Layout */
.layer-preview-container {
  display: grid;
  grid-template-columns: 1fr 2fr;
  grid-template-rows: auto 1fr;
  gap: var(--space-lg);
  padding: var(--space-xl);
  background: var(--background-color);
  border-radius: var(--radius-lg);
  box-shadow: var(--shadow-lg);
  min-height: 600px;
}

/* Header Section */
.preview-header {
  grid-column: 1 / -1;
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding-bottom: var(--space-lg);
  border-bottom: 2px solid var(--border-color);
}

.preview-title {
  margin: 0;
  font-size: var(--font-size-xl);
  font-weight: var(--font-weight-bold);
  color: var(--text-color);
}

.connection-status {
  display: flex;
  align-items: center;
  gap: var(--space-sm);
  padding: var(--space-sm) var(--space-md);
  border-radius: var(--radius-md);
  font-weight: var(--font-weight-medium);
}

.connection-status.connected {
  background-color: var(--success-bg);
  color: var(--success-color);
}

.connection-status.disconnected {
  background-color: var(--danger-bg);
  color: var(--danger-color);
}

.status-indicator {
  width: 8px;
  height: 8px;
  border-radius: 50%;
  background-color: currentColor;
}

/* Main Controls */
.preview-controls {
  display: flex;
  flex-direction: column;
  gap: var(--space-lg);
}

.control-section {
  background: var(--card-background);
  padding: var(--space-lg);
  border-radius: var(--radius-md);
  border: 1px solid var(--border-color);
}

.section-title {
  margin: 0 0 var(--space-md) 0;
  font-size: var(--font-size-lg);
  font-weight: var(--font-weight-semibold);
  color: var(--text-color);
}

/* Drum Selection */
.drum-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(120px, 1fr));
  gap: var(--space-sm);
}

.drum-button {
  display: flex;
  flex-direction: column;
  align-items: center;
  gap: var(--space-xs);
  padding: var(--space-md);
  border: 2px solid var(--border-color);
  border-radius: var(--radius-md);
  background: var(--button-bg);
  color: var(--button-text);
  cursor: pointer;
  transition: all var(--transition-fast);
  font-size: var(--font-size-sm);
}

.drum-button:hover:not(:disabled) {
  border-color: var(--primary-color);
  background: var(--primary-bg-light);
  transform: translateY(-2px);
  box-shadow: var(--shadow-md);
}

.drum-button.active {
  border-color: var(--primary-color);
  background: var(--primary-bg);
  color: var(--primary-text);
  box-shadow: var(--shadow-md);
}

.drum-button:disabled {
  opacity: 0.6;
  cursor: not-allowed;
  transform: none;
}

.drum-icon {
  font-size: var(--font-size-xl);
  margin-bottom: var(--space-xs);
}

.drum-label {
  font-weight: var(--font-weight-medium);
  text-align: center;
}

.drum-status {
  font-size: var(--font-size-xs);
  padding: 2px 6px;
  border-radius: var(--radius-sm);
  text-transform: uppercase;
  font-weight: var(--font-weight-semibold);
}

.drum-status.idle {
  background: var(--gray-200);
  color: var(--gray-700);
}

.drum-status.running {
  background: var(--success-bg);
  color: var(--success-color);
}

.drum-status.error {
  background: var(--danger-bg);
  color: var(--danger-color);
}

/* Layer Controls */
.layer-controls {
  display: flex;
  flex-direction: column;
  gap: var(--space-md);
}

.layer-input-group {
  display: flex;
  align-items: center;
  gap: var(--space-sm);
}

.input-label {
  font-weight: var(--font-weight-medium);
  color: var(--label-color);
  min-width: 100px;
}

.layer-input {
  flex: 1;
  padding: var(--space-sm) var(--space-md);
  border: 2px solid var(--input-border);
  border-radius: var(--radius-md);
  background: var(--input-bg);
  color: var(--input-text);
  font-family: var(--font-family-mono);
  font-size: var(--font-size-base);
  transition: border-color var(--transition-fast);
}

.layer-input:focus {
  outline: none;
  border-color: var(--primary-color);
  box-shadow: 0 0 0 3px var(--primary-focus-ring);
}

.layer-input:disabled {
  background: var(--input-disabled-bg);
  color: var(--input-disabled-text);
  cursor: not-allowed;
}

.layer-range {
  font-size: var(--font-size-sm);
  color: var(--text-muted);
  font-family: var(--font-family-mono);
}

.layer-navigation {
  display: flex;
  gap: var(--space-sm);
}

.nav-button {
  flex: 1;
  display: flex;
  align-items: center;
  justify-content: center;
  gap: var(--space-sm);
  padding: var(--space-sm) var(--space-md);
  border: 2px solid var(--border-color);
  border-radius: var(--radius-md);
  background: var(--button-bg);
  color: var(--button-text);
  cursor: pointer;
  transition: all var(--transition-fast);
  font-weight: var(--font-weight-medium);
}

.nav-button:hover:not(:disabled) {
  border-color: var(--primary-color);
  background: var(--primary-bg-light);
}

.nav-button:disabled {
  opacity: 0.5;
  cursor: not-allowed;
  transform: none;
}

.button-icon {
  font-size: var(--font-size-lg);
}

/* Action Buttons */
.action-buttons {
  display: flex;
  flex-direction: column;
  gap: var(--space-sm);
}

.action-button {
  display: flex;
  align-items: center;
  justify-content: center;
  gap: var(--space-sm);
  padding: var(--space-md) var(--space-lg);
  border: none;
  border-radius: var(--radius-md);
  font-size: var(--font-size-base);
  font-weight: var(--font-weight-semibold);
  cursor: pointer;
  transition: all var(--transition-fast);
  position: relative;
}

.primary-button {
  background: var(--primary-color);
  color: var(--primary-text);
}

.primary-button:hover:not(:disabled) {
  background: var(--primary-hover);
  transform: translateY(-1px);
  box-shadow: var(--shadow-md);
}

.secondary-button {
  background: var(--secondary-bg);
  color: var(--secondary-text);
  border: 2px solid var(--border-color);
}

.secondary-button:hover:not(:disabled) {
  background: var(--secondary-hover);
  border-color: var(--secondary-border-hover);
}

.action-button:disabled {
  opacity: 0.6;
  cursor: not-allowed;
  transform: none;
  box-shadow: none;
}

.action-button.loading {
  color: transparent;
}

.loading-spinner {
  position: absolute;
  width: 20px;
  height: 20px;
  border: 2px solid currentColor;
  border-top-color: transparent;
  border-radius: 50%;
  animation: spin 1s linear infinite;
}

.loading-spinner.large {
  width: 32px;
  height: 32px;
  border-width: 3px;
}

@keyframes spin {
  to { transform: rotate(360deg); }
}

/* Preview Display */
.preview-display {
  background: var(--card-background);
  border: 1px solid var(--border-color);
  border-radius: var(--radius-md);
  padding: var(--space-lg);
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  min-height: 400px;
  position: relative;
}

.loading-placeholder,
.error-placeholder,
.empty-placeholder {
  display: flex;
  flex-direction: column;
  align-items: center;
  gap: var(--space-md);
  text-align: center;
  color: var(--text-muted);
}

.empty-icon,
.error-icon {
  font-size: 48px;
  opacity: 0.5;
}

.preview-content {
  width: 100%;
  height: 100%;
  display: flex;
  flex-direction: column;
  align-items: center;
  gap: var(--space-md);
}

.preview-image {
  max-width: 100%;
  max-height: 300px;
  border-radius: var(--radius-md);
  box-shadow: var(--shadow-md);
  object-fit: contain;
}

.preview-info {
  display: flex;
  justify-content: space-between;
  width: 100%;
  padding: var(--space-sm) var(--space-md);
  background: var(--info-bg);
  border-radius: var(--radius-sm);
  font-size: var(--font-size-sm);
  color: var(--info-text);
}

.layer-info,
.drum-info {
  font-weight: var(--font-weight-medium);
}

.retry-button {
  padding: var(--space-sm) var(--space-md);
  background: var(--primary-color);
  color: var(--primary-text);
  border: none;
  border-radius: var(--radius-sm);
  cursor: pointer;
  font-weight: var(--font-weight-medium);
  transition: background-color var(--transition-fast);
}

.retry-button:hover {
  background: var(--primary-hover);
}

/* ===========================================
   RESPONSIVE DESIGN
   =========================================== */

@media (max-width: 1024px) {
  .layer-preview-container {
    grid-template-columns: 1fr;
    grid-template-rows: auto auto 1fr;
    gap: var(--space-md);
  }

  .drum-grid {
    grid-template-columns: repeat(auto-fit, minmax(100px, 1fr));
  }

  .layer-navigation {
    flex-direction: column;
  }

  .nav-button {
    justify-content: flex-start;
  }
}

@media (max-width: 768px) {
  .layer-preview-container {
    padding: var(--space-md);
    gap: var(--space-sm);
  }

  .preview-header {
    flex-direction: column;
    gap: var(--space-sm);
    align-items: flex-start;
  }

  .drum-grid {
    grid-template-columns: repeat(2, 1fr);
  }

  .action-buttons {
    flex-direction: row;
  }

  .preview-display {
    min-height: 300px;
  }

  .preview-image {
    max-height: 200px;
  }
}

@media (max-width: 480px) {
  .drum-grid {
    grid-template-columns: 1fr;
  }

  .layer-input-group {
    flex-direction: column;
    align-items: flex-start;
    gap: var(--space-xs);
  }

  .input-label {
    min-width: auto;
  }

  .action-buttons {
    flex-direction: column;
  }
}

/* ===========================================
   DARK THEME SUPPORT
   =========================================== */

[data-theme="dark"] .layer-preview-container {
  --background-color: var(--gray-900);
  --card-background: var(--gray-800);
  --text-color: var(--gray-100);
  --text-muted: var(--gray-400);
  --border-color: var(--gray-700);
  --button-bg: var(--gray-700);
  --button-text: var(--gray-100);
  --input-bg: var(--gray-800);
  --input-text: var(--gray-100);
  --input-border: var(--gray-600);
  --primary-bg: var(--primary-color);
  --primary-text: white;
  --primary-bg-light: rgba(0, 123, 255, 0.1);
  --secondary-bg: var(--gray-700);
  --secondary-text: var(--gray-100);
  --success-bg: rgba(40, 167, 69, 0.1);
  --success-color: var(--success-color);
  --danger-bg: rgba(220, 53, 69, 0.1);
  --danger-color: var(--danger-color);
  --info-bg: var(--gray-700);
  --info-text: var(--gray-200);
}

/* ===========================================
   HIGH CONTRAST MODE
   =========================================== */

@media (prefers-contrast: high) {
  .drum-button,
  .nav-button,
  .action-button {
    border-width: 3px;
  }

  .layer-input {
    border-width: 3px;
  }

  .status-indicator {
    width: 12px;
    height: 12px;
  }
}

/* ===========================================
   REDUCED MOTION
   =========================================== */

@media (prefers-reduced-motion: reduce) {
  .drum-button,
  .nav-button,
  .action-button,
  .retry-button {
    transition: none;
    transform: none;
  }

  .loading-spinner {
    animation: none;
  }

  .drum-button:hover,
  .nav-button:hover,
  .action-button:hover {
    transform: none;
  }
}

/* ===========================================
   PRINT STYLES
   =========================================== */

@media print {
  .layer-preview-container {
    box-shadow: none;
    border: 1px solid #000;
  }

  .drum-button,
  .nav-button,
  .action-button {
    border: 1px solid #000;
    background: white;
    color: black;
  }

  .preview-controls {
    break-inside: avoid;
  }
}
```

## 🎓 Key Takeaways

### CSS Best Practices for Industrial Applications

1. **Consistent Design System**: Use CSS variables for colors, spacing, and typography
2. **Responsive Design**: Support various screen sizes and devices
3. **Accessibility**: Consider contrast, focus states, and reduced motion
4. **Performance**: Use efficient selectors and avoid layout thrashing
5. **Maintainability**: Organize styles logically and use semantic class names

### Industrial UI Considerations

1. **Clarity**: Use clear visual hierarchy and readable fonts
2. **Reliability**: Consistent interaction patterns users can depend on
3. **Efficiency**: Minimize clicks and cognitive load
4. **Safety**: Clear error states and confirmation patterns
5. **Professional**: Clean, modern appearance that inspires confidence

## 🚀 What's Next

Now that you understand CSS fundamentals and industrial styling patterns, we'll move on to modern JavaScript features that power Vue.js applications.

**Chapter 5: JavaScript Fundamentals** will cover:
- ES6+ features used in Vue.js development
- Async/await patterns for API integration
- Modern JavaScript best practices

---

**Continue your learning journey** → [Chapter 5: JavaScript Fundamentals](05-javascript-fundamentals.md)

## 💡 Practice Exercises

1. **Create a responsive card component** with hover effects and proper accessibility
2. **Implement a loading state system** with different spinner styles
3. **Design an error state pattern** that works across different components
4. **Build a form styling system** with validation states and focus management

*Mastering CSS is essential for creating professional industrial interfaces. The patterns you've learned here will serve you well in building reliable, user-friendly applications.*

---

## 🚀 Mini Project: Task Dashboard - Professional CSS Implementation

### Exercise 4.1: Create CSS Architecture

Implement a complete CSS system for your Task Dashboard. Create `styles/main.css`:

```css
/* ===========================================
   CSS CUSTOM PROPERTIES (DESIGN SYSTEM)
   =========================================== */

:root {
  /* Colors - Light Theme */
  --color-primary: #007bff;
  --color-primary-dark: #0056b3;
  --color-primary-light: #66b3ff;
  --color-secondary: #6c757d;
  --color-success: #28a745;
  --color-warning: #ffc107;
  --color-danger: #dc3545;
  --color-info: #17a2b8;
  
  /* Neutral Colors */
  --color-white: #ffffff;
  --color-gray-50: #f8f9fa;
  --color-gray-100: #e9ecef;
  --color-gray-200: #dee2e6;
  --color-gray-300: #ced4da;
  --color-gray-400: #adb5bd;
  --color-gray-500: #6c757d;
  --color-gray-600: #495057;
  --color-gray-700: #343a40;
  --color-gray-800: #212529;
  --color-gray-900: #000000;
  
  /* Background Colors */
  --bg-primary: var(--color-white);
  --bg-secondary: var(--color-gray-50);
  --bg-card: var(--color-white);
  --bg-input: var(--color-white);
  
  /* Text Colors */
  --text-primary: var(--color-gray-800);
  --text-secondary: var(--color-gray-600);
  --text-muted: var(--color-gray-500);
  --text-inverse: var(--color-white);
  
  /* Border Colors */
  --border-color: var(--color-gray-300);
  --border-color-light: var(--color-gray-200);
  --border-color-dark: var(--color-gray-400);
  
  /* Typography */
  --font-family-sans: 'Inter', system-ui, -apple-system, BlinkMacSystemFont, sans-serif;
  --font-family-mono: 'JetBrains Mono', 'Fira Code', Consolas, monospace;
  
  --font-size-xs: 0.75rem;    /* 12px */
  --font-size-sm: 0.875rem;   /* 14px */
  --font-size-base: 1rem;     /* 16px */
  --font-size-lg: 1.125rem;   /* 18px */
  --font-size-xl: 1.25rem;    /* 20px */
  --font-size-2xl: 1.5rem;    /* 24px */
  --font-size-3xl: 1.875rem;  /* 30px */
  
  --font-weight-normal: 400;
  --font-weight-medium: 500;
  --font-weight-semibold: 600;
  --font-weight-bold: 700;
  
  --line-height-tight: 1.25;
  --line-height-normal: 1.5;
  --line-height-relaxed: 1.75;
  
  /* Spacing */
  --space-xs: 0.25rem;   /* 4px */
  --space-sm: 0.5rem;    /* 8px */
  --space-md: 1rem;      /* 16px */
  --space-lg: 1.5rem;    /* 24px */
  --space-xl: 2rem;      /* 32px */
  --space-2xl: 3rem;     /* 48px */
  --space-3xl: 4rem;     /* 64px */
  
  /* Border Radius */
  --radius-sm: 0.25rem;   /* 4px */
  --radius-md: 0.5rem;    /* 8px */
  --radius-lg: 0.75rem;   /* 12px */
  --radius-xl: 1rem;      /* 16px */
  
  /* Shadows */
  --shadow-sm: 0 1px 2px rgba(0, 0, 0, 0.05);
  --shadow-md: 0 4px 6px rgba(0, 0, 0, 0.1);
  --shadow-lg: 0 10px 15px rgba(0, 0, 0, 0.1);
  --shadow-xl: 0 20px 25px rgba(0, 0, 0, 0.15);
  
  /* Transitions */
  --transition-fast: 150ms ease;
  --transition-normal: 250ms ease;
  --transition-slow: 350ms ease;
  
  /* Z-index Scale */
  --z-dropdown: 1000;
  --z-sticky: 1020;
  --z-fixed: 1030;
  --z-modal: 1040;
  --z-tooltip: 1070;
}

/* Dark Theme */
[data-theme="dark"] {
  --bg-primary: var(--color-gray-900);
  --bg-secondary: var(--color-gray-800);
  --bg-card: var(--color-gray-800);
  --bg-input: var(--color-gray-700);
  
  --text-primary: var(--color-gray-100);
  --text-secondary: var(--color-gray-300);
  --text-muted: var(--color-gray-400);
  
  --border-color: var(--color-gray-600);
  --border-color-light: var(--color-gray-700);
  --border-color-dark: var(--color-gray-500);
}

/* ===========================================
   RESET AND BASE STYLES
   =========================================== */

*,
*::before,
*::after {
  box-sizing: border-box;
}

* {
  margin: 0;
  padding: 0;
}

html {
  font-size: 100%;
  scroll-behavior: smooth;
  -webkit-text-size-adjust: 100%;
}

body {
  font-family: var(--font-family-sans);
  font-size: var(--font-size-base);
  line-height: var(--line-height-normal);
  color: var(--text-primary);
  background-color: var(--bg-primary);
  transition: background-color var(--transition-normal), color var(--transition-normal);
}

/* ===========================================
   ACCESSIBILITY UTILITIES
   =========================================== */

.visually-hidden {
  position: absolute !important;
  width: 1px !important;
  height: 1px !important;
  padding: 0 !important;
  margin: -1px !important;
  overflow: hidden !important;
  clip: rect(0, 0, 0, 0) !important;
  white-space: nowrap !important;
  border: 0 !important;
}

.skip-nav {
  position: absolute;
  top: -40px;
  left: 6px;
  background: var(--color-primary);
  color: var(--text-inverse);
  padding: var(--space-sm) var(--space-md);
  text-decoration: none;
  border-radius: var(--radius-md);
  z-index: var(--z-modal);
  transition: top var(--transition-fast);
}

.skip-nav:focus {
  top: 6px;
}

/* Focus styles for accessibility */
:focus {
  outline: 2px solid var(--color-primary);
  outline-offset: 2px;
}

:focus:not(:focus-visible) {
  outline: none;
}

/* ===========================================
   LAYOUT COMPONENTS
   =========================================== */

.app-container {
  min-height: 100vh;
  display: flex;
  flex-direction: column;
  background-color: var(--bg-secondary);
}

/* Header */
.app-header {
  background: var(--bg-card);
  border-bottom: 1px solid var(--border-color);
  padding: var(--space-lg) var(--space-xl);
  box-shadow: var(--shadow-sm);
  position: sticky;
  top: 0;
  z-index: var(--z-sticky);
}

.header-content {
  max-width: 1200px;
  margin: 0 auto;
  display: flex;
  align-items: center;
  justify-content: space-between;
  gap: var(--space-lg);
}

.app-title {
  font-size: var(--font-size-2xl);
  font-weight: var(--font-weight-bold);
  color: var(--text-primary);
  display: flex;
  align-items: center;
  gap: var(--space-sm);
  margin: 0;
}

.app-icon {
  font-size: var(--font-size-3xl);
}

/* Statistics */
.stats-container {
  display: flex;
  gap: var(--space-lg);
}

.stat-item {
  text-align: center;
  min-width: 80px;
}

.stat-value {
  display: block;
  font-size: var(--font-size-xl);
  font-weight: var(--font-weight-bold);
  color: var(--color-primary);
  line-height: var(--line-height-tight);
}

.stat-label {
  display: block;
  font-size: var(--font-size-sm);
  color: var(--text-secondary);
  margin-top: var(--space-xs);
}

/* Theme Toggle */
.theme-toggle {
  background: none;
  border: 2px solid var(--border-color);
  border-radius: var(--radius-lg);
  padding: var(--space-sm);
  cursor: pointer;
  transition: all var(--transition-fast);
  position: relative;
  width: 48px;
  height: 48px;
  display: flex;
  align-items: center;
  justify-content: center;
}

.theme-toggle:hover {
  border-color: var(--color-primary);
  background: var(--color-primary);
}

.theme-toggle:hover .theme-icon {
  transform: scale(1.1);
}

.theme-icon {
  font-size: var(--font-size-lg);
  transition: transform var(--transition-fast);
}

[data-theme="light"] .dark-icon {
  display: none;
}

[data-theme="dark"] .light-icon {
  display: none;
}

/* Main Content */
.main-content {
  flex: 1;
  max-width: 1200px;
  margin: 0 auto;
  padding: var(--space-xl);
  width: 100%;
}

/* Sections */
.section-title {
  font-size: var(--font-size-xl);
  font-weight: var(--font-weight-semibold);
  color: var(--text-primary);
  margin-bottom: var(--space-lg);
}

/* ===========================================
   FORM COMPONENTS
   =========================================== */

.task-form-section {
  background: var(--bg-card);
  border-radius: var(--radius-lg);
  padding: var(--space-xl);
  margin-bottom: var(--space-xl);
  box-shadow: var(--shadow-md);
  border: 1px solid var(--border-color);
}

.task-form {
  display: flex;
  flex-direction: column;
  gap: var(--space-lg);
}

.form-group {
  display: flex;
  flex-direction: column;
  gap: var(--space-sm);
}

.form-row {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
  gap: var(--space-lg);
}

.form-label {
  font-weight: var(--font-weight-medium);
  color: var(--text-primary);
  font-size: var(--font-size-sm);
}

.required {
  color: var(--color-danger);
  margin-left: var(--space-xs);
}

/* Form Inputs */
.form-input,
.form-select,
.form-textarea {
  width: 100%;
  padding: var(--space-sm) var(--space-md);
  border: 2px solid var(--border-color);
  border-radius: var(--radius-md);
  background: var(--bg-input);
  color: var(--text-primary);
  font-size: var(--font-size-base);
  font-family: inherit;
  transition: border-color var(--transition-fast), box-shadow var(--transition-fast);
}

.form-input:focus,
.form-select:focus,
.form-textarea:focus {
  outline: none;
  border-color: var(--color-primary);
  box-shadow: 0 0 0 3px rgba(0, 123, 255, 0.1);
}

.form-input:invalid,
.form-select:invalid,
.form-textarea:invalid {
  border-color: var(--color-danger);
}

.form-input:invalid:focus,
.form-select:invalid:focus,
.form-textarea:invalid:focus {
  box-shadow: 0 0 0 3px rgba(220, 53, 69, 0.1);
}

.form-textarea {
  resize: vertical;
  min-height: 80px;
}

.help-text {
  font-size: var(--font-size-sm);
  color: var(--text-muted);
}

.error-message {
  font-size: var(--font-size-sm);
  color: var(--color-danger);
  font-weight: var(--font-weight-medium);
}

/* ===========================================
   BUTTON COMPONENTS
   =========================================== */

.btn {
  display: inline-flex;
  align-items: center;
  justify-content: center;
  gap: var(--space-sm);
  padding: var(--space-sm) var(--space-lg);
  border: 2px solid transparent;
  border-radius: var(--radius-md);
  font-family: inherit;
  font-size: var(--font-size-base);
  font-weight: var(--font-weight-medium);
  text-decoration: none;
  cursor: pointer;
  transition: all var(--transition-fast);
  min-height: 44px; /* Touch-friendly minimum */
}

.btn:disabled {
  opacity: 0.6;
  cursor: not-allowed;
  transform: none !important;
}

.btn-primary {
  background: var(--color-primary);
  color: var(--text-inverse);
  border-color: var(--color-primary);
}

.btn-primary:hover:not(:disabled) {
  background: var(--color-primary-dark);
  border-color: var(--color-primary-dark);
  transform: translateY(-1px);
  box-shadow: var(--shadow-md);
}

.btn-secondary {
  background: var(--color-secondary);
  color: var(--text-inverse);
  border-color: var(--color-secondary);
}

.btn-secondary:hover:not(:disabled) {
  background: #5a6268;
  border-color: #5a6268;
  transform: translateY(-1px);
}

.btn-outline {
  background: transparent;
  color: var(--color-primary);
  border-color: var(--border-color);
}

.btn-outline:hover:not(:disabled) {
  background: var(--color-primary);
  color: var(--text-inverse);
  border-color: var(--color-primary);
}

.btn-danger {
  background: var(--color-danger);
  color: var(--text-inverse);
  border-color: var(--color-danger);
}

.btn-danger:hover:not(:disabled) {
  background: #c82333;
  border-color: #c82333;
}

.form-actions {
  display: flex;
  gap: var(--space-md);
  justify-content: flex-start;
}

.btn-icon {
  font-size: var(--font-size-lg);
}

/* ===========================================
   TASK COMPONENTS
   =========================================== */

.task-controls-section {
  background: var(--bg-card);
  border-radius: var(--radius-lg);
  padding: var(--space-xl);
  margin-bottom: var(--space-xl);
  box-shadow: var(--shadow-md);
  border: 1px solid var(--border-color);
}

.controls-container {
  display: flex;
  flex-wrap: wrap;
  gap: var(--space-lg);
  align-items: center;
  justify-content: space-between;
}

.search-group {
  flex: 1;
  min-width: 250px;
}

.search-input {
  width: 100%;
  padding: var(--space-sm) var(--space-lg);
  border: 2px solid var(--border-color);
  border-radius: var(--radius-lg);
  background: var(--bg-input);
  font-size: var(--font-size-base);
  transition: border-color var(--transition-fast);
}

.search-input:focus {
  outline: none;
  border-color: var(--color-primary);
}

.filter-group {
  display: flex;
  gap: var(--space-md);
  flex-wrap: wrap;
}

.filter-item {
  display: flex;
  align-items: center;
  gap: var(--space-sm);
}

.filter-label {
  font-size: var(--font-size-sm);
  font-weight: var(--font-weight-medium);
  color: var(--text-secondary);
  white-space: nowrap;
}

.filter-select {
  padding: var(--space-xs) var(--space-sm);
  border: 1px solid var(--border-color);
  border-radius: var(--radius-sm);
  background: var(--bg-input);
  font-size: var(--font-size-sm);
}

.view-controls {
  display: flex;
  gap: var(--space-sm);
}

/* Progress Bar */
.progress-container {
  margin-bottom: var(--space-xl);
  display: flex;
  align-items: center;
  gap: var(--space-md);
}

.progress-bar {
  flex: 1;
  height: 8px;
  background: var(--color-gray-200);
  border-radius: var(--radius-lg);
  overflow: hidden;
}

.progress-fill {
  height: 100%;
  background: linear-gradient(90deg, var(--color-success), var(--color-primary));
  border-radius: var(--radius-lg);
  transition: width var(--transition-slow);
}

.progress-text {
  font-size: var(--font-size-sm);
  font-weight: var(--font-weight-medium);
  color: var(--text-secondary);
  min-width: 80px;
  text-align: right;
}

/* Task List */
.task-list {
  display: flex;
  flex-direction: column;
  gap: var(--space-md);
}

/* Empty State */
.empty-state {
  text-align: center;
  padding: var(--space-3xl) var(--space-xl);
  color: var(--text-muted);
}

.empty-icon {
  font-size: 4rem;
  margin-bottom: var(--space-lg);
  opacity: 0.5;
}

.empty-title {
  font-size: var(--font-size-xl);
  font-weight: var(--font-weight-semibold);
  margin-bottom: var(--space-sm);
  color: var(--text-secondary);
}

.empty-description {
  font-size: var(--font-size-base);
  max-width: 400px;
  margin: 0 auto;
  line-height: var(--line-height-relaxed);
}

/* Task Cards */
.task-card {
  background: var(--bg-card);
  border: 1px solid var(--border-color);
  border-radius: var(--radius-lg);
  padding: var(--space-lg);
  box-shadow: var(--shadow-sm);
  transition: all var(--transition-fast);
  position: relative;
}

.task-card:hover {
  box-shadow: var(--shadow-md);
  transform: translateY(-1px);
}

.task-card.completed {
  opacity: 0.7;
  background: var(--color-gray-50);
}

.task-header {
  display: flex;
  align-items: flex-start;
  gap: var(--space-md);
  margin-bottom: var(--space-md);
}

.task-checkbox {
  width: 20px;
  height: 20px;
  margin-top: 2px;
  flex-shrink: 0;
}

.task-title {
  flex: 1;
  font-size: var(--font-size-lg);
  font-weight: var(--font-weight-semibold);
  color: var(--text-primary);
  line-height: var(--line-height-tight);
  margin: 0;
}

.task-card.completed .task-title {
  text-decoration: line-through;
  color: var(--text-muted);
}

.task-priority {
  font-size: var(--font-size-lg);
  flex-shrink: 0;
}

.task-body {
  margin-left: 32px; /* Align with title */
}

.task-description {
  font-size: var(--font-size-base);
  color: var(--text-secondary);
  line-height: var(--line-height-relaxed);
  margin-bottom: var(--space-md);
}

.task-meta {
  display: flex;
  gap: var(--space-md);
  flex-wrap: wrap;
  align-items: center;
}

.task-category {
  font-size: var(--font-size-sm);
  font-weight: var(--font-weight-medium);
  padding: var(--space-xs) var(--space-sm);
  border-radius: var(--radius-sm);
  background: var(--color-gray-100);
  color: var(--text-primary);
}

.category-work { background: #e3f2fd; color: #1565c0; }
.category-personal { background: #e8f5e8; color: #2e7d32; }
.category-learning { background: #fff3e0; color: #ef6c00; }
.category-health { background: #fce4ec; color: #c2185b; }

.task-due-date {
  font-size: var(--font-size-sm);
  color: var(--text-muted);
}

.task-due-date.overdue {
  color: var(--color-danger);
  font-weight: var(--font-weight-medium);
}

.task-actions {
  position: absolute;
  top: var(--space-lg);
  right: var(--space-lg);
  display: flex;
  gap: var(--space-xs);
  opacity: 0;
  transition: opacity var(--transition-fast);
}

.task-card:hover .task-actions {
  opacity: 1;
}

.task-action-btn {
  width: 32px;
  height: 32px;
  border: none;
  border-radius: var(--radius-sm);
  background: var(--color-gray-100);
  color: var(--text-secondary);
  cursor: pointer;
  display: flex;
  align-items: center;
  justify-content: center;
  transition: all var(--transition-fast);
}

.task-action-btn:hover {
  background: var(--color-primary);
  color: var(--text-inverse);
  transform: scale(1.1);
}

.edit-btn:hover {
  background: var(--color-info);
}

.delete-btn:hover {
  background: var(--color-danger);
}

/* ===========================================
   FOOTER
   =========================================== */

.app-footer {
  background: var(--bg-card);
  border-top: 1px solid var(--border-color);
  padding: var(--space-lg) var(--space-xl);
  margin-top: auto;
}

.footer-content {
  max-width: 1200px;
  margin: 0 auto;
  display: flex;
  justify-content: space-between;
  align-items: center;
  gap: var(--space-lg);
  font-size: var(--font-size-sm);
  color: var(--text-muted);
}

.link-button {
  background: none;
  border: none;
  color: var(--color-primary);
  text-decoration: underline;
  cursor: pointer;
  font-size: inherit;
  padding: 0;
}

.link-button:hover {
  color: var(--color-primary-dark);
}

/* ===========================================
   MODAL COMPONENTS
   =========================================== */

.modal {
  position: fixed;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background: rgba(0, 0, 0, 0.5);
  display: none;
  align-items: center;
  justify-content: center;
  z-index: var(--z-modal);
  padding: var(--space-lg);
}

.modal.active {
  display: flex;
}

.modal-content {
  background: var(--bg-card);
  border-radius: var(--radius-lg);
  box-shadow: var(--shadow-xl);
  max-width: 500px;
  width: 100%;
  max-height: 90vh;
  overflow-y: auto;
}

.modal-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: var(--space-lg) var(--space-xl);
  border-bottom: 1px solid var(--border-color);
}

.modal-header h2 {
  margin: 0;
  font-size: var(--font-size-xl);
  color: var(--text-primary);
}

.modal-close {
  background: none;
  border: none;
  font-size: var(--font-size-xl);
  cursor: pointer;
  color: var(--text-muted);
  width: 32px;
  height: 32px;
  display: flex;
  align-items: center;
  justify-content: center;
  border-radius: var(--radius-sm);
  transition: background-color var(--transition-fast);
}

.modal-close:hover {
  background: var(--color-gray-100);
  color: var(--text-primary);
}

.modal-body {
  padding: var(--space-xl);
}

.data-actions {
  display: flex;
  flex-direction: column;
  gap: var(--space-md);
  margin-bottom: var(--space-xl);
}

.data-info h3 {
  font-size: var(--font-size-lg);
  margin-bottom: var(--space-md);
  color: var(--text-primary);
}

.data-info ul {
  list-style-position: inside;
  color: var(--text-secondary);
  line-height: var(--line-height-relaxed);
}

/* ===========================================
   RESPONSIVE DESIGN
   =========================================== */

@media (max-width: 768px) {
  .header-content {
    flex-direction: column;
    gap: var(--space-md);
    text-align: center;
  }
  
  .stats-container {
    order: -1;
    justify-content: center;
  }
  
  .main-content {
    padding: var(--space-lg);
  }
  
  .form-row {
    grid-template-columns: 1fr;
  }
  
  .controls-container {
    flex-direction: column;
    align-items: stretch;
  }
  
  .filter-group {
    flex-direction: column;
  }
  
  .filter-item {
    justify-content: space-between;
  }
  
  .view-controls {
    justify-content: center;
  }
  
  .progress-container {
    flex-direction: column;
    gap: var(--space-sm);
  }
  
  .progress-text {
    text-align: center;
  }
  
  .task-actions {
    position: static;
    opacity: 1;
    margin-top: var(--space-md);
    justify-content: center;
  }
  
  .footer-content {
    flex-direction: column;
    text-align: center;
  }
}

@media (max-width: 480px) {
  .task-form-section,
  .task-controls-section {
    padding: var(--space-lg);
  }
  
  .form-actions {
    flex-direction: column;
  }
  
  .task-header {
    flex-wrap: wrap;
  }
  
  .task-body {
    margin-left: 0;
    margin-top: var(--space-sm);
  }
  
  .task-meta {
    flex-direction: column;
    align-items: flex-start;
    gap: var(--space-sm);
  }
}

/* ===========================================
   ANIMATION UTILITIES
   =========================================== */

@keyframes fadeIn {
  from { opacity: 0; transform: translateY(10px); }
  to { opacity: 1; transform: translateY(0); }
}

@keyframes slideIn {
  from { transform: translateX(-100%); }
  to { transform: translateX(0); }
}

@keyframes pulse {
  0%, 100% { opacity: 1; }
  50% { opacity: 0.5; }
}

.fade-in {
  animation: fadeIn 0.3s ease;
}

.slide-in {
  animation: slideIn 0.3s ease;
}

.pulse {
  animation: pulse 1.5s infinite;
}

/* ===========================================
   UTILITY CLASSES
   =========================================== */

.hidden {
  display: none !important;
}

.invisible {
  visibility: hidden !important;
}

.text-center {
  text-align: center !important;
}

.text-left {
  text-align: left !important;
}

.text-right {
  text-align: right !important;
}

.font-mono {
  font-family: var(--font-family-mono) !important;
}

.sr-only {
  position: absolute !important;
  width: 1px !important;
  height: 1px !important;
  padding: 0 !important;
  margin: -1px !important;
  overflow: hidden !important;
  clip: rect(0, 0, 0, 0) !important;
  white-space: nowrap !important;
  border: 0 !important;
}

/* ===========================================
   PRINT STYLES
   =========================================== */

@media print {
  .task-form-section,
  .task-controls-section,
  .task-actions,
  .app-footer {
    display: none;
  }
  
  .task-card {
    border: 1px solid #000;
    box-shadow: none;
    page-break-inside: avoid;
    margin-bottom: var(--space-md);
  }
  
  .app-header {
    border-bottom: 2px solid #000;
    box-shadow: none;
  }
}

/* ===========================================
   REDUCED MOTION
   =========================================== */

@media (prefers-reduced-motion: reduce) {
  *,
  *::before,
  *::after {
    animation-duration: 0.01ms !important;
    animation-iteration-count: 1 !important;
    transition-duration: 0.01ms !important;
    scroll-behavior: auto !important;
  }
  
  .task-card:hover {
    transform: none;
  }
  
  .btn:hover {
    transform: none;
  }
}

/* ===========================================
   HIGH CONTRAST MODE
   =========================================== */

@media (prefers-contrast: high) {
  :root {
    --border-color: #000000;
    --shadow-sm: none;
    --shadow-md: none;
    --shadow-lg: none;
    --shadow-xl: none;
  }
  
  .task-card,
  .form-input,
  .form-select,
  .form-textarea,
  .btn {
    border-width: 2px;
  }
}
```

### Exercise 4.2: Create Theme System

Create `styles/themes.css` for advanced theming:

```css
/* ===========================================
   THEME SYSTEM
   =========================================== */

/* Theme transition animations */
* {
  transition-property: background-color, border-color, color, box-shadow;
  transition-duration: var(--transition-normal);
  transition-timing-function: ease;
}

/* Theme-specific overrides */
[data-theme="dark"] {
  /* Adjust shadows for dark theme */
  --shadow-sm: 0 1px 2px rgba(0, 0, 0, 0.3);
  --shadow-md: 0 4px 6px rgba(0, 0, 0, 0.3);
  --shadow-lg: 0 10px 15px rgba(0, 0, 0, 0.3);
  --shadow-xl: 0 20px 25px rgba(0, 0, 0, 0.4);
}

/* Theme-specific category colors for dark mode */
[data-theme="dark"] .category-work { 
  background: rgba(25, 118, 210, 0.3); 
  color: #90caf9; 
}

[data-theme="dark"] .category-personal { 
  background: rgba(46, 125, 50, 0.3); 
  color: #a5d6a7; 
}

[data-theme="dark"] .category-learning { 
  background: rgba(239, 108, 0, 0.3); 
  color: #ffb74d; 
}

[data-theme="dark"] .category-health { 
  background: rgba(194, 24, 91, 0.3); 
  color: #f48fb1; 
}

/* Auto theme detection */
@media (prefers-color-scheme: dark) {
  :root:not([data-theme]) {
    --bg-primary: var(--color-gray-900);
    --bg-secondary: var(--color-gray-800);
    --bg-card: var(--color-gray-800);
    --bg-input: var(--color-gray-700);
    
    --text-primary: var(--color-gray-100);
    --text-secondary: var(--color-gray-300);
    --text-muted: var(--color-gray-400);
    
    --border-color: var(--color-gray-600);
    --border-color-light: var(--color-gray-700);
    --border-color-dark: var(--color-gray-500);
  }
}
```

### Exercise 4.3: Responsive Design Testing

Create different viewport tests for your dashboard:

1. **Mobile (320px-768px)**
   - Single column layout
   - Stacked form elements
   - Touch-friendly buttons (44px minimum)
   - Simplified navigation

2. **Tablet (768px-1024px)**
   - Two-column grid for forms
   - Sidebar navigation
   - Medium-sized cards

3. **Desktop (1024px+)**
   - Multi-column layouts
   - Hover effects
   - Keyboard shortcuts
   - Advanced filtering

Document your responsive strategy in `docs/responsive-design.md`

### Exercise 4.4: Performance Optimization

Implement CSS performance best practices:

1. **Critical CSS**: Identify above-the-fold styles
2. **CSS Custom Properties**: Use variables for repeated values
3. **Efficient Selectors**: Avoid deep nesting and complex selectors
4. **Animation Performance**: Use transform and opacity for animations

Create `docs/css-performance.md` documenting your optimization strategies.

### Success Criteria
- [ ] Complete CSS system implemented with design tokens
- [ ] Responsive design works on all screen sizes
- [ ] Dark/light theme system functional
- [ ] All interactive states (hover, focus, active) styled
- [ ] Accessibility considerations (contrast, focus, reduced motion)
- [ ] Performance optimizations applied
- [ ] Print styles for task lists
- [ ] Cross-browser compatibility tested

### 💡 Key Learning Points

1. **Design Systems**: Using CSS custom properties for consistency
2. **Component Architecture**: Styling components independently
3. **Responsive Design**: Mobile-first approach with progressive enhancement
4. **Accessibility**: Inclusive design from the start
5. **Performance**: Optimizing CSS for speed and efficiency

### 🎯 Looking Ahead

In Chapter 5, you'll add JavaScript functionality to make your beautifully styled dashboard interactive. The CSS foundation you've built will support all the dynamic features you'll implement.

**Your dashboard looks professional** - now it's time to make it work!