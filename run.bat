@echo off
echo Starting Recoater Application...

:: Start Backend in a new window
start "Recoater Backend" cmd /k "cd backend && python -m uvicorn app.main:app --reload"

:: Wait for backend to start
timeout /t 5 /nobreak >nul

:: Start Frontend in a new window
start "Recoater Frontend" cmd /k "cd frontend && npm run dev"

:: Open browser after a short delay
timeout /t 1 /nobreak >nul
start "" http://localhost:5173

echo.
echo Application should be running at http://localhost:5173
echo If you see any errors, check the command windows that opened.
echo.
pause
