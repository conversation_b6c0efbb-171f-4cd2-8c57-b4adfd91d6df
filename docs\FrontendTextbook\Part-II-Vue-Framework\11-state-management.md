# Chapter 11: State Management with Pinia

## Learning Objectives
- Understand when and why to use centralized state management
- Master Pinia stores for Vue.js applications
- Implement reactive state, getters, and actions
- Share state across multiple components
- Handle asynchronous operations in stores

## Understanding State Management

### Component State vs Global State

```
Component State (Local):
┌─────────────────┐
│  Component A    │ ← Only Component A can access
│ selectedDrum: 0 │
└─────────────────┘

Global State (Shared):
┌─────────────────┐
│   Pinia Store   │ ← All components can access
│ selectedDrum: 0 │ ← Single source of truth
│ drums: [...]    │
└─────────────────┘
         ↓
┌─────────────────┐  ┌─────────────────┐  ┌─────────────────┐
│  Component A    │  │  Component B    │  │  Component C    │
│                 │  │                 │  │                 │
└─────────────────┘  └─────────────────┘  └─────────────────┘
```

### When to Use Global State

**Use Global State for:**
- Data shared across multiple components
- User authentication status
- Application configuration
- Complex forms with multiple steps
- Real-time updates (WebSocket data)

**Keep Local State for:**
- Component-specific UI state
- Form inputs before submission
- Temporary data that doesn't need persistence

## Setting Up Pinia

```javascript
// main.js
import { createApp } from 'vue'
import { createPinia } from 'pinia'
import App from './App.vue'

const app = createApp(App)
const pinia = createPinia()

app.use(pinia)
app.mount('#app')
```

## Creating Pinia Stores

### Basic Store Structure

```javascript
// stores/drumStore.js
import { defineStore } from 'pinia'
import { ref, computed } from 'vue'

export const useDrumStore = defineStore('drum', () => {
  // State (reactive data)
  const drums = ref([])
  const selectedDrumId = ref(null)
  const isLoading = ref(false)
  const error = ref(null)
  
  // Getters (computed properties)
  const selectedDrum = computed(() => {
    return drums.value.find(drum => drum.id === selectedDrumId.value)
  })
  
  const availableDrums = computed(() => {
    return drums.value.filter(drum => drum.status !== 'error')
  })
  
  const drumCount = computed(() => drums.value.length)
  
  // Actions (methods)
  const loadDrums = async () => {
    isLoading.value = true
    error.value = null
    
    try {
      const response = await fetch('/api/drums')
      drums.value = await response.json()
    } catch (err) {
      error.value = err.message
    } finally {
      isLoading.value = false
    }
  }
  
  const selectDrum = (drumId) => {
    selectedDrumId.value = drumId
  }
  
  const updateDrumStatus = (drumId, status) => {
    const drum = drums.value.find(d => d.id === drumId)
    if (drum) {
      drum.status = status
    }
  }
  
  return {
    // State
    drums,
    selectedDrumId,
    isLoading,
    error,
    // Getters
    selectedDrum,
    availableDrums,
    drumCount,
    // Actions
    loadDrums,
    selectDrum,
    updateDrumStatus
  }
})
```

### Using Stores in Components

```vue
<template>
  <div class="drum-dashboard">
    <!-- Loading state -->
    <div v-if="drumStore.isLoading" class="loading">
      Loading drums...
    </div>
    
    <!-- Error state -->
    <div v-else-if="drumStore.error" class="error">
      Error: {{ drumStore.error }}
    </div>
    
    <!-- Drum list -->
    <div v-else class="drum-list">
      <div 
        v-for="drum in drumStore.availableDrums" 
        :key="drum.id"
        :class="{ selected: drum.id === drumStore.selectedDrumId }"
        @click="drumStore.selectDrum(drum.id)"
        class="drum-card"
      >
        <h3>{{ drum.name }}</h3>
        <p>Status: {{ drum.status }}</p>
        <p>Temperature: {{ drum.temperature }}°C</p>
      </div>
    </div>
    
    <!-- Selected drum details -->
    <div v-if="drumStore.selectedDrum" class="drum-details">
      <h2>{{ drumStore.selectedDrum.name }}</h2>
      <p>Currently selected drum</p>
    </div>
    
    <!-- Summary -->
    <div class="summary">
      Total drums: {{ drumStore.drumCount }}
    </div>
  </div>
</template>

<script>
import { useDrumStore } from '@/stores/drumStore'
import { onMounted } from 'vue'

export default {
  setup() {
    const drumStore = useDrumStore()
    
    // Load drums when component mounts
    onMounted(() => {
      drumStore.loadDrums()
    })
    
    return {
      drumStore
    }
  }
}
</script>
```

## Advanced Store Patterns

### Store Composition (Multiple Stores)

```javascript
// stores/layerStore.js
export const useLayerStore = defineStore('layer', () => {
  const layers = ref([])
  const currentLayerIndex = ref(0)
  
  const currentLayer = computed(() => {
    return layers.value[currentLayerIndex.value]
  })
  
  const loadLayers = async (drumId) => {
    const response = await fetch(`/api/drums/${drumId}/layers`)
    layers.value = await response.json()
  }
  
  const setCurrentLayer = (index) => {
    currentLayerIndex.value = index
  }
  
  return {
    layers,
    currentLayerIndex,
    currentLayer,
    loadLayers,
    setCurrentLayer
  }
})

// stores/previewStore.js
export const usePreviewStore = defineStore('preview', () => {
  const previewImageUrl = ref(null)
  const isLoading = ref(false)
  
  const loadPreview = async (drumId, layerIndex) => {
    isLoading.value = true
    try {
      const response = await fetch(`/api/preview/${drumId}/${layerIndex}`)
      const blob = await response.blob()
      previewImageUrl.value = URL.createObjectURL(blob)
    } finally {
      isLoading.value = false
    }
  }
  
  return {
    previewImageUrl,
    isLoading,
    loadPreview
  }
})
```

### Using Multiple Stores Together

```vue
<template>
  <div class="layer-preview-app">
    <!-- Drum selector -->
    <select @change="handleDrumChange" v-model="selectedDrumId">
      <option v-for="drum in drumStore.drums" :key="drum.id" :value="drum.id">
        {{ drum.name }}
      </option>
    </select>
    
    <!-- Layer selector -->
    <div v-if="layerStore.layers.length > 0" class="layer-navigation">
      <button @click="previousLayer" :disabled="layerStore.currentLayerIndex <= 0">
        Previous
      </button>
      <span>Layer {{ layerStore.currentLayerIndex + 1 }} of {{ layerStore.layers.length }}</span>
      <button @click="nextLayer" :disabled="layerStore.currentLayerIndex >= layerStore.layers.length - 1">
        Next
      </button>
    </div>
    
    <!-- Preview -->
    <div class="preview-area">
      <div v-if="previewStore.isLoading">Loading preview...</div>
      <img v-else-if="previewStore.previewImageUrl" :src="previewStore.previewImageUrl" alt="Layer Preview">
    </div>
  </div>
</template>

<script>
export default {
  setup() {
    const drumStore = useDrumStore()
    const layerStore = useLayerStore()
    const previewStore = usePreviewStore()
    
    const selectedDrumId = ref(null)
    
    const handleDrumChange = async () => {
      if (selectedDrumId.value) {
        await layerStore.loadLayers(selectedDrumId.value)
        loadCurrentPreview()
      }
    }
    
    const previousLayer = () => {
      if (layerStore.currentLayerIndex > 0) {
        layerStore.setCurrentLayer(layerStore.currentLayerIndex - 1)
        loadCurrentPreview()
      }
    }
    
    const nextLayer = () => {
      if (layerStore.currentLayerIndex < layerStore.layers.length - 1) {
        layerStore.setCurrentLayer(layerStore.currentLayerIndex + 1)
        loadCurrentPreview()
      }
    }
    
    const loadCurrentPreview = () => {
      if (selectedDrumId.value !== null) {
        previewStore.loadPreview(selectedDrumId.value, layerStore.currentLayerIndex)
      }
    }
    
    onMounted(() => {
      drumStore.loadDrums()
    })
    
    return {
      drumStore,
      layerStore,
      previewStore,
      selectedDrumId,
      handleDrumChange,
      previousLayer,
      nextLayer
    }
  }
}
</script>
```

## Real-World Example: Print Job Management Store

```javascript
// stores/printJobStore.js
export const usePrintJobStore = defineStore('printJob', () => {
  // State
  const currentJob = ref(null)
  const jobHistory = ref([])
  const isExecuting = ref(false)
  const progress = ref(0)
  const logs = ref([])
  
  // Getters
  const hasActiveJob = computed(() => currentJob.value !== null)
  
  const jobStatus = computed(() => {
    if (!currentJob.value) return 'idle'
    if (isExecuting.value) return 'running'
    if (currentJob.value.completed) return 'completed'
    return 'paused'
  })
  
  const recentJobs = computed(() => {
    return jobHistory.value
      .slice(-5)
      .sort((a, b) => new Date(b.startTime) - new Date(a.startTime))
  })
  
  // Actions
  const startJob = async (jobData) => {
    try {
      currentJob.value = {
        id: Date.now(),
        name: jobData.name,
        startTime: new Date().toISOString(),
        totalLayers: jobData.layers.length,
        completed: false,
        ...jobData
      }
      
      isExecuting.value = true
      progress.value = 0
      
      addLog('Job started', 'info')
      
      // Simulate job execution
      for (let i = 0; i < jobData.layers.length; i++) {
        if (!isExecuting.value) break // Job was cancelled
        
        await executeLayer(jobData.layers[i])
        progress.value = ((i + 1) / jobData.layers.length) * 100
        addLog(`Layer ${i + 1} completed`, 'success')
      }
      
      if (isExecuting.value) {
        currentJob.value.completed = true
        currentJob.value.endTime = new Date().toISOString()
        jobHistory.value.push({ ...currentJob.value })
        addLog('Job completed successfully', 'success')
      }
      
    } catch (error) {
      addLog(`Job failed: ${error.message}`, 'error')
      throw error
    } finally {
      isExecuting.value = false
    }
  }
  
  const cancelJob = () => {
    if (isExecuting.value) {
      isExecuting.value = false
      addLog('Job cancelled by user', 'warning')
    }
  }
  
  const clearJob = () => {
    currentJob.value = null
    progress.value = 0
    logs.value = []
  }
  
  const executeLayer = async (layer) => {
    // Simulate layer execution time
    await new Promise(resolve => setTimeout(resolve, 1000))
    
    // Simulate potential errors
    if (Math.random() < 0.1) {
      throw new Error('Layer execution failed')
    }
  }
  
  const addLog = (message, type = 'info') => {
    logs.value.push({
      id: Date.now(),
      message,
      type,
      timestamp: new Date().toISOString()
    })
    
    // Keep only last 100 logs
    if (logs.value.length > 100) {
      logs.value = logs.value.slice(-100)
    }
  }
  
  return {
    // State
    currentJob,
    jobHistory,
    isExecuting,
    progress,
    logs,
    // Getters
    hasActiveJob,
    jobStatus,
    recentJobs,
    // Actions
    startJob,
    cancelJob,
    clearJob,
    addLog
  }
})
```

### Using the Print Job Store

```vue
<template>
  <div class="print-dashboard">
    <!-- Job Control -->
    <div class="job-control">
      <button v-if="!printJobStore.hasActiveJob" @click="showJobCreator = true">
        Start New Job
      </button>
      
      <div v-else class="active-job">
        <h3>{{ printJobStore.currentJob.name }}</h3>
        <div class="progress-bar">
          <div 
            class="progress-fill" 
            :style="{ width: printJobStore.progress + '%' }"
          ></div>
        </div>
        <p>Status: {{ printJobStore.jobStatus }}</p>
        
        <button v-if="printJobStore.isExecuting" @click="printJobStore.cancelJob">
          Cancel Job
        </button>
        <button v-else @click="printJobStore.clearJob">
          Clear Job
        </button>
      </div>
    </div>
    
    <!-- Job History -->
    <div class="job-history">
      <h3>Recent Jobs</h3>
      <div v-for="job in printJobStore.recentJobs" :key="job.id" class="job-item">
        <span>{{ job.name }}</span>
        <span>{{ formatDate(job.startTime) }}</span>
        <span :class="job.completed ? 'success' : 'failed'">
          {{ job.completed ? 'Completed' : 'Failed' }}
        </span>
      </div>
    </div>
    
    <!-- Live Logs -->
    <div class="logs">
      <h3>Logs</h3>
      <div class="log-container">
        <div 
          v-for="log in printJobStore.logs.slice(-10)" 
          :key="log.id" 
          :class="['log-entry', log.type]"
        >
          <span class="timestamp">{{ formatTime(log.timestamp) }}</span>
          <span class="message">{{ log.message }}</span>
        </div>
      </div>
    </div>
  </div>
</template>

<script>
export default {
  setup() {
    const printJobStore = usePrintJobStore()
    const showJobCreator = ref(false)
    
    const formatDate = (dateString) => {
      return new Date(dateString).toLocaleDateString()
    }
    
    const formatTime = (dateString) => {
      return new Date(dateString).toLocaleTimeString()
    }
    
    return {
      printJobStore,
      showJobCreator,
      formatDate,
      formatTime
    }
  }
}
</script>
```

## Store Best Practices

### 1. **Store Organization**
- One store per domain (drums, layers, jobs)
- Keep stores focused and cohesive
- Use composition for complex state

### 2. **Naming Conventions**
```javascript
// State: nouns
const drums = ref([])
const isLoading = ref(false)

// Getters: descriptive, often adjectives
const availableDrums = computed(() => ...)
const hasActiveJob = computed(() => ...)

// Actions: verbs
const loadDrums = async () => {}
const selectDrum = (id) => {}
```

### 3. **Error Handling**
```javascript
const loadData = async () => {
  isLoading.value = true
  error.value = null
  
  try {
    const data = await fetchData()
    items.value = data
  } catch (err) {
    error.value = err.message
    console.error('Failed to load data:', err)
  } finally {
    isLoading.value = false
  }
}
```

## Key Takeaways

1. **Pinia provides centralized state management** for Vue applications
2. **Stores contain state, getters, and actions** in a single place
3. **Multiple stores can work together** for complex applications
4. **Reactive state automatically updates** all connected components
5. **Proper error handling and loading states** improve user experience

## Next Steps

Chapter 12 will cover **API Integration and HTTP Communication** for connecting your Vue.js application with backend services.

---

## 🚀 Mini Project: Advanced Task Management with Pinia

### Exercise 11.1: Create Comprehensive Task Store System

Transform your task dashboard into a full state management system with Pinia stores that handle complex application state.

#### `src/stores/taskStore.js`:

```javascript
import { defineStore } from 'pinia'
import { ref, computed, watch } from 'vue'
import { useNotificationStore } from './notificationStore'
import { useAnalyticsStore } from './analyticsStore'

export const useTaskStore = defineStore('task', () => {
  // Get other stores
  const notificationStore = useNotificationStore()
  const analyticsStore = useAnalyticsStore()

  // State
  const tasks = ref([])
  const isLoading = ref(false)
  const error = ref(null)
  const lastSync = ref(null)
  const selectedTaskIds = ref(new Set())
  const viewMode = ref('grid') // grid, list, kanban
  const filters = ref({
    search: '',
    category: null,
    priority: null,
    status: 'all', // all, pending, completed, overdue
    dateRange: null
  })
  const sortBy = ref({ field: 'createdAt', direction: 'desc' })

  // Categories with analytics
  const categories = ref([
    { id: 'personal', name: 'Personal', emoji: '👤', color: '#3b82f6' },
    { id: 'work', name: 'Work', emoji: '💼', color: '#10b981' },
    { id: 'health', name: 'Health', emoji: '🏃‍♂️', color: '#f59e0b' },
    { id: 'learning', name: 'Learning', emoji: '📚', color: '#8b5cf6' },
    { id: 'finance', name: 'Finance', emoji: '💰', color: '#06b6d4' },
    { id: 'family', name: 'Family', emoji: '👨‍👩‍👧‍👦', color: '#ec4899' },
    { id: 'travel', name: 'Travel', emoji: '✈️', color: '#14b8a6' },
    { id: 'hobbies', name: 'Hobbies', emoji: '🎨', color: '#f97316' }
  ])

  // Template tasks for quick creation
  const taskTemplates = ref([
    {
      id: 'daily-standup',
      title: 'Daily Team Standup',
      description: 'Attend daily standup meeting',
      category: 'work',
      priority: 'medium',
      estimatedDuration: 30,
      tags: ['meeting', 'daily', 'team']
    },
    {
      id: 'exercise',
      title: 'Exercise Session',
      description: 'Complete workout routine',
      category: 'health',
      priority: 'high',
      estimatedDuration: 60,
      tags: ['fitness', 'health', 'routine']
    },
    {
      id: 'code-review',
      title: 'Code Review',
      description: 'Review team member\'s pull request',
      category: 'work',
      priority: 'medium',
      estimatedDuration: 45,
      tags: ['code', 'review', 'collaboration']
    }
  ])

  // Computed properties
  const filteredTasks = computed(() => {
    let filtered = [...tasks.value]

    // Search filter
    if (filters.value.search) {
      const search = filters.value.search.toLowerCase()
      filtered = filtered.filter(task =>
        task.title.toLowerCase().includes(search) ||
        task.description?.toLowerCase().includes(search) ||
        task.tags?.some(tag => tag.toLowerCase().includes(search))
      )
    }

    // Category filter
    if (filters.value.category) {
      filtered = filtered.filter(task => task.category === filters.value.category)
    }

    // Priority filter
    if (filters.value.priority) {
      filtered = filtered.filter(task => task.priority === filters.value.priority)
    }

    // Status filter
    if (filters.value.status !== 'all') {
      switch (filters.value.status) {
        case 'completed':
          filtered = filtered.filter(task => task.completed)
          break
        case 'pending':
          filtered = filtered.filter(task => !task.completed)
          break
        case 'overdue':
          filtered = filtered.filter(task => 
            !task.completed && task.dueDate && new Date(task.dueDate) < new Date()
          )
          break
      }
    }

    // Date range filter
    if (filters.value.dateRange) {
      const { start, end } = filters.value.dateRange
      filtered = filtered.filter(task => {
        const taskDate = new Date(task.createdAt)
        return taskDate >= new Date(start) && taskDate <= new Date(end)
      })
    }

    return filtered
  })

  const sortedTasks = computed(() => {
    return [...filteredTasks.value].sort((a, b) => {
      const { field, direction } = sortBy.value
      let aValue = a[field]
      let bValue = b[field]

      // Handle different data types
      if (field === 'dueDate') {
        aValue = aValue ? new Date(aValue) : new Date('9999-12-31')
        bValue = bValue ? new Date(bValue) : new Date('9999-12-31')
      } else if (field === 'priority') {
        const priorityOrder = { high: 3, medium: 2, low: 1 }
        aValue = priorityOrder[aValue] || 0
        bValue = priorityOrder[bValue] || 0
      } else if (field === 'estimatedDuration') {
        aValue = aValue || 0
        bValue = bValue || 0
      }

      const comparison = aValue < bValue ? -1 : aValue > bValue ? 1 : 0
      return direction === 'asc' ? comparison : -comparison
    })
  })

  const taskStats = computed(() => {
    const all = tasks.value
    const completed = all.filter(t => t.completed)
    const pending = all.filter(t => !t.completed)
    const overdue = pending.filter(t => t.dueDate && new Date(t.dueDate) < new Date())
    const today = pending.filter(t => {
      if (!t.dueDate) return false
      const due = new Date(t.dueDate)
      const now = new Date()
      return due.toDateString() === now.toDateString()
    })

    const totalEstimatedTime = pending.reduce((sum, task) => sum + (task.estimatedDuration || 0), 0)
    const completedThisWeek = completed.filter(t => {
      const completedDate = new Date(t.completedAt)
      const weekAgo = new Date()
      weekAgo.setDate(weekAgo.getDate() - 7)
      return completedDate >= weekAgo
    })

    return {
      total: all.length,
      completed: completed.length,
      pending: pending.length,
      overdue: overdue.length,
      dueToday: today.length,
      completionRate: all.length > 0 ? (completed.length / all.length * 100).toFixed(1) : 0,
      totalEstimatedTime,
      completedThisWeek: completedThisWeek.length,
      avgCompletionTime: calculateAvgCompletionTime(completed)
    }
  })

  const categoryStats = computed(() => {
    return categories.value.map(category => {
      const categoryTasks = tasks.value.filter(t => t.category === category.id)
      const completed = categoryTasks.filter(t => t.completed)
      
      return {
        ...category,
        taskCount: categoryTasks.length,
        completedCount: completed.length,
        completionRate: categoryTasks.length > 0 
          ? (completed.length / categoryTasks.length * 100).toFixed(1) 
          : 0
      }
    })
  })

  const priorityDistribution = computed(() => {
    const distribution = { high: 0, medium: 0, low: 0 }
    tasks.value.forEach(task => {
      if (task.priority && !task.completed) {
        distribution[task.priority]++
      }
    })
    return distribution
  })

  const upcomingDeadlines = computed(() => {
    const upcoming = tasks.value
      .filter(task => !task.completed && task.dueDate)
      .map(task => ({
        ...task,
        daysUntilDue: Math.ceil((new Date(task.dueDate) - new Date()) / (1000 * 60 * 60 * 24))
      }))
      .filter(task => task.daysUntilDue >= 0 && task.daysUntilDue <= 7)
      .sort((a, b) => a.daysUntilDue - b.daysUntilDue)
    
    return upcoming
  })

  // Actions
  const createTask = (taskData) => {
    const newTask = {
      id: generateTaskId(),
      title: taskData.title,
      description: taskData.description || '',
      category: taskData.category || 'personal',
      priority: taskData.priority || 'medium',
      tags: taskData.tags || [],
      dueDate: taskData.dueDate || null,
      estimatedDuration: taskData.estimatedDuration || null,
      completed: false,
      createdAt: new Date().toISOString(),
      updatedAt: new Date().toISOString(),
      completedAt: null,
      subtasks: taskData.subtasks || [],
      attachments: taskData.attachments || [],
      notes: taskData.notes || ''
    }

    tasks.value.push(newTask)
    analyticsStore.trackEvent('task_created', { category: newTask.category, priority: newTask.priority })
    notificationStore.addNotification({
      type: 'success',
      message: `Task "${newTask.title}" created successfully`,
      duration: 3000
    })

    saveToLocalStorage()
    return newTask
  }

  const updateTask = (taskId, updates) => {
    const index = tasks.value.findIndex(t => t.id === taskId)
    if (index === -1) return null

    const oldTask = { ...tasks.value[index] }
    const updatedTask = {
      ...oldTask,
      ...updates,
      updatedAt: new Date().toISOString()
    }

    // Handle completion state change
    if (updates.completed !== undefined && updates.completed !== oldTask.completed) {
      if (updates.completed) {
        updatedTask.completedAt = new Date().toISOString()
        analyticsStore.trackEvent('task_completed', { 
          category: updatedTask.category,
          duration: calculateTaskDuration(oldTask.createdAt, updatedTask.completedAt)
        })
        notificationStore.addNotification({
          type: 'success',
          message: `Task "${updatedTask.title}" completed! 🎉`,
          duration: 4000
        })
      } else {
        updatedTask.completedAt = null
        analyticsStore.trackEvent('task_uncompleted', { category: updatedTask.category })
      }
    }

    tasks.value[index] = updatedTask
    saveToLocalStorage()
    return updatedTask
  }

  const deleteTask = (taskId) => {
    const index = tasks.value.findIndex(t => t.id === taskId)
    if (index === -1) return false

    const task = tasks.value[index]
    tasks.value.splice(index, 1)
    selectedTaskIds.value.delete(taskId)

    analyticsStore.trackEvent('task_deleted', { category: task.category })
    notificationStore.addNotification({
      type: 'info',
      message: `Task "${task.title}" deleted`,
      duration: 3000,
      actions: [
        {
          label: 'Undo',
          action: () => {
            tasks.value.splice(index, 0, task)
            saveToLocalStorage()
            notificationStore.addNotification({
              type: 'success',
              message: 'Task restored',
              duration: 2000
            })
          }
        }
      ]
    })

    saveToLocalStorage()
    return true
  }

  const bulkUpdateTasks = (taskIds, updates) => {
    const updatedTasks = []
    
    taskIds.forEach(taskId => {
      const updated = updateTask(taskId, updates)
      if (updated) updatedTasks.push(updated)
    })

    if (updatedTasks.length > 0) {
      analyticsStore.trackEvent('tasks_bulk_updated', { 
        count: updatedTasks.length, 
        updates: Object.keys(updates) 
      })
    }

    return updatedTasks
  }

  const createTaskFromTemplate = (templateId) => {
    const template = taskTemplates.value.find(t => t.id === templateId)
    if (!template) return null

    const { id, ...taskData } = template
    return createTask(taskData)
  }

  const duplicateTask = (taskId) => {
    const originalTask = tasks.value.find(t => t.id === taskId)
    if (!originalTask) return null

    const duplicatedTask = {
      ...originalTask,
      title: `${originalTask.title} (Copy)`,
      completed: false,
      createdAt: new Date().toISOString(),
      updatedAt: new Date().toISOString(),
      completedAt: null
    }

    delete duplicatedTask.id
    return createTask(duplicatedTask)
  }

  // Filter and sort actions
  const updateFilters = (newFilters) => {
    filters.value = { ...filters.value, ...newFilters }
    analyticsStore.trackEvent('filters_updated', { filters: Object.keys(newFilters) })
  }

  const clearFilters = () => {
    filters.value = {
      search: '',
      category: null,
      priority: null,
      status: 'all',
      dateRange: null
    }
  }

  const updateSort = (field, direction = null) => {
    if (sortBy.value.field === field && !direction) {
      // Toggle direction if same field
      sortBy.value.direction = sortBy.value.direction === 'asc' ? 'desc' : 'asc'
    } else {
      sortBy.value = {
        field,
        direction: direction || 'asc'
      }
    }
  }

  // Selection actions
  const selectTask = (taskId) => {
    selectedTaskIds.value.add(taskId)
  }

  const deselectTask = (taskId) => {
    selectedTaskIds.value.delete(taskId)
  }

  const toggleTaskSelection = (taskId) => {
    if (selectedTaskIds.value.has(taskId)) {
      deselectTask(taskId)
    } else {
      selectTask(taskId)
    }
  }

  const selectAllTasks = () => {
    filteredTasks.value.forEach(task => selectedTaskIds.value.add(task.id))
  }

  const clearSelection = () => {
    selectedTaskIds.value.clear()
  }

  // View actions
  const setViewMode = (mode) => {
    viewMode.value = mode
    analyticsStore.trackEvent('view_mode_changed', { mode })
  }

  // Data persistence
  const saveToLocalStorage = () => {
    try {
      localStorage.setItem('taskDashboard_tasks', JSON.stringify(tasks.value))
      localStorage.setItem('taskDashboard_lastSync', new Date().toISOString())
      lastSync.value = new Date().toISOString()
    } catch (error) {
      console.error('Failed to save to localStorage:', error)
      notificationStore.addNotification({
        type: 'error',
        message: 'Failed to save tasks to local storage',
        duration: 5000
      })
    }
  }

  const loadFromLocalStorage = () => {
    try {
      const savedTasks = localStorage.getItem('taskDashboard_tasks')
      const savedLastSync = localStorage.getItem('taskDashboard_lastSync')
      
      if (savedTasks) {
        tasks.value = JSON.parse(savedTasks)
        analyticsStore.trackEvent('data_loaded', { source: 'localStorage', taskCount: tasks.value.length })
      }
      
      if (savedLastSync) {
        lastSync.value = savedLastSync
      }

      // Create some sample tasks if none exist
      if (tasks.value.length === 0) {
        createSampleTasks()
      }

    } catch (error) {
      console.error('Failed to load from localStorage:', error)
      notificationStore.addNotification({
        type: 'warning',
        message: 'Failed to load saved tasks',
        duration: 5000
      })
      createSampleTasks()
    }
  }

  const exportTasks = (format = 'json') => {
    const exportData = {
      tasks: tasks.value,
      categories: categories.value,
      exportDate: new Date().toISOString(),
      version: '1.0'
    }

    if (format === 'json') {
      const dataStr = JSON.stringify(exportData, null, 2)
      const dataBlob = new Blob([dataStr], { type: 'application/json' })
      const url = URL.createObjectURL(dataBlob)
      
      const link = document.createElement('a')
      link.href = url
      link.download = `tasks_export_${new Date().toISOString().split('T')[0]}.json`
      link.click()
      
      URL.revokeObjectURL(url)
    }

    analyticsStore.trackEvent('tasks_exported', { format, taskCount: tasks.value.length })
    notificationStore.addNotification({
      type: 'success',
      message: `Tasks exported successfully (${format.toUpperCase()})`,
      duration: 3000
    })
  }

  const importTasks = (importData) => {
    try {
      if (importData.tasks && Array.isArray(importData.tasks)) {
        const importedCount = importData.tasks.length
        tasks.value = [...tasks.value, ...importData.tasks]
        
        saveToLocalStorage()
        analyticsStore.trackEvent('tasks_imported', { taskCount: importedCount })
        notificationStore.addNotification({
          type: 'success',
          message: `Successfully imported ${importedCount} tasks`,
          duration: 4000
        })
        
        return true
      }
    } catch (error) {
      console.error('Failed to import tasks:', error)
      notificationStore.addNotification({
        type: 'error',
        message: 'Failed to import tasks. Please check the file format.',
        duration: 5000
      })
    }
    return false
  }

  // Helper functions
  const generateTaskId = () => {
    return Date.now().toString(36) + Math.random().toString(36).substr(2)
  }

  const calculateTaskDuration = (startDate, endDate) => {
    return Math.round((new Date(endDate) - new Date(startDate)) / (1000 * 60 * 60 * 24))
  }

  const calculateAvgCompletionTime = (completedTasks) => {
    if (completedTasks.length === 0) return 0
    
    const totalDuration = completedTasks.reduce((sum, task) => {
      if (task.completedAt && task.createdAt) {
        return sum + calculateTaskDuration(task.createdAt, task.completedAt)
      }
      return sum
    }, 0)

    return Math.round(totalDuration / completedTasks.length)
  }

  const createSampleTasks = () => {
    const sampleTasks = [
      {
        title: 'Complete Vue.js tutorial',
        description: 'Finish the advanced Vue.js course and build a project',
        category: 'learning',
        priority: 'high',
        tags: ['vue', 'javascript', 'frontend'],
        estimatedDuration: 120,
        dueDate: new Date(Date.now() + 7 * 24 * 60 * 60 * 1000).toISOString()
      },
      {
        title: 'Weekly team meeting',
        description: 'Discuss project progress and upcoming deadlines',
        category: 'work',
        priority: 'medium',
        tags: ['meeting', 'team', 'planning'],
        estimatedDuration: 60,
        dueDate: new Date(Date.now() + 3 * 24 * 60 * 60 * 1000).toISOString()
      },
      {
        title: 'Grocery shopping',
        description: 'Buy groceries for the week',
        category: 'personal',
        priority: 'low',
        tags: ['shopping', 'weekly'],
        estimatedDuration: 45
      }
    ]

    sampleTasks.forEach(taskData => createTask(taskData))
    saveToLocalStorage()
  }

  // Watchers
  watch(tasks, () => {
    saveToLocalStorage()
  }, { deep: true })

  return {
    // State
    tasks,
    isLoading,
    error,
    lastSync,
    selectedTaskIds,
    viewMode,
    filters,
    sortBy,
    categories,
    taskTemplates,

    // Computed
    filteredTasks,
    sortedTasks,
    taskStats,
    categoryStats,
    priorityDistribution,
    upcomingDeadlines,

    // Actions
    createTask,
    updateTask,
    deleteTask,
    bulkUpdateTasks,
    createTaskFromTemplate,
    duplicateTask,
    updateFilters,
    clearFilters,
    updateSort,
    selectTask,
    deselectTask,
    toggleTaskSelection,
    selectAllTasks,
    clearSelection,
    setViewMode,
    saveToLocalStorage,
    loadFromLocalStorage,
    exportTasks,
    importTasks
  }
})
```

#### `src/stores/notificationStore.js`:

```javascript
import { defineStore } from 'pinia'
import { ref, computed } from 'vue'

export const useNotificationStore = defineStore('notification', () => {
  const notifications = ref([])
  const maxNotifications = ref(5)

  const activeNotifications = computed(() => 
    notifications.value.filter(n => !n.dismissed)
  )

  const addNotification = (notification) => {
    const newNotification = {
      id: Date.now() + Math.random(),
      type: 'info', // info, success, warning, error
      message: '',
      duration: 4000,
      actions: [],
      dismissible: true,
      timestamp: new Date().toISOString(),
      dismissed: false,
      ...notification
    }

    notifications.value.unshift(newNotification)

    // Auto-remove after duration
    if (newNotification.duration > 0) {
      setTimeout(() => {
        dismissNotification(newNotification.id)
      }, newNotification.duration)
    }

    // Keep only max notifications
    if (notifications.value.length > maxNotifications.value) {
      notifications.value = notifications.value.slice(0, maxNotifications.value)
    }

    return newNotification.id
  }

  const dismissNotification = (id) => {
    const notification = notifications.value.find(n => n.id === id)
    if (notification) {
      notification.dismissed = true
    }
  }

  const clearAll = () => {
    notifications.value.forEach(n => n.dismissed = true)
  }

  const removeNotification = (id) => {
    const index = notifications.value.findIndex(n => n.id === id)
    if (index > -1) {
      notifications.value.splice(index, 1)
    }
  }

  return {
    notifications,
    activeNotifications,
    addNotification,
    dismissNotification,
    clearAll,
    removeNotification
  }
})
```

#### `src/stores/analyticsStore.js`:

```javascript
import { defineStore } from 'pinia'
import { ref, computed } from 'vue'

export const useAnalyticsStore = defineStore('analytics', () => {
  const events = ref([])
  const sessionStart = ref(new Date().toISOString())
  const pageViews = ref([])

  const sessionStats = computed(() => {
    const now = new Date()
    const start = new Date(sessionStart.value)
    const duration = Math.round((now - start) / 1000 / 60) // minutes

    const eventCounts = events.value.reduce((acc, event) => {
      acc[event.name] = (acc[event.name] || 0) + 1
      return acc
    }, {})

    return {
      duration,
      eventCount: events.value.length,
      eventTypes: Object.keys(eventCounts).length,
      topEvents: Object.entries(eventCounts)
        .sort(([,a], [,b]) => b - a)
        .slice(0, 5)
    }
  })

  const trackEvent = (name, properties = {}) => {
    const event = {
      id: Date.now() + Math.random(),
      name,
      properties,
      timestamp: new Date().toISOString()
    }

    events.value.push(event)

    // Keep only last 1000 events
    if (events.value.length > 1000) {
      events.value = events.value.slice(-1000)
    }

    console.log('📊 Analytics Event:', name, properties)
  }

  const trackPageView = (page) => {
    pageViews.value.push({
      page,
      timestamp: new Date().toISOString()
    })
    trackEvent('page_view', { page })
  }

  const getEventsByName = (name) => {
    return events.value.filter(event => event.name === name)
  }

  const getEventsInTimeRange = (startDate, endDate) => {
    const start = new Date(startDate)
    const end = new Date(endDate)
    
    return events.value.filter(event => {
      const eventDate = new Date(event.timestamp)
      return eventDate >= start && eventDate <= end
    })
  }

  return {
    events,
    pageViews,
    sessionStats,
    trackEvent,
    trackPageView,
    getEventsByName,
    getEventsInTimeRange
  }
})
```

### Exercise 11.2: Create Advanced Dashboard with Store Integration

Create a comprehensive dashboard that showcases all the stores working together:

#### `src/components/AdvancedTaskDashboard.vue`:

```vue
<template>
  <div class="advanced-dashboard">
    <!-- Header with Stats -->
    <header class="dashboard-header">
      <div class="header-content">
        <div class="title-section">
          <h1 class="dashboard-title">📋 Task Dashboard</h1>
          <p class="last-sync">Last sync: {{ formatDate(taskStore.lastSync) }}</p>
        </div>

        <div class="quick-stats">
          <div class="stat-card">
            <div class="stat-value">{{ taskStore.taskStats.total }}</div>
            <div class="stat-label">Total Tasks</div>
          </div>
          <div class="stat-card">
            <div class="stat-value">{{ taskStore.taskStats.pending }}</div>
            <div class="stat-label">Pending</div>
          </div>
          <div class="stat-card">
            <div class="stat-value">{{ taskStore.taskStats.dueToday }}</div>
            <div class="stat-label">Due Today</div>
          </div>
          <div class="stat-card">
            <div class="stat-value">{{ taskStore.taskStats.completionRate }}%</div>
            <div class="stat-label">Completion Rate</div>
          </div>
        </div>

        <div class="header-actions">
          <button @click="showCreateTask = true" class="primary-btn">
            ➕ New Task
          </button>
          <button @click="exportData" class="secondary-btn">
            📤 Export
          </button>
          <input
            ref="importInput"
            type="file"
            accept=".json"
            style="display: none"
            @change="handleImport"
          />
          <button @click="$refs.importInput.click()" class="secondary-btn">
            📥 Import
          </button>
        </div>
      </div>
    </header>

    <!-- Analytics Overview -->
    <section class="analytics-section" v-if="showAnalytics">
      <div class="analytics-header">
        <h2>📊 Analytics</h2>
        <button @click="showAnalytics = false" class="close-btn">✕</button>
      </div>
      
      <div class="analytics-grid">
        <div class="analytics-card">
          <h3>Session Stats</h3>
          <div class="analytics-content">
            <p>Duration: {{ analyticsStore.sessionStats.duration }} minutes</p>
            <p>Events: {{ analyticsStore.sessionStats.eventCount }}</p>
            <p>Top Actions: {{ analyticsStore.sessionStats.topEvents.map(([name]) => name).join(', ') }}</p>
          </div>
        </div>

        <div class="analytics-card">
          <h3>Category Distribution</h3>
          <div class="category-chart">
            <div
              v-for="category in taskStore.categoryStats"
              :key="category.id"
              class="category-bar"
              :style="{ 
                '--completion-rate': category.completionRate + '%',
                '--category-color': category.color 
              }"
            >
              <div class="category-info">
                <span>{{ category.emoji }} {{ category.name }}</span>
                <span>{{ category.taskCount }} tasks ({{ category.completionRate }}%)</span>
              </div>
              <div class="category-progress">
                <div class="progress-fill"></div>
              </div>
            </div>
          </div>
        </div>

        <div class="analytics-card">
          <h3>Priority Distribution</h3>
          <div class="priority-chart">
            <div class="priority-item high">
              <span>🔴 High Priority</span>
              <span>{{ taskStore.priorityDistribution.high }}</span>
            </div>
            <div class="priority-item medium">
              <span>🟡 Medium Priority</span>
              <span>{{ taskStore.priorityDistribution.medium }}</span>
            </div>
            <div class="priority-item low">
              <span>🟢 Low Priority</span>
              <span>{{ taskStore.priorityDistribution.low }}</span>
            </div>
          </div>
        </div>

        <div class="analytics-card">
          <h3>Upcoming Deadlines</h3>
          <div class="deadlines-list">
            <div
              v-for="task in taskStore.upcomingDeadlines"
              :key="task.id"
              class="deadline-item"
              :class="{ urgent: task.daysUntilDue <= 1 }"
            >
              <div class="deadline-task">{{ task.title }}</div>
              <div class="deadline-time">
                {{ task.daysUntilDue === 0 ? 'Today' : `${task.daysUntilDue} days` }}
              </div>
            </div>
            <div v-if="taskStore.upcomingDeadlines.length === 0" class="no-deadlines">
              🎉 No upcoming deadlines!
            </div>
          </div>
        </div>
      </div>
    </section>

    <!-- Filters and Controls -->
    <section class="controls-section">
      <div class="filters-row">
        <div class="filter-group">
          <input
            v-model="taskStore.filters.search"
            type="text"
            placeholder="🔍 Search tasks..."
            class="search-input"
          />
        </div>

        <div class="filter-group">
          <select v-model="taskStore.filters.category" class="filter-select">
            <option value="">All Categories</option>
            <option
              v-for="category in taskStore.categories"
              :key="category.id"
              :value="category.id"
            >
              {{ category.emoji }} {{ category.name }}
            </option>
          </select>
        </div>

        <div class="filter-group">
          <select v-model="taskStore.filters.priority" class="filter-select">
            <option value="">All Priorities</option>
            <option value="high">🔴 High</option>
            <option value="medium">🟡 Medium</option>
            <option value="low">🟢 Low</option>
          </select>
        </div>

        <div class="filter-group">
          <select v-model="taskStore.filters.status" class="filter-select">
            <option value="all">All Status</option>
            <option value="pending">📋 Pending</option>
            <option value="completed">✅ Completed</option>
            <option value="overdue">⚠️ Overdue</option>
          </select>
        </div>

        <button @click="taskStore.clearFilters()" class="clear-filters-btn">
          Clear Filters
        </button>
      </div>

      <div class="view-controls">
        <div class="view-modes">
          <button
            v-for="mode in viewModes"
            :key="mode.value"
            @click="taskStore.setViewMode(mode.value)"
            :class="['view-mode-btn', { active: taskStore.viewMode === mode.value }]"
          >
            {{ mode.icon }} {{ mode.label }}
          </button>
        </div>

        <div class="selection-info" v-if="taskStore.selectedTaskIds.size > 0">
          <span>{{ taskStore.selectedTaskIds.size }} selected</span>
          <button @click="bulkComplete" class="bulk-action-btn">✅ Complete</button>
          <button @click="bulkDelete" class="bulk-action-btn danger">🗑️ Delete</button>
          <button @click="taskStore.clearSelection()" class="bulk-action-btn">Clear</button>
        </div>

        <button @click="showAnalytics = !showAnalytics" class="analytics-toggle">
          📊 Analytics
        </button>
      </div>
    </section>

    <!-- Task Templates -->
    <section class="templates-section" v-if="showTemplates">
      <h3>🚀 Quick Templates</h3>
      <div class="templates-grid">
        <div
          v-for="template in taskStore.taskTemplates"
          :key="template.id"
          @click="createFromTemplate(template.id)"
          class="template-card"
        >
          <div class="template-title">{{ template.title }}</div>
          <div class="template-meta">
            <span>{{ getCategoryDisplay(template.category) }}</span>
            <span>{{ template.estimatedDuration }}min</span>
          </div>
        </div>
      </div>
    </section>

    <!-- Main Task Area -->
    <main class="tasks-main">
      <InteractiveTaskBoard
        :tasks="taskStore.sortedTasks"
        :categories="taskStore.categories"
        :filters="taskStore.filters"
        @task-created="handleTaskCreated"
        @task-updated="handleTaskUpdated"
        @task-deleted="handleTaskDeleted"
        @tasks-bulk-updated="handleTasksBulkUpdated"
        @task-edit-requested="editTask"
        @files-dropped="handleFilesDropped"
        @board-title-updated="handleBoardTitleUpdated"
      />
    </main>

    <!-- Task Creation Modal -->
    <TaskCreationModal
      v-if="showCreateTask"
      @close="showCreateTask = false"
      @task-created="handleTaskCreated"
    />

    <!-- Task Edit Modal -->
    <TaskEditModal
      v-if="editingTask"
      :task="editingTask"
      @close="editingTask = null"
      @task-updated="handleTaskUpdated"
    />

    <!-- Notifications -->
    <NotificationContainer />
  </div>
</template>

<script setup>
import { ref, computed, onMounted, watch } from 'vue'
import { useTaskStore } from '../stores/taskStore'
import { useNotificationStore } from '../stores/notificationStore'
import { useAnalyticsStore } from '../stores/analyticsStore'
import InteractiveTaskBoard from './InteractiveTaskBoard.vue'
import TaskCreationModal from './TaskCreationModal.vue'
import TaskEditModal from './TaskEditModal.vue'
import NotificationContainer from './NotificationContainer.vue'

// Store instances
const taskStore = useTaskStore()
const notificationStore = useNotificationStore()
const analyticsStore = useAnalyticsStore()

// Component state
const showCreateTask = ref(false)
const showAnalytics = ref(false)
const showTemplates = ref(true)
const editingTask = ref(null)
const importInput = ref(null)

// View modes
const viewModes = [
  { value: 'grid', label: 'Grid', icon: '⊞' },
  { value: 'list', label: 'List', icon: '☰' },
  { value: 'kanban', label: 'Kanban', icon: '📋' }
]

// Methods
const formatDate = (dateString) => {
  if (!dateString) return 'Never'
  return new Date(dateString).toLocaleString()
}

const getCategoryDisplay = (categoryId) => {
  const category = taskStore.categories.find(c => c.id === categoryId)
  return category ? `${category.emoji} ${category.name}` : categoryId
}

const handleTaskCreated = (taskData) => {
  taskStore.createTask(taskData)
  showCreateTask.value = false
  analyticsStore.trackEvent('task_created_from_dashboard')
}

const handleTaskUpdated = (updatedTask) => {
  taskStore.updateTask(updatedTask.id, updatedTask)
  editingTask.value = null
}

const handleTaskDeleted = (task) => {
  taskStore.deleteTask(task.id)
}

const handleTasksBulkUpdated = (tasks) => {
  tasks.forEach(task => {
    taskStore.updateTask(task.id, task)
  })
}

const editTask = (task) => {
  editingTask.value = task
  analyticsStore.trackEvent('task_edit_opened', { category: task.category })
}

const createFromTemplate = (templateId) => {
  taskStore.createTaskFromTemplate(templateId)
  analyticsStore.trackEvent('template_used', { templateId })
}

const bulkComplete = () => {
  const updates = { completed: true }
  taskStore.bulkUpdateTasks(Array.from(taskStore.selectedTaskIds), updates)
  taskStore.clearSelection()
}

const bulkDelete = () => {
  if (confirm(`Delete ${taskStore.selectedTaskIds.size} selected tasks?`)) {
    Array.from(taskStore.selectedTaskIds).forEach(taskId => {
      taskStore.deleteTask(taskId)
    })
    taskStore.clearSelection()
  }
}

const exportData = () => {
  taskStore.exportTasks('json')
  analyticsStore.trackEvent('data_exported_from_dashboard')
}

const handleImport = (event) => {
  const file = event.target.files[0]
  if (!file) return

  const reader = new FileReader()
  reader.onload = (e) => {
    try {
      const importData = JSON.parse(e.target.result)
      taskStore.importTasks(importData)
      analyticsStore.trackEvent('data_imported_from_dashboard')
    } catch (error) {
      notificationStore.addNotification({
        type: 'error',
        message: 'Invalid file format. Please select a valid JSON file.',
        duration: 5000
      })
    }
  }
  reader.readAsText(file)
  
  // Clear input
  event.target.value = ''
}

const handleFilesDropped = (files) => {
  // Handle dropped files (could be import files or attachments)
  analyticsStore.trackEvent('files_dropped', { fileCount: files.length })
  notificationStore.addNotification({
    type: 'info',
    message: `${files.length} file(s) dropped - feature coming soon!`,
    duration: 3000
  })
}

const handleBoardTitleUpdated = (newTitle) => {
  analyticsStore.trackEvent('board_title_updated', { titleLength: newTitle.length })
}

// Lifecycle
onMounted(() => {
  taskStore.loadFromLocalStorage()
  analyticsStore.trackPageView('task_dashboard')
  
  // Track user engagement
  analyticsStore.trackEvent('dashboard_loaded', {
    taskCount: taskStore.tasks.length,
    viewMode: taskStore.viewMode
  })
})

// Watch for filter changes to track analytics
watch(() => taskStore.filters, (newFilters) => {
  const activeFilters = Object.entries(newFilters)
    .filter(([key, value]) => value && value !== 'all' && value !== '')
    .map(([key]) => key)
  
  if (activeFilters.length > 0) {
    analyticsStore.trackEvent('filters_applied', { filters: activeFilters })
  }
}, { deep: true })
</script>

<style scoped>
.advanced-dashboard {
  min-height: 100vh;
  background: var(--color-background);
}

.dashboard-header {
  background: var(--color-background-soft);
  border-bottom: 2px solid var(--color-border);
  padding: 2rem;
}

.header-content {
  display: flex;
  justify-content: space-between;
  align-items: center;
  flex-wrap: wrap;
  gap: 2rem;
}

.title-section {
  flex: 1;
  min-width: 200px;
}

.dashboard-title {
  margin: 0 0 0.5rem 0;
  font-size: 2.5rem;
  font-weight: 700;
  color: var(--color-text);
}

.last-sync {
  margin: 0;
  color: var(--color-text-light);
  font-size: 0.875rem;
}

.quick-stats {
  display: flex;
  gap: 1rem;
  flex-wrap: wrap;
}

.stat-card {
  background: var(--color-background);
  border: 1px solid var(--color-border);
  border-radius: 8px;
  padding: 1rem;
  text-align: center;
  min-width: 100px;
}

.stat-value {
  font-size: 1.5rem;
  font-weight: 700;
  color: var(--color-primary);
  margin-bottom: 0.25rem;
}

.stat-label {
  font-size: 0.75rem;
  color: var(--color-text-light);
  text-transform: uppercase;
  letter-spacing: 0.5px;
}

.header-actions {
  display: flex;
  gap: 0.5rem;
  flex-wrap: wrap;
}

.primary-btn {
  padding: 0.75rem 1.5rem;
  background: var(--color-primary);
  color: white;
  border: none;
  border-radius: 6px;
  font-weight: 500;
  cursor: pointer;
  transition: all 0.2s ease;
}

.primary-btn:hover {
  background: var(--color-primary-dark);
  transform: translateY(-1px);
}

.secondary-btn {
  padding: 0.75rem 1rem;
  background: var(--color-background);
  color: var(--color-text);
  border: 1px solid var(--color-border);
  border-radius: 6px;
  cursor: pointer;
  transition: all 0.2s ease;
}

.secondary-btn:hover {
  background: var(--color-background-soft);
}

.analytics-section {
  background: var(--color-background);
  border-bottom: 1px solid var(--color-border);
  padding: 2rem;
}

.analytics-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 1.5rem;
}

.analytics-header h2 {
  margin: 0;
  color: var(--color-text);
}

.close-btn {
  background: none;
  border: none;
  font-size: 1.25rem;
  cursor: pointer;
  color: var(--color-text-light);
}

.analytics-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
  gap: 1.5rem;
}

.analytics-card {
  background: var(--color-background-soft);
  border: 1px solid var(--color-border);
  border-radius: 8px;
  padding: 1.5rem;
}

.analytics-card h3 {
  margin: 0 0 1rem 0;
  color: var(--color-text);
  font-size: 1.125rem;
}

.analytics-content p {
  margin: 0.5rem 0;
  color: var(--color-text);
}

.category-chart {
  display: flex;
  flex-direction: column;
  gap: 0.75rem;
}

.category-bar {
  display: flex;
  flex-direction: column;
  gap: 0.25rem;
}

.category-info {
  display: flex;
  justify-content: space-between;
  font-size: 0.875rem;
  color: var(--color-text);
}

.category-progress {
  height: 6px;
  background: var(--color-background);
  border-radius: 3px;
  overflow: hidden;
}

.progress-fill {
  height: 100%;
  background: var(--category-color);
  width: var(--completion-rate);
  transition: width 0.3s ease;
}

.priority-chart {
  display: flex;
  flex-direction: column;
  gap: 0.75rem;
}

.priority-item {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 0.5rem;
  border-radius: 4px;
}

.priority-item.high { background: var(--color-danger-light); }
.priority-item.medium { background: var(--color-warning-light); }
.priority-item.low { background: var(--color-success-light); }

.deadlines-list {
  display: flex;
  flex-direction: column;
  gap: 0.5rem;
}

.deadline-item {
  display: flex;
  justify-content: space-between;
  padding: 0.5rem;
  border-left: 3px solid var(--color-warning);
  background: var(--color-warning-light);
  border-radius: 0 4px 4px 0;
}

.deadline-item.urgent {
  border-left-color: var(--color-danger);
  background: var(--color-danger-light);
}

.deadline-task {
  font-weight: 500;
  color: var(--color-text);
}

.deadline-time {
  font-size: 0.875rem;
  color: var(--color-text-light);
}

.no-deadlines {
  text-align: center;
  color: var(--color-text-light);
  font-style: italic;
  padding: 1rem;
}

.controls-section {
  background: var(--color-background);
  border-bottom: 1px solid var(--color-border);
  padding: 1.5rem 2rem;
}

.filters-row {
  display: flex;
  gap: 1rem;
  align-items: center;
  flex-wrap: wrap;
  margin-bottom: 1rem;
}

.filter-group {
  display: flex;
  flex-direction: column;
  gap: 0.25rem;
}

.search-input,
.filter-select {
  padding: 0.5rem;
  border: 1px solid var(--color-border);
  border-radius: 4px;
  background: var(--color-background);
  color: var(--color-text);
  min-width: 150px;
}

.search-input:focus,
.filter-select:focus {
  outline: none;
  border-color: var(--color-primary);
}

.clear-filters-btn {
  padding: 0.5rem 1rem;
  background: var(--color-secondary);
  color: white;
  border: none;
  border-radius: 4px;
  cursor: pointer;
  font-size: 0.875rem;
}

.view-controls {
  display: flex;
  justify-content: space-between;
  align-items: center;
  flex-wrap: wrap;
  gap: 1rem;
}

.view-modes {
  display: flex;
  border: 1px solid var(--color-border);
  border-radius: 6px;
  overflow: hidden;
}

.view-mode-btn {
  padding: 0.5rem 1rem;
  border: none;
  background: var(--color-background);
  color: var(--color-text);
  cursor: pointer;
  transition: all 0.2s ease;
  border-right: 1px solid var(--color-border);
}

.view-mode-btn:last-child {
  border-right: none;
}

.view-mode-btn.active {
  background: var(--color-primary);
  color: white;
}

.selection-info {
  display: flex;
  align-items: center;
  gap: 0.5rem;
  padding: 0.5rem 1rem;
  background: var(--color-primary-light);
  border-radius: 6px;
  border: 1px solid var(--color-primary);
}

.bulk-action-btn {
  padding: 0.25rem 0.75rem;
  border: none;
  border-radius: 4px;
  cursor: pointer;
  font-size: 0.875rem;
  background: var(--color-background);
  color: var(--color-text);
}

.bulk-action-btn.danger {
  background: var(--color-danger);
  color: white;
}

.analytics-toggle {
  padding: 0.5rem 1rem;
  background: var(--color-background);
  border: 1px solid var(--color-border);
  border-radius: 4px;
  cursor: pointer;
  color: var(--color-text);
}

.templates-section {
  background: var(--color-background-soft);
  padding: 1.5rem 2rem;
  border-bottom: 1px solid var(--color-border);
}

.templates-section h3 {
  margin: 0 0 1rem 0;
  color: var(--color-text);
}

.templates-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
  gap: 1rem;
}

.template-card {
  background: var(--color-background);
  border: 1px solid var(--color-border);
  border-radius: 6px;
  padding: 1rem;
  cursor: pointer;
  transition: all 0.2s ease;
}

.template-card:hover {
  border-color: var(--color-primary);
  transform: translateY(-1px);
  box-shadow: 0 2px 8px rgba(0,0,0,0.1);
}

.template-title {
  font-weight: 500;
  margin-bottom: 0.5rem;
  color: var(--color-text);
}

.template-meta {
  display: flex;
  gap: 0.5rem;
  font-size: 0.75rem;
  color: var(--color-text-light);
}

.tasks-main {
  flex: 1;
}

/* Responsive Design */
@media (max-width: 768px) {
  .header-content {
    flex-direction: column;
    align-items: stretch;
  }
  
  .quick-stats {
    justify-content: center;
  }
  
  .filters-row {
    flex-direction: column;
    align-items: stretch;
  }
  
  .view-controls {
    flex-direction: column;
    align-items: stretch;
  }
  
  .analytics-grid {
    grid-template-columns: 1fr;
  }
  
  .templates-grid {
    grid-template-columns: 1fr;
  }
}
</style>
```

### Success Criteria
- [ ] Multiple Pinia stores working together seamlessly
- [ ] Complex state management with proper reactivity
- [ ] Analytics tracking system functional
- [ ] Notification system integrated
- [ ] Data persistence with localStorage working
- [ ] Import/export functionality operational
- [ ] Real-time dashboard updates
- [ ] Advanced filtering and sorting capabilities

### 💡 Key Learning Points

1. **Store Architecture**: How to organize multiple stores for complex applications
2. **State Composition**: Combining different stores to create rich functionality
3. **Reactive Data Flow**: Understanding how Pinia's reactivity works across components
4. **Performance Optimization**: Best practices for store structure and computed properties
5. **Data Persistence**: Implementing proper save/load functionality

### 🎯 Looking Ahead

In Chapter 12, you'll learn about API integration and HTTP communication, connecting your Vue.js application with real backend services!

**Your advanced state management system is complete** - experience the power of centralized application state!