# Chapter 9: Vue.js Component Templates and Directives

## Learning Objectives
By the end of this chapter, you will understand:
- How Vue.js templates work and their relationship to HTML
- Essential Vue.js directives for dynamic content
- Conditional rendering and list rendering patterns
- Event binding and form handling
- Template syntax best practices

## Understanding Vue.js Templates

Vue.js templates are **HTML with superpowers** - they can automatically update based on your data.

### Template vs Static HTML

```
Static HTML:
┌─────────────────┐
│ <div>           │
│   <h1>Title</h1>│  ← Fixed content
│   <p>Text</p>   │  ← Never changes
│ </div>          │
└─────────────────┘

Vue.js Template:
┌─────────────────┐
│ <div>           │
│   <h1>{{title}}</h1>│  ← Dynamic content
│   <p>{{text}}</p>   │  ← Updates automatically
│ </div>          │
└─────────────────┘
```

## Text Interpolation

```vue
<template>
  <div class="drum-display">
    <!-- Basic interpolation -->
    <h2>{{ drumName }}</h2>
    <p>Temperature: {{ temperature }}°C</p>
    
    <!-- Expressions -->
    <p>Progress: {{ (currentLayer / totalLayers * 100).toFixed(1) }}%</p>
    
    <!-- Method calls -->
    <p>Updated: {{ formatTime(lastUpdate) }}</p>
  </div>
</template>
```

## Essential Directives

### 1. v-bind (Attribute Binding)

```vue
<template>
  <!-- Shorthand: :attribute -->
  <img :src="drumImage" :alt="drumName">
  <button :disabled="isLoading" :class="buttonClass">
    {{ buttonText }}
  </button>
  
  <!-- Dynamic styles -->
  <div :style="{ color: temperature > 250 ? 'red' : 'green' }">
    Temperature: {{ temperature }}°C
  </div>
</template>
```

### 2. v-if/v-else (Conditional Rendering)

```vue
<template>
  <div>
    <div v-if="status === 'idle'" class="status-idle">
      🟡 Ready to start
    </div>
    <div v-else-if="status === 'printing'" class="status-printing">
      🖨️ Printing layer {{ currentLayer }}
    </div>
    <div v-else-if="status === 'error'" class="status-error">
      ❌ Error: {{ errorMessage }}
    </div>
    <div v-else>
      Status: {{ status }}
    </div>
  </div>
</template>
```

### 3. v-for (List Rendering)

```vue
<template>
  <div class="drum-list">
    <!-- Basic list -->
    <div v-for="drum in drums" :key="drum.id" class="drum-card">
      <h3>{{ drum.name }}</h3>
      <p>Status: {{ drum.status }}</p>
    </div>
    
    <!-- With index -->
    <div v-for="(layer, index) in layers" :key="layer.id">
      {{ index + 1 }}. {{ layer.name }}
    </div>
    
    <!-- Object properties -->
    <div v-for="(value, key) in config" :key="key">
      {{ key }}: {{ value }}
    </div>
  </div>
</template>
```

### 4. v-on (Event Handling)

```vue
<template>
  <div class="controls">
    <!-- Basic events -->
    <button @click="startDrum">Start</button>
    <button @click="stopDrum">Stop</button>
    
    <!-- With parameters -->
    <button @click="selectDrum(0)">Drum 1</button>
    
    <!-- Event modifiers -->
    <form @submit.prevent="handleSubmit">
      <input @keyup.enter="handleEnter" v-model="input">
      <button type="submit">Save</button>
    </form>
    
    <!-- Stop propagation -->
    <div @click="outerClick">
      <button @click.stop="innerClick">Inner</button>
    </div>
  </div>
</template>
```

## Form Input Binding

### v-model for Two-Way Data Binding

```vue
<template>
  <div class="form">
    <!-- Text input -->
    <input v-model="drumName" type="text" placeholder="Drum name">
    
    <!-- Number input -->
    <input v-model.number="temperature" type="number" min="100" max="300">
    
    <!-- Select dropdown -->
    <select v-model="selectedMaterial">
      <option value="">Choose material...</option>
      <option v-for="material in materials" :key="material.id" :value="material.id">
        {{ material.name }}
      </option>
    </select>
    
    <!-- Checkbox -->
    <label>
      <input v-model="autoStart" type="checkbox">
      Auto-start after heating
    </label>
    
    <!-- Radio buttons -->
    <div v-for="quality in printQualities" :key="quality.value">
      <label>
        <input v-model="printQuality" :value="quality.value" type="radio">
        {{ quality.label }}
      </label>
    </div>
  </div>
</template>

<script>
export default {
  setup() {
    const drumName = ref('')
    const temperature = ref(200)
    const selectedMaterial = ref('')
    const autoStart = ref(false)
    const printQuality = ref('normal')
    
    const materials = ref([
      { id: 'pla', name: 'PLA' },
      { id: 'abs', name: 'ABS' }
    ])
    
    const printQualities = ref([
      { value: 'draft', label: 'Draft' },
      { value: 'normal', label: 'Normal' },
      { value: 'high', label: 'High Quality' }
    ])
    
    return {
      drumName,
      temperature,
      selectedMaterial,
      autoStart,
      printQuality,
      materials,
      printQualities
    }
  }
}
</script>
```

## Real-World Example: Layer Preview Interface

```vue
<template>
  <div class="layer-preview">
    <!-- Header with dynamic title -->
    <h2>{{ previewTitle }}</h2>
    
    <!-- Connection status -->
    <div :class="connectionClass">
      {{ connectionText }}
    </div>
    
    <!-- Drum selector -->
    <div class="drum-selector">
      <button 
        v-for="drum in availableDrums" 
        :key="drum.id"
        :class="{ active: selectedDrum === drum.id }"
        @click="selectDrum(drum.id)"
      >
        Drum {{ drum.id + 1 }}
      </button>
    </div>
    
    <!-- Layer input (conditional) -->
    <div v-if="selectedDrum !== null" class="layer-input">
      <label>Layer Number:</label>
      <input 
        v-model.number="layerNumber" 
        type="number" 
        :min="1" 
        :max="maxLayers"
        @input="validateLayer"
      >
      <span v-if="layerError" class="error">{{ layerError }}</span>
    </div>
    
    <!-- Load button -->
    <button 
      @click="loadPreview" 
      :disabled="!canLoadPreview"
      :class="buttonClass"
    >
      {{ buttonText }}
    </button>
    
    <!-- Preview image -->
    <div v-if="previewImageUrl" class="preview-container">
      <img :src="previewImageUrl" alt="Layer Preview">
    </div>
    
    <!-- Error message -->
    <div v-if="errorMessage" class="error-message">
      {{ errorMessage }}
    </div>
  </div>
</template>

<script>
import { ref, computed } from 'vue'

export default {
  setup() {
    const selectedDrum = ref(null)
    const layerNumber = ref(1)
    const maxLayers = ref(100)
    const isLoading = ref(false)
    const previewImageUrl = ref(null)
    const errorMessage = ref('')
    const layerError = ref('')
    const isConnected = ref(true)
    
    const availableDrums = ref([
      { id: 0, name: 'Drum 1' },
      { id: 1, name: 'Drum 2' },
      { id: 2, name: 'Drum 3' }
    ])
    
    // Computed properties
    const previewTitle = computed(() => {
      if (selectedDrum.value !== null) {
        return `Layer ${layerNumber.value} - Drum ${selectedDrum.value + 1}`
      }
      return 'Layer Preview'
    })
    
    const connectionClass = computed(() => ({
      'connection-status': true,
      'connected': isConnected.value,
      'disconnected': !isConnected.value
    }))
    
    const connectionText = computed(() => {
      return isConnected.value ? '✅ Connected' : '❌ Disconnected'
    })
    
    const canLoadPreview = computed(() => {
      return selectedDrum.value !== null && 
             !isLoading.value && 
             !layerError.value &&
             isConnected.value
    })
    
    const buttonClass = computed(() => ({
      'load-btn': true,
      'loading': isLoading.value,
      'disabled': !canLoadPreview.value
    }))
    
    const buttonText = computed(() => {
      if (isLoading.value) return 'Loading...'
      if (!isConnected.value) return 'Not Connected'
      if (selectedDrum.value === null) return 'Select Drum'
      return 'Load Preview'
    })
    
    // Methods
    const selectDrum = (drumId) => {
      selectedDrum.value = drumId
      layerNumber.value = 1
      previewImageUrl.value = null
      errorMessage.value = ''
    }
    
    const validateLayer = () => {
      if (layerNumber.value < 1) {
        layerError.value = 'Layer must be at least 1'
      } else if (layerNumber.value > maxLayers.value) {
        layerError.value = `Layer cannot exceed ${maxLayers.value}`
      } else {
        layerError.value = ''
      }
    }
    
    const loadPreview = async () => {
      if (!canLoadPreview.value) return
      
      isLoading.value = true
      errorMessage.value = ''
      
      try {
        // Simulate API call
        await new Promise(resolve => setTimeout(resolve, 1000))
        previewImageUrl.value = `/api/preview/drum${selectedDrum.value}/layer${layerNumber.value}.png`
      } catch (error) {
        errorMessage.value = 'Failed to load preview'
      } finally {
        isLoading.value = false
      }
    }
    
    return {
      selectedDrum,
      layerNumber,
      maxLayers,
      isLoading,
      previewImageUrl,
      errorMessage,
      layerError,
      isConnected,
      availableDrums,
      previewTitle,
      connectionClass,
      connectionText,
      canLoadPreview,
      buttonClass,
      buttonText,
      selectDrum,
      validateLayer,
      loadPreview
    }
  }
}
</script>
```

## Best Practices

### Template Organization
```vue
<!-- ✅ GOOD: Clear structure -->
<template>
  <div class="component-container">
    <!-- Header section -->
    <header>
      <h1>{{ title }}</h1>
    </header>
    
    <!-- Main content -->
    <main>
      <div v-if="loading">Loading...</div>
      <div v-else>{{ content }}</div>
    </main>
    
    <!-- Footer section -->
    <footer>
      <button @click="action">Action</button>
    </footer>
  </div>
</template>
```

### Performance Tips
- Use `v-show` for frequently toggled elements
- Use `v-if` for rarely shown content
- Always provide `:key` for `v-for` items
- Keep expressions in templates simple
- Use computed properties for complex logic

## Key Takeaways

1. **Templates are reactive HTML** that automatically update with data changes
2. **Directives** (`v-if`, `v-for`, `v-on`) add dynamic behavior to templates
3. **v-model** creates two-way data binding for forms
4. **Event handling** with modifiers provides powerful interaction patterns
5. **Computed properties** keep templates clean and performant

## Next Steps

In Chapter 10, we'll dive deeper into **Event Handling and User Interaction**, covering:
- Advanced event patterns
- Custom events between components
- Form validation techniques
- Keyboard and mouse interaction patterns

---

## 🚀 Mini Project: Dynamic Template System

### Exercise 9.1: Create Advanced Task Form Component

Build a sophisticated task form that demonstrates all template features. Create `src/components/TaskForm.vue`:

```vue
<template>
  <form 
    class="task-form" 
    @submit.prevent="handleSubmit"
    @keydown.ctrl.enter="handleSubmit"
    novalidate
  >
    <header class="form-header">
      <h2 class="form-title">
        {{ isEditing ? 'Edit Task' : 'Create New Task' }}
      </h2>
      <div class="form-mode-indicator" :class="formModeClass">
        {{ formModeText }}
      </div>
    </header>

    <!-- Task Title Section -->
    <fieldset class="form-section">
      <legend class="section-title">Task Details</legend>
      
      <div class="input-group" :class="{ 'has-error': errors.title }">
        <label for="task-title" class="input-label required">
          Task Title
        </label>
        <input
          id="task-title"
          ref="titleInput"
          v-model.trim="formData.title"
          type="text"
          class="form-input"
          placeholder="Enter a descriptive task title..."
          maxlength="200"
          :aria-describedby="errors.title ? 'title-error' : 'title-help'"
          :aria-invalid="!!errors.title"
          @blur="validateField('title')"
          @input="clearFieldError('title')"
        />
        <div id="title-help" class="input-help" v-if="!errors.title">
          {{ formData.title.length }}/200 characters
        </div>
        <div id="title-error" class="input-error" v-if="errors.title">
          {{ errors.title }}
        </div>
      </div>

      <div class="input-group">
        <label for="task-description" class="input-label">
          Description <span class="optional">(optional)</span>
        </label>
        <textarea
          id="task-description"
          v-model.trim="formData.description"
          class="form-textarea"
          placeholder="Add more details about this task..."
          rows="3"
          maxlength="1000"
          @input="autoResize"
        ></textarea>
        <div class="input-help">
          {{ formData.description.length }}/1000 characters
        </div>
      </div>
    </fieldset>

    <!-- Task Properties Section -->
    <fieldset class="form-section">
      <legend class="section-title">Task Properties</legend>
      
      <div class="form-row">
        <div class="input-group">
          <label for="task-category" class="input-label">Category</label>
          <select
            id="task-category"
            v-model="formData.category"
            class="form-select"
          >
            <option value="">Select a category</option>
            <option 
              v-for="category in categories"
              :key="category.id"
              :value="category.id"
            >
              {{ category.emoji }} {{ category.name }}
            </option>
          </select>
        </div>

        <div class="input-group">
          <label for="task-priority" class="input-label">Priority</label>
          <div class="priority-selector">
            <label 
              v-for="priority in priorityOptions"
              :key="priority.value"
              class="priority-option"
              :class="{ 'selected': formData.priority === priority.value }"
            >
              <input
                type="radio"
                :value="priority.value"
                v-model="formData.priority"
                class="priority-radio"
              />
              <span class="priority-icon">{{ priority.icon }}</span>
              <span class="priority-label">{{ priority.label }}</span>
            </label>
          </div>
        </div>
      </div>

      <div class="form-row">
        <div class="input-group">
          <label for="task-due-date" class="input-label">Due Date</label>
          <input
            id="task-due-date"
            v-model="formData.dueDate"
            type="date"
            class="form-input"
            :min="todayDate"
            :max="maxDate"
          />
          <div class="input-help" v-if="formData.dueDate">
            {{ formatDueDateHelp(formData.dueDate) }}
          </div>
        </div>

        <div class="input-group">
          <label for="task-estimated-time" class="input-label">
            Estimated Time <span class="optional">(optional)</span>
          </label>
          <div class="time-input-group">
            <input
              id="task-estimated-time"
              v-model.number="formData.estimatedHours"
              type="number"
              min="0"
              max="999"
              step="0.5"
              class="form-input time-input"
              placeholder="0"
            />
            <span class="time-unit">hours</span>
          </div>
        </div>
      </div>
    </fieldset>

    <!-- Advanced Options Section -->
    <fieldset class="form-section" v-show="showAdvancedOptions">
      <legend class="section-title">Advanced Options</legend>
      
      <div class="checkbox-group">
        <label class="checkbox-label">
          <input
            type="checkbox"
            v-model="formData.isRecurring"
            class="form-checkbox"
          />
          <span class="checkbox-text">Recurring Task</span>
        </label>

        <div v-if="formData.isRecurring" class="recurring-options">
          <label for="recurrence-pattern" class="input-label">Repeat</label>
          <select
            id="recurrence-pattern"
            v-model="formData.recurrencePattern"
            class="form-select"
          >
            <option value="daily">Daily</option>
            <option value="weekly">Weekly</option>
            <option value="monthly">Monthly</option>
          </select>
        </div>
      </div>

      <div class="checkbox-group">
        <label class="checkbox-label">
          <input
            type="checkbox"
            v-model="formData.isImportant"
            class="form-checkbox"
          />
          <span class="checkbox-text">Mark as Important</span>
        </label>

        <label class="checkbox-label">
          <input
            type="checkbox"
            v-model="formData.hasReminder"
            class="form-checkbox"
          />
          <span class="checkbox-text">Set Reminder</span>
        </label>
      </div>

      <div class="input-group">
        <label for="task-tags" class="input-label">
          Tags <span class="optional">(comma-separated)</span>
        </label>
        <input
          id="task-tags"
          v-model="tagsInput"
          type="text"
          class="form-input"
          placeholder="work, urgent, project-alpha..."
          @blur="processTags"
        />
        <div class="tags-preview" v-if="formData.tags.length > 0">
          <span 
            v-for="tag in formData.tags"
            :key="tag"
            class="tag-pill"
          >
            {{ tag }}
            <button 
              type="button" 
              @click="removeTag(tag)"
              class="tag-remove"
              :aria-label="`Remove ${tag} tag`"
            >
              ×
            </button>
          </span>
        </div>
      </div>
    </fieldset>

    <!-- Form Actions -->
    <footer class="form-actions">
      <div class="action-group">
        <button
          type="button"
          @click="toggleAdvancedOptions"
          class="btn btn-link"
        >
          {{ showAdvancedOptions ? '🔽 Hide' : '🔼 Show' }} Advanced Options
        </button>
      </div>

      <div class="action-group">
        <button
          v-if="isEditing"
          type="button"
          @click="handleCancel"
          class="btn btn-secondary"
        >
          Cancel
        </button>
        
        <button
          type="button"
          @click="handleClear"
          class="btn btn-secondary"
          :disabled="isFormEmpty"
        >
          Clear Form
        </button>
        
        <button
          type="submit"
          class="btn btn-primary"
          :disabled="!isFormValid || isSubmitting"
        >
          <span v-if="isSubmitting">
            ⏳ {{ isEditing ? 'Updating...' : 'Creating...' }}
          </span>
          <span v-else>
            {{ isEditing ? '💾 Update Task' : '➕ Create Task' }}
          </span>
        </button>
      </div>
    </footer>

    <!-- Form Validation Summary -->
    <div class="validation-summary" v-if="hasErrors && showValidationSummary">
      <h3 class="validation-title">Please fix the following errors:</h3>
      <ul class="validation-list">
        <li v-for="(error, field) in errors" :key="field" class="validation-item">
          <strong>{{ getFieldLabel(field) }}:</strong> {{ error }}
        </li>
      </ul>
    </div>

    <!-- Keyboard Shortcuts Help -->
    <div class="keyboard-shortcuts" v-if="showKeyboardHelp">
      <h4>⌨️ Keyboard Shortcuts:</h4>
      <ul>
        <li><kbd>Ctrl + Enter</kbd> - Submit form</li>
        <li><kbd>Escape</kbd> - Clear form / Cancel edit</li>
        <li><kbd>Tab</kbd> - Navigate fields</li>
        <li><kbd>Ctrl + /</kbd> - Toggle this help</li>
      </ul>
    </div>
  </form>
</template>

<script setup>
import { ref, reactive, computed, watch, nextTick, onMounted, onUnmounted } from 'vue'
import { useDateFormatting } from '../composables/useDateFormatting'
import { useTaskValidation } from '../composables/useTaskValidation'

// Props
const props = defineProps({
  initialData: {
    type: Object,
    default: () => ({})
  },
  categories: {
    type: Array,
    default: () => []
  },
  isEditing: {
    type: Boolean,
    default: false
  }
})

// Emits
const emit = defineEmits(['task-submitted', 'form-cancelled', 'form-cleared'])

// Composables
const { formatDueDate } = useDateFormatting()
const { validateTask } = useTaskValidation()

// Form state
const formData = reactive({
  title: '',
  description: '',
  category: '',
  priority: 'medium',
  dueDate: '',
  estimatedHours: 0,
  isRecurring: false,
  recurrencePattern: 'weekly',
  isImportant: false,
  hasReminder: false,
  tags: []
})

const errors = reactive({})
const isSubmitting = ref(false)
const showAdvancedOptions = ref(false)
const showValidationSummary = ref(false)
const showKeyboardHelp = ref(false)
const titleInput = ref(null)
const tagsInput = ref('')

// Priority options
const priorityOptions = [
  { value: 'low', label: 'Low', icon: '🟢' },
  { value: 'medium', label: 'Medium', icon: '🟡' },
  { value: 'high', label: 'High', icon: '🔴' }
]

// Computed properties
const isFormValid = computed(() => {
  return formData.title.trim().length >= 3 && Object.keys(errors).length === 0
})

const isFormEmpty = computed(() => {
  return !formData.title.trim() &&
         !formData.description.trim() &&
         !formData.category &&
         formData.priority === 'medium' &&
         !formData.dueDate &&
         formData.estimatedHours === 0 &&
         formData.tags.length === 0
})

const hasErrors = computed(() => {
  return Object.keys(errors).length > 0
})

const formModeClass = computed(() => ({
  'mode-create': !props.isEditing,
  'mode-edit': props.isEditing
}))

const formModeText = computed(() => {
  return props.isEditing ? 'Editing existing task' : 'Creating new task'
})

const todayDate = computed(() => {
  return new Date().toISOString().split('T')[0]
})

const maxDate = computed(() => {
  const maxDate = new Date()
  maxDate.setFullYear(maxDate.getFullYear() + 2)
  return maxDate.toISOString().split('T')[0]
})

// Methods
const validateField = (fieldName) => {
  const fieldValue = formData[fieldName]
  
  switch (fieldName) {
    case 'title':
      if (!fieldValue || fieldValue.trim().length === 0) {
        errors[fieldName] = 'Task title is required'
      } else if (fieldValue.trim().length < 3) {
        errors[fieldName] = 'Task title must be at least 3 characters'
      } else if (fieldValue.length > 200) {
        errors[fieldName] = 'Task title cannot exceed 200 characters'
      } else {
        delete errors[fieldName]
      }
      break
      
    case 'dueDate':
      if (fieldValue) {
        const dueDate = new Date(fieldValue)
        const today = new Date()
        today.setHours(0, 0, 0, 0)
        
        if (dueDate < today) {
          errors[fieldName] = 'Due date cannot be in the past'
        } else {
          delete errors[fieldName]
        }
      } else {
        delete errors[fieldName]
      }
      break
  }
}

const clearFieldError = (fieldName) => {
  if (errors[fieldName]) {
    delete errors[fieldName]
  }
}

const getFieldLabel = (fieldName) => {
  const labels = {
    title: 'Task Title',
    description: 'Description',
    dueDate: 'Due Date',
    category: 'Category',
    priority: 'Priority'
  }
  return labels[fieldName] || fieldName
}

const formatDueDateHelp = (dateString) => {
  const date = new Date(dateString)
  const today = new Date()
  const diffTime = date.getTime() - today.getTime()
  const diffDays = Math.ceil(diffTime / (1000 * 60 * 60 * 24))
  
  if (diffDays === 0) return 'Due today'
  if (diffDays === 1) return 'Due tomorrow'
  if (diffDays > 1) return `Due in ${diffDays} days`
  return formatDueDate(dateString)
}

const autoResize = (event) => {
  const textarea = event.target
  textarea.style.height = 'auto'
  textarea.style.height = textarea.scrollHeight + 'px'
}

const processTags = () => {
  if (tagsInput.value.trim()) {
    const newTags = tagsInput.value
      .split(',')
      .map(tag => tag.trim().toLowerCase())
      .filter(tag => tag.length > 0 && !formData.tags.includes(tag))
    
    formData.tags.push(...newTags)
    tagsInput.value = ''
  }
}

const removeTag = (tagToRemove) => {
  const index = formData.tags.indexOf(tagToRemove)
  if (index > -1) {
    formData.tags.splice(index, 1)
  }
}

const toggleAdvancedOptions = () => {
  showAdvancedOptions.value = !showAdvancedOptions.value
}

const handleSubmit = async () => {
  // Validate all fields
  validateField('title')
  validateField('dueDate')
  
  if (!isFormValid.value) {
    showValidationSummary.value = true
    return
  }
  
  isSubmitting.value = true
  showValidationSummary.value = false
  
  try {
    // Process tags one more time
    processTags()
    
    // Create submission data
    const submissionData = {
      ...formData,
      // Ensure tags is always an array
      tags: [...formData.tags],
      // Convert estimated hours to number or null
      estimatedHours: formData.estimatedHours > 0 ? formData.estimatedHours : null,
      // Ensure boolean values
      isRecurring: Boolean(formData.isRecurring),
      isImportant: Boolean(formData.isImportant),
      hasReminder: Boolean(formData.hasReminder)
    }
    
    emit('task-submitted', submissionData)
    
    // Clear form if creating new task
    if (!props.isEditing) {
      handleClear()
    }
    
  } catch (error) {
    console.error('Form submission error:', error)
  } finally {
    isSubmitting.value = false
  }
}

const handleCancel = () => {
  if (props.isEditing) {
    emit('form-cancelled')
  }
  resetForm()
}

const handleClear = () => {
  resetForm()
  emit('form-cleared')
}

const resetForm = () => {
  Object.assign(formData, {
    title: '',
    description: '',
    category: '',
    priority: 'medium',
    dueDate: '',
    estimatedHours: 0,
    isRecurring: false,
    recurrencePattern: 'weekly',
    isImportant: false,
    hasReminder: false,
    tags: []
  })
  
  Object.keys(errors).forEach(key => delete errors[key])
  tagsInput.value = ''
  showValidationSummary.value = false
  showAdvancedOptions.value = false
}

const handleKeyboardShortcuts = (e) => {
  // Ctrl/Cmd + / : Toggle keyboard help
  if ((e.ctrlKey || e.metaKey) && e.key === '/') {
    e.preventDefault()
    showKeyboardHelp.value = !showKeyboardHelp.value
  }
  
  // Escape: Clear form or cancel edit
  if (e.key === 'Escape') {
    e.preventDefault()
    if (showKeyboardHelp.value) {
      showKeyboardHelp.value = false
    } else if (props.isEditing) {
      handleCancel()
    } else {
      handleClear()
    }
  }
}

// Watchers
watch(() => props.initialData, (newData) => {
  if (newData && Object.keys(newData).length > 0) {
    Object.assign(formData, newData)
    if (newData.tags && Array.isArray(newData.tags)) {
      formData.tags = [...newData.tags]
    }
  }
}, { immediate: true, deep: true })

// Lifecycle
onMounted(async () => {
  document.addEventListener('keydown', handleKeyboardShortcuts)
  
  // Focus title input
  await nextTick()
  if (titleInput.value) {
    titleInput.value.focus()
  }
})

onUnmounted(() => {
  document.removeEventListener('keydown', handleKeyboardShortcuts)
})
</script>

<style scoped>
.task-form {
  max-width: 600px;
  margin: 0 auto;
  padding: 2rem;
  background: var(--color-background);
  border-radius: 8px;
  box-shadow: 0 2px 8px rgba(0,0,0,0.1);
}

.form-header {
  margin-bottom: 2rem;
  text-align: center;
}

.form-title {
  margin: 0 0 0.5rem 0;
  color: var(--color-text);
}

.form-mode-indicator {
  padding: 0.25rem 0.75rem;
  border-radius: 4px;
  font-size: 0.875rem;
  font-weight: 500;
}

.mode-create {
  background: var(--color-success-light);
  color: var(--color-success-dark);
}

.mode-edit {
  background: var(--color-warning-light);
  color: var(--color-warning-dark);
}

.form-section {
  margin-bottom: 2rem;
  border: none;
  padding: 0;
}

.section-title {
  margin-bottom: 1rem;
  font-size: 1.125rem;
  font-weight: 600;
  color: var(--color-text);
  border-bottom: 2px solid var(--color-primary);
  padding-bottom: 0.5rem;
}

.input-group {
  margin-bottom: 1.5rem;
}

.input-label {
  display: block;
  margin-bottom: 0.5rem;
  font-weight: 500;
  color: var(--color-text);
}

.required::after {
  content: " *";
  color: var(--color-danger);
}

.optional {
  font-weight: normal;
  color: var(--color-text-light);
  font-size: 0.875rem;
}

.form-input,
.form-textarea,
.form-select {
  width: 100%;
  padding: 0.75rem;
  border: 2px solid var(--color-border);
  border-radius: 4px;
  font-size: 1rem;
  transition: border-color 0.2s ease;
}

.form-input:focus,
.form-textarea:focus,
.form-select:focus {
  outline: none;
  border-color: var(--color-primary);
}

.has-error .form-input,
.has-error .form-textarea,
.has-error .form-select {
  border-color: var(--color-danger);
}

.input-help {
  margin-top: 0.25rem;
  font-size: 0.875rem;
  color: var(--color-text-light);
}

.input-error {
  margin-top: 0.25rem;
  font-size: 0.875rem;
  color: var(--color-danger);
}

.form-row {
  display: grid;
  grid-template-columns: 1fr 1fr;
  gap: 1rem;
}

.priority-selector {
  display: flex;
  gap: 0.5rem;
  flex-wrap: wrap;
}

.priority-option {
  display: flex;
  align-items: center;
  gap: 0.5rem;
  padding: 0.5rem 0.75rem;
  border: 2px solid var(--color-border);
  border-radius: 4px;
  cursor: pointer;
  transition: all 0.2s ease;
}

.priority-option.selected {
  border-color: var(--color-primary);
  background: var(--color-primary-light);
}

.priority-radio {
  display: none;
}

.time-input-group {
  display: flex;
  align-items: center;
  gap: 0.5rem;
}

.time-input {
  width: 100px;
}

.time-unit {
  color: var(--color-text-light);
  font-size: 0.875rem;
}

.checkbox-group {
  margin-bottom: 1rem;
}

.checkbox-label {
  display: flex;
  align-items: center;
  gap: 0.5rem;
  margin-bottom: 0.5rem;
  cursor: pointer;
}

.form-checkbox {
  width: 1.25rem;
  height: 1.25rem;
}

.recurring-options {
  margin-left: 1.75rem;
  margin-top: 0.5rem;
}

.tags-preview {
  margin-top: 0.5rem;
  display: flex;
  flex-wrap: wrap;
  gap: 0.5rem;
}

.tag-pill {
  display: inline-flex;
  align-items: center;
  gap: 0.25rem;
  padding: 0.25rem 0.5rem;
  background: var(--color-primary-light);
  color: var(--color-primary-dark);
  border-radius: 12px;
  font-size: 0.875rem;
}

.tag-remove {
  background: none;
  border: none;
  color: inherit;
  cursor: pointer;
  font-size: 1rem;
  line-height: 1;
}

.form-actions {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-top: 2rem;
  padding-top: 1rem;
  border-top: 1px solid var(--color-border);
}

.action-group {
  display: flex;
  gap: 0.75rem;
}

.btn {
  padding: 0.75rem 1.5rem;
  border: none;
  border-radius: 4px;
  font-size: 0.875rem;
  font-weight: 500;
  cursor: pointer;
  transition: all 0.2s ease;
}

.btn-primary {
  background: var(--color-primary);
  color: white;
}

.btn-primary:hover:not(:disabled) {
  background: var(--color-primary-dark);
}

.btn-secondary {
  background: var(--color-background);
  color: var(--color-text);
  border: 1px solid var(--color-border);
}

.btn-link {
  background: none;
  color: var(--color-primary);
  text-decoration: none;
  padding: 0.5rem;
}

.btn:disabled {
  opacity: 0.5;
  cursor: not-allowed;
}

.validation-summary {
  margin-top: 1rem;
  padding: 1rem;
  background: var(--color-danger-light);
  border: 1px solid var(--color-danger);
  border-radius: 4px;
}

.validation-title {
  margin: 0 0 0.5rem 0;
  color: var(--color-danger);
  font-size: 0.875rem;
}

.validation-list {
  margin: 0;
  padding-left: 1rem;
}

.validation-item {
  color: var(--color-danger);
  font-size: 0.875rem;
}

.keyboard-shortcuts {
  margin-top: 1rem;
  padding: 1rem;
  background: var(--color-background-soft);
  border-radius: 4px;
  font-size: 0.875rem;
}

.keyboard-shortcuts h4 {
  margin: 0 0 0.5rem 0;
}

.keyboard-shortcuts ul {
  margin: 0;
  padding-left: 1rem;
}

kbd {
  background: var(--color-background);
  border: 1px solid var(--color-border);
  border-radius: 3px;
  padding: 0.125rem 0.25rem;
  font-size: 0.75rem;
  font-family: monospace;
}

@media (max-width: 640px) {
  .form-row {
    grid-template-columns: 1fr;
  }
  
  .form-actions {
    flex-direction: column;
    gap: 1rem;
  }
  
  .action-group {
    width: 100%;
    justify-content: center;
  }
}
</style>
```

### Exercise 9.2: Create Task Filters Component

Create `src/components/TaskFilters.vue` with advanced filtering capabilities:

```vue
<template>
  <section class="task-filters" role="search" aria-label="Task filters">
    <div class="filters-container">
      <!-- Search Input -->
      <div class="filter-group search-group">
        <label for="task-search" class="filter-label">
          🔍 Search Tasks
        </label>
        <div class="search-input-wrapper">
          <input
            id="task-search"
            :value="search"
            @input="$emit('update:search', $event.target.value)"
            type="text"
            class="search-input"
            placeholder="Search by title or description..."
            :aria-describedby="search ? 'search-results' : null"
          />
          <button
            v-if="search"
            @click="$emit('update:search', '')"
            class="clear-search"
            type="button"
            aria-label="Clear search"
          >
            ✕
          </button>
        </div>
        <div id="search-results" class="search-info" v-if="search">
          Found {{ filteredCount }} task{{ filteredCount !== 1 ? 's' : '' }}
        </div>
      </div>

      <!-- Filter Controls -->
      <div class="filter-controls">
        <!-- Category Filter -->
        <div class="filter-group">
          <label for="category-filter" class="filter-label">Category</label>
          <select
            id="category-filter"
            :value="category"
            @change="$emit('update:category', $event.target.value)"
            class="filter-select"
          >
            <option value="">All Categories</option>
            <option 
              v-for="cat in categories"
              :key="cat.id"
              :value="cat.id"
            >
              {{ cat.emoji }} {{ cat.name }}
            </option>
          </select>
        </div>

        <!-- Status Filter -->
        <div class="filter-group">
          <label for="status-filter" class="filter-label">Status</label>
          <select
            id="status-filter"
            :value="status"
            @change="$emit('update:status', $event.target.value)"
            class="filter-select"
          >
            <option value="">All Tasks</option>
            <option value="pending">📋 Pending</option>
            <option value="completed">✅ Completed</option>
            <option value="overdue">⚠️ Overdue</option>
          </select>
        </div>

        <!-- Priority Filter -->
        <div class="filter-group">
          <label for="priority-filter" class="filter-label">Priority</label>
          <div class="priority-filter-buttons">
            <button
              v-for="priorityOption in priorityOptions"
              :key="priorityOption.value"
              @click="togglePriority(priorityOption.value)"
              :class="['priority-btn', { 
                'active': priority === priorityOption.value,
                [`priority-${priorityOption.value}`]: true
              }]"
              type="button"
              :aria-pressed="priority === priorityOption.value"
            >
              {{ priorityOption.icon }} {{ priorityOption.label }}
            </button>
          </div>
        </div>

        <!-- Date Range Filter -->
        <div class="filter-group">
          <label class="filter-label">Due Date</label>
          <div class="date-filter-buttons">
            <button
              v-for="dateOption in dateOptions"
              :key="dateOption.value"
              @click="handleDateFilter(dateOption.value)"
              :class="['date-btn', { 'active': activeDateFilter === dateOption.value }]"
              type="button"
            >
              {{ dateOption.label }}
            </button>
          </div>
        </div>
      </div>

      <!-- Action Controls -->
      <div class="filter-actions">
        <button
          @click="handleClearFilters"
          class="action-btn clear-filters"
          type="button"
          :disabled="!hasActiveFilters"
        >
          🧹 Clear All Filters
        </button>

        <div class="bulk-actions" v-if="selectedTasks.length > 0">
          <span class="selection-count">
            {{ selectedTasks.length }} task{{ selectedTasks.length !== 1 ? 's' : '' }} selected
          </span>
          <button
            @click="$emit('clear-completed')"
            class="action-btn bulk-action"
            type="button"
          >
            🗑️ Delete Completed
          </button>
          <button
            @click="$emit('export-tasks')"
            class="action-btn bulk-action"
            type="button"
          >
            📤 Export Tasks
          </button>
        </div>
      </div>

      <!-- Active Filters Display -->
      <div class="active-filters" v-if="hasActiveFilters">
        <h3 class="active-filters-title">Active Filters:</h3>
        <div class="filter-pills">
          <span v-if="search" class="filter-pill">
            🔍 Search: "{{ search }}"
            <button @click="$emit('update:search', '')" class="pill-remove">×</button>
          </span>
          
          <span v-if="category" class="filter-pill">
            📁 Category: {{ getCategoryName(category) }}
            <button @click="$emit('update:category', '')" class="pill-remove">×</button>
          </span>
          
          <span v-if="status" class="filter-pill">
            📊 Status: {{ getStatusName(status) }}
            <button @click="$emit('update:status', '')" class="pill-remove">×</button>
          </span>
          
          <span v-if="priority" class="filter-pill">
            🎯 Priority: {{ getPriorityName(priority) }}
            <button @click="$emit('update:priority', '')" class="pill-remove">×</button>
          </span>
          
          <span v-if="activeDateFilter" class="filter-pill">
            📅 Due: {{ getDateFilterName(activeDateFilter) }}
            <button @click="handleDateFilter('')" class="pill-remove">×</button>
          </span>
        </div>
      </div>
    </div>
  </section>
</template>

<script setup>
import { ref, computed } from 'vue'

// Props
const props = defineProps({
  search: { type: String, default: '' },
  category: { type: String, default: '' },
  status: { type: String, default: '' },
  priority: { type: String, default: '' },
  categories: { type: Array, default: () => [] },
  filteredCount: { type: Number, default: 0 },
  selectedTasks: { type: Array, default: () => [] }
})

// Emits
defineEmits([
  'update:search',
  'update:category', 
  'update:status',
  'update:priority',
  'clear-completed',
  'export-tasks'
])

// Local state
const activeDateFilter = ref('')

// Filter options
const priorityOptions = [
  { value: 'high', label: 'High', icon: '🔴' },
  { value: 'medium', label: 'Medium', icon: '🟡' },
  { value: 'low', label: 'Low', icon: '🟢' }
]

const dateOptions = [
  { value: 'today', label: 'Today' },
  { value: 'tomorrow', label: 'Tomorrow' },
  { value: 'this-week', label: 'This Week' },
  { value: 'next-week', label: 'Next Week' },
  { value: 'overdue', label: 'Overdue' }
]

// Computed properties
const hasActiveFilters = computed(() => {
  return !!(props.search || props.category || props.status || props.priority || activeDateFilter.value)
})

// Methods
const togglePriority = (priorityValue) => {
  if (props.priority === priorityValue) {
    $emit('update:priority', '')
  } else {
    $emit('update:priority', priorityValue)
  }
}

const handleDateFilter = (dateValue) => {
  activeDateFilter.value = activeDateFilter.value === dateValue ? '' : dateValue
  // Emit custom event for date filtering
  $emit('date-filter-changed', activeDateFilter.value)
}

const handleClearFilters = () => {
  $emit('update:search', '')
  $emit('update:category', '')
  $emit('update:status', '')
  $emit('update:priority', '')
  activeDateFilter.value = ''
  $emit('date-filter-changed', '')
}

const getCategoryName = (categoryId) => {
  const category = props.categories.find(c => c.id === categoryId)
  return category ? `${category.emoji} ${category.name}` : categoryId
}

const getStatusName = (statusValue) => {
  const statusNames = {
    pending: '📋 Pending',
    completed: '✅ Completed',
    overdue: '⚠️ Overdue'
  }
  return statusNames[statusValue] || statusValue
}

const getPriorityName = (priorityValue) => {
  const priority = priorityOptions.find(p => p.value === priorityValue)
  return priority ? `${priority.icon} ${priority.label}` : priorityValue
}

const getDateFilterName = (dateValue) => {
  const dateOption = dateOptions.find(d => d.value === dateValue)
  return dateOption ? dateOption.label : dateValue
}
</script>

<style scoped>
.task-filters {
  margin: 2rem 0;
  padding: 1.5rem;
  background: var(--color-background-soft);
  border-radius: 8px;
  border: 1px solid var(--color-border);
}

.filters-container {
  display: flex;
  flex-direction: column;
  gap: 1.5rem;
}

.search-group {
  max-width: 400px;
}

.filter-label {
  display: block;
  margin-bottom: 0.5rem;
  font-weight: 500;
  color: var(--color-text);
  font-size: 0.875rem;
}

.search-input-wrapper {
  position: relative;
}

.search-input {
  width: 100%;
  padding: 0.75rem 2.5rem 0.75rem 0.75rem;
  border: 2px solid var(--color-border);
  border-radius: 4px;
  font-size: 1rem;
}

.clear-search {
  position: absolute;
  right: 0.5rem;
  top: 50%;
  transform: translateY(-50%);
  background: none;
  border: none;
  color: var(--color-text-light);
  cursor: pointer;
  padding: 0.25rem;
}

.search-info {
  margin-top: 0.25rem;
  font-size: 0.875rem;
  color: var(--color-text-light);
}

.filter-controls {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
  gap: 1rem;
}

.filter-select {
  width: 100%;
  padding: 0.5rem;
  border: 1px solid var(--color-border);
  border-radius: 4px;
}

.priority-filter-buttons,
.date-filter-buttons {
  display: flex;
  gap: 0.5rem;
  flex-wrap: wrap;
}

.priority-btn,
.date-btn {
  padding: 0.5rem 0.75rem;
  border: 1px solid var(--color-border);
  border-radius: 4px;
  background: var(--color-background);
  cursor: pointer;
  font-size: 0.875rem;
  transition: all 0.2s ease;
}

.priority-btn.active,
.date-btn.active {
  background: var(--color-primary);
  color: white;
  border-color: var(--color-primary);
}

.priority-high.active { background: var(--color-danger); border-color: var(--color-danger); }
.priority-medium.active { background: var(--color-warning); border-color: var(--color-warning); }
.priority-low.active { background: var(--color-success); border-color: var(--color-success); }

.filter-actions {
  display: flex;
  justify-content: space-between;
  align-items: center;
  flex-wrap: wrap;
  gap: 1rem;
}

.bulk-actions {
  display: flex;
  align-items: center;
  gap: 1rem;
}

.selection-count {
  font-size: 0.875rem;
  color: var(--color-text-light);
}

.action-btn {
  padding: 0.5rem 1rem;
  border: 1px solid var(--color-border);
  border-radius: 4px;
  background: var(--color-background);
  cursor: pointer;
  font-size: 0.875rem;
  transition: all 0.2s ease;
}

.action-btn:hover:not(:disabled) {
  background: var(--color-background-soft);
}

.action-btn:disabled {
  opacity: 0.5;
  cursor: not-allowed;
}

.active-filters {
  padding-top: 1rem;
  border-top: 1px solid var(--color-border);
}

.active-filters-title {
  margin: 0 0 0.75rem 0;
  font-size: 0.875rem;
  font-weight: 600;
  color: var(--color-text);
}

.filter-pills {
  display: flex;
  flex-wrap: wrap;
  gap: 0.5rem;
}

.filter-pill {
  display: inline-flex;
  align-items: center;
  gap: 0.5rem;
  padding: 0.25rem 0.75rem;
  background: var(--color-primary-light);
  color: var(--color-primary-dark);
  border-radius: 12px;
  font-size: 0.875rem;
}

.pill-remove {
  background: none;
  border: none;
  color: inherit;
  cursor: pointer;
  font-size: 1rem;
  line-height: 1;
  padding: 0;
}

@media (max-width: 768px) {
  .filter-controls {
    grid-template-columns: 1fr;
  }
  
  .filter-actions {
    flex-direction: column;
    align-items: stretch;
  }
  
  .bulk-actions {
    justify-content: space-between;
  }
}
</style>
```

### Success Criteria
- [ ] Advanced task form with all template features implemented
- [ ] Dynamic validation with real-time feedback
- [ ] Sophisticated filtering system working
- [ ] Template directives properly used
- [ ] Event handling with modifiers functional
- [ ] Form accessibility features working
- [ ] Keyboard shortcuts implemented
- [ ] Responsive design functional

### 💡 Key Learning Points

1. **Template Directives**: Mastering v-if, v-for, v-model, and v-on for dynamic behavior
2. **Event Handling**: Using event modifiers and keyboard shortcuts effectively
3. **Form Management**: Building complex forms with validation and user experience
4. **Conditional Rendering**: Showing/hiding content based on application state
5. **Component Communication**: Props and events for parent-child communication

### 🎯 Looking Ahead

In Chapter 10, you'll explore advanced event handling patterns, learning how to create sophisticated user interactions and component communication systems!

**Your dynamic template system is complete** - experience the power of reactive templates!