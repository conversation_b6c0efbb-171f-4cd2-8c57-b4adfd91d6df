# Chapter 7: Vue.js Composition API Deep Dive

## Learning Objectives
By the end of this chapter, you will understand:
- The Composition API and how it differs from Options API
- How to organize complex component logic effectively
- Reactive references, reactive objects, and computed properties
- Lifecycle hooks in the Composition API
- How to create reusable composable functions

## Understanding the Composition API

The **Composition API** is Vue 3's modern way of organizing component logic. It provides more flexibility and better code organization, especially for complex components.

### Traditional vs Composition API

```javascript
// Options API (Vue 2 style)
export default {
  data() {
    return {
      drumId: 0,
      previewLoading: false,
      previewImageUrl: null
    }
  },
  computed: {
    drumDisplayName() {
      return `Drum ${this.drumId + 1}`
    }
  },
  methods: {
    async loadPreview() {
      this.previewLoading = true
      try {
        const response = await apiService.getDrumPreview(this.drumId)
        this.previewImageUrl = URL.createObjectURL(response.data)
      } finally {
        this.previewLoading = false
      }
    }
  },
  mounted() {
    this.loadPreview()
  }
}

// Composition API (Vue 3 style)
export default {
  setup() {
    // Reactive state
    const drumId = ref(0)
    const previewLoading = ref(false)
    const previewImageUrl = ref(null)
    
    // Computed properties
    const drumDisplayName = computed(() => `Drum ${drumId.value + 1}`)
    
    // Methods
    const loadPreview = async () => {
      previewLoading.value = true
      try {
        const response = await apiService.getDrumPreview(drumId.value)
        previewImageUrl.value = URL.createObjectURL(response.data)
      } finally {
        previewLoading.value = false
      }
    }
    
    // Lifecycle
    onMounted(() => {
      loadPreview()
    })
    
    // Return what template needs
    return {
      drumId,
      previewLoading,
      previewImageUrl,
      drumDisplayName,
      loadPreview
    }
  }
}
```

### Why Composition API is Better for Complex Components

```
Options API Organization:
┌─────────────────┐
│ data()          │ ← All state mixed together
├─────────────────┤
│ computed: {}    │ ← All computed properties mixed
├─────────────────┤
│ methods: {}     │ ← All methods mixed together
├─────────────────┤
│ mounted()       │ ← Lifecycle scattered
└─────────────────┘

Composition API Organization:
┌─────────────────┐
│ Feature A:      │
│ - state         │ ← Related logic grouped
│ - computed      │
│ - methods       │
│ - lifecycle     │
├─────────────────┤
│ Feature B:      │
│ - state         │ ← Each feature self-contained
│ - computed      │
│ - methods       │
└─────────────────┘
```

## Reactive Data Management

### 1. **ref() - Reactive Primitives**

Used for primitive values (numbers, strings, booleans):

```javascript
import { ref } from 'vue'

// Creating reactive references
const selectedDrum = ref(0)           // number
const previewLoading = ref(false)     // boolean
const errorMessage = ref('')          // string
const layerNumber = ref(1)            // number

// Reading values (need .value)
console.log(selectedDrum.value)       // 0

// Updating values
selectedDrum.value = 2
previewLoading.value = true

// In templates, .value is automatic
// <div>{{ selectedDrum }}</div>  ← No .value needed in template
```

### 2. **reactive() - Reactive Objects**

Used for objects and arrays:

```javascript
import { reactive } from 'vue'

// Creating reactive objects
const drumConfig = reactive({
  temperature: 200,
  speed: 50,
  material: 'PLA'
})

const layers = reactive([
  { id: 1, name: 'Base Layer' },
  { id: 2, name: 'Support Layer' }
])

// Direct property access (no .value needed)
console.log(drumConfig.temperature)   // 200
drumConfig.temperature = 220

// Adding/removing from arrays
layers.push({ id: 3, name: 'Top Layer' })
```

### 3. **When to Use ref() vs reactive()**

```javascript
// Use ref() for:
const count = ref(0)                  // Primitives
const isLoading = ref(false)          // Booleans
const message = ref('Hello')          // Strings
const userArray = ref([])             // When you might replace entire array

// Use reactive() for:
const config = reactive({             // Complex objects
  drums: {
    drum0: { temp: 200 },
    drum1: { temp: 210 }
  },
  settings: {
    autoPreview: true,
    maxLayers: 100
  }
})

const formData = reactive({           // Form objects
  drumId: 0,
  layerNumber: 1,
  temperature: 200
})
```

## Computed Properties

Computed properties automatically recalculate when their dependencies change:

### Basic Computed Properties

```javascript
import { ref, computed } from 'vue'

const selectedDrum = ref(0)
const maxLayers = ref(100)
const currentLayer = ref(1)

// Simple computed property
const drumDisplayName = computed(() => {
  return `Drum ${selectedDrum.value + 1}`
})

// Computed with multiple dependencies
const layerProgress = computed(() => {
  return Math.round((currentLayer.value / maxLayers.value) * 100)
})

// Computed with conditional logic
const layerStatus = computed(() => {
  const progress = layerProgress.value
  if (progress < 25) return 'Starting'
  if (progress < 75) return 'In Progress'
  if (progress < 100) return 'Nearly Complete'
  return 'Complete'
})
```

### Advanced Computed Properties

```javascript
// Computed with complex logic
const availableDrums = computed(() => {
  return drumsData.value.filter(drum => {
    return drum.isConnected && !drum.hasError
  }).map(drum => ({
    id: drum.id,
    name: `Drum ${drum.id + 1}`,
    status: drum.status,
    canPreview: drum.hasCliFile
  }))
})

// Computed for UI state
const previewButtonText = computed(() => {
  if (previewLoading.value) return 'Loading...'
  if (!selectedDrum.value) return 'Select Drum'
  if (!hasCliFile.value) return 'No CLI File'
  return 'Load Preview'
})

// Computed for form validation
const isFormValid = computed(() => {
  return selectedDrum.value !== null &&
         currentLayer.value >= 1 &&
         currentLayer.value <= maxLayers.value
})
```

## Watchers and Effects

### 1. **watch() - React to Data Changes**

```javascript
import { ref, watch } from 'vue'

const selectedDrum = ref(0)
const currentLayer = ref(1)

// Watch a single value
watch(selectedDrum, (newDrum, oldDrum) => {
  console.log(`Switched from drum ${oldDrum} to ${newDrum}`)
  // Reset layer when drum changes
  currentLayer.value = 1
})

// Watch multiple values
watch([selectedDrum, currentLayer], ([newDrum, newLayer], [oldDrum, oldLayer]) => {
  console.log(`Drum: ${oldDrum}→${newDrum}, Layer: ${oldLayer}→${newLayer}`)
  // Reload preview when either changes
  loadPreview()
})

// Watch with options
watch(selectedDrum, (newValue) => {
  // This runs immediately and on changes
  updateDrumConfiguration(newValue)
}, { 
  immediate: true,  // Run immediately
  deep: true        // Watch nested properties
})
```

### 2. **watchEffect() - Automatic Dependency Tracking**

```javascript
import { watchEffect } from 'vue'

// Automatically tracks dependencies
watchEffect(() => {
  // This will re-run whenever selectedDrum or currentLayer changes
  console.log(`Current: Drum ${selectedDrum.value}, Layer ${currentLayer.value}`)
  
  // Update document title
  document.title = `Layer ${currentLayer.value} - Drum ${selectedDrum.value + 1}`
})

// Cleanup function
watchEffect((onInvalidate) => {
  const timer = setInterval(() => {
    // Some periodic task
  }, 1000)
  
  // Cleanup when effect re-runs or component unmounts
  onInvalidate(() => {
    clearInterval(timer)
  })
})
```

## Lifecycle Hooks

Vue components have a lifecycle - they are created, mounted, updated, and unmounted:

```javascript
import { 
  onBeforeMount,
  onMounted, 
  onBeforeUpdate,
  onUpdated,
  onBeforeUnmount,
  onUnmounted 
} from 'vue'

export default {
  setup() {
    // Before component mounts to DOM
    onBeforeMount(() => {
      console.log('About to mount component')
    })
    
    // After component mounts to DOM
    onMounted(() => {
      console.log('Component mounted')
      // Good place for:
      // - API calls
      // - Setting up event listeners
      // - Initializing third-party libraries
      loadInitialData()
    })
    
    // Before component updates
    onBeforeUpdate(() => {
      console.log('About to update component')
    })
    
    // After component updates
    onUpdated(() => {
      console.log('Component updated')
      // Good place for:
      // - DOM operations after updates
      // - Third-party library updates
    })
    
    // Before component unmounts
    onBeforeUnmount(() => {
      console.log('About to unmount component')
      // Good place for:
      // - Cleanup timers
      // - Remove event listeners
      // - Cancel ongoing requests
    })
    
    // After component unmounts
    onUnmounted(() => {
      console.log('Component unmounted')
    })
  }
}
```

### Real-World Lifecycle Example

```javascript
// Layer Preview Component Lifecycle
export default {
  setup() {
    const drumId = ref(0)
    const previewImageUrl = ref(null)
    const intervalId = ref(null)
    
    // Setup when component mounts
    onMounted(() => {
      // Load initial preview
      loadPreview()
      
      // Setup auto-refresh every 30 seconds
      intervalId.value = setInterval(() => {
        if (!previewLoading.value) {
          loadPreview()
        }
      }, 30000)
    })
    
    // Cleanup when component unmounts
    onBeforeUnmount(() => {
      // Clear interval to prevent memory leaks
      if (intervalId.value) {
        clearInterval(intervalId.value)
      }
      
      // Revoke object URLs to free memory
      if (previewImageUrl.value) {
        URL.revokeObjectURL(previewImageUrl.value)
      }
    })
    
    const loadPreview = async () => {
      // Preview loading logic
    }
    
    return { drumId, previewImageUrl, loadPreview }
  }
}
```

## Composable Functions (Reusable Logic)

Composables are functions that encapsulate reactive logic for reuse across components:

### 1. **Basic Composable Pattern**

```javascript
// composables/useDrumControl.js
import { ref, computed } from 'vue'

export function useDrumControl() {
  const selectedDrum = ref(0)
  const drumsData = ref([
    { id: 0, name: 'Drum 1', isConnected: false },
    { id: 1, name: 'Drum 2', isConnected: false },
    { id: 2, name: 'Drum 3', isConnected: false }
  ])
  
  const availableDrums = computed(() => {
    return drumsData.value.filter(drum => drum.isConnected)
  })
  
  const selectDrum = (drumId) => {
    selectedDrum.value = drumId
  }
  
  const getCurrentDrum = computed(() => {
    return drumsData.value.find(drum => drum.id === selectedDrum.value)
  })
  
  return {
    selectedDrum,
    drumsData,
    availableDrums,
    selectDrum,
    getCurrentDrum
  }
}
```

### 2. **Advanced Composable with API Integration**

```javascript
// composables/useLayerPreview.js
import { ref, computed } from 'vue'
import apiService from '@/services/api'

export function useLayerPreview() {
  const previewImageUrl = ref(null)
  const previewLoading = ref(false)
  const previewError = ref(null)
  
  const hasPreview = computed(() => !!previewImageUrl.value)
  
  const loadPreview = async (drumId, layerNumber = 1) => {
    previewLoading.value = true
    previewError.value = null
    
    try {
      // Clean up previous preview
      if (previewImageUrl.value) {
        URL.revokeObjectURL(previewImageUrl.value)
      }
      
      const response = await apiService.getDrumGeometryPreview(drumId, layerNumber)
      previewImageUrl.value = URL.createObjectURL(response.data)
    } catch (error) {
      previewError.value = error.response?.data?.detail || error.message
      previewImageUrl.value = null
    } finally {
      previewLoading.value = false
    }
  }
  
  const clearPreview = () => {
    if (previewImageUrl.value) {
      URL.revokeObjectURL(previewImageUrl.value)
      previewImageUrl.value = null
    }
    previewError.value = null
  }
  
  return {
    previewImageUrl,
    previewLoading,
    previewError,
    hasPreview,
    loadPreview,
    clearPreview
  }
}
```

### 3. **Using Composables in Components**

```javascript
// In a component
export default {
  setup() {
    // Use multiple composables
    const { selectedDrum, availableDrums, selectDrum } = useDrumControl()
    const { previewImageUrl, previewLoading, loadPreview } = useLayerPreview()
    const { errorMessage, showError, clearError } = useErrorHandling()
    
    // Component-specific logic
    const currentLayer = ref(1)
    
    const handleLoadPreview = async () => {
      try {
        await loadPreview(selectedDrum.value, currentLayer.value)
      } catch (error) {
        showError('Failed to load preview')
      }
    }
    
    // Watch for drum changes
    watch(selectedDrum, () => {
      handleLoadPreview()
    })
    
    return {
      selectedDrum,
      availableDrums,
      selectDrum,
      previewImageUrl,
      previewLoading,
      currentLayer,
      handleLoadPreview,
      errorMessage
    }
  }
}
```

## Organizing Complex Components

### Problem: Large Setup Function

```javascript
// BAD: Everything in one setup function
export default {
  setup() {
    // 50+ lines of drum control logic
    // 30+ lines of layer preview logic  
    // 20+ lines of error handling logic
    // 40+ lines of form validation logic
    // This becomes unreadable!
  }
}
```

### Solution: Extract Composables

```javascript
// GOOD: Organized with composables
export default {
  setup() {
    // Each composable handles one concern
    const drumControl = useDrumControl()
    const layerPreview = useLayerPreview()
    const errorHandling = useErrorHandling()
    const formValidation = useFormValidation()
    
    // Component-specific logic only
    const handleSubmit = () => {
      if (formValidation.isValid.value) {
        layerPreview.loadPreview(
          drumControl.selectedDrum.value,
          formValidation.layerNumber.value
        )
      }
    }
    
    return {
      ...drumControl,
      ...layerPreview,
      ...errorHandling,
      ...formValidation,
      handleSubmit
    }
  }
}
```

## Real-World Example: Layer Preview Component

Let's put it all together in a complete component:

```vue
<template>
  <div class="layer-preview">
    <h2>Layer Preview</h2>
    
    <!-- Drum Selection -->
    <div class="form-group">
      <label for="drum-select">Select Drum:</label>
      <select 
        id="drum-select" 
        v-model="selectedDrum"
        :disabled="previewLoading"
      >
        <option 
          v-for="drum in availableDrums" 
          :key="drum.id" 
          :value="drum.id"
        >
          {{ drum.name }}
        </option>
      </select>
    </div>
    
    <!-- Layer Selection -->
    <div class="form-group">
      <label for="layer-input">Layer Number:</label>
      <input 
        id="layer-input"
        v-model.number="currentLayer" 
        type="number" 
        :min="1" 
        :max="maxLayers"
        :disabled="previewLoading"
      >
    </div>
    
    <!-- Load Button -->
    <button 
      @click="handleLoadPreview" 
      :disabled="!isFormValid || previewLoading"
      class="load-btn"
    >
      {{ previewButtonText }}
    </button>
    
    <!-- Error Display -->
    <div v-if="previewError" class="error">
      {{ previewError }}
    </div>
    
    <!-- Preview Image -->
    <div v-if="hasPreview" class="preview-container">
      <img :src="previewImageUrl" alt="Layer Preview">
    </div>
  </div>
</template>

<script>
import { ref, computed, watch, onMounted, onBeforeUnmount } from 'vue'
import { useDrumControl } from '@/composables/useDrumControl'
import { useLayerPreview } from '@/composables/useLayerPreview'

export default {
  name: 'LayerPreview',
  setup() {
    // Composables
    const { selectedDrum, availableDrums } = useDrumControl()
    const { 
      previewImageUrl, 
      previewLoading, 
      previewError,
      hasPreview,
      loadPreview 
    } = useLayerPreview()
    
    // Component state
    const currentLayer = ref(1)
    const maxLayers = ref(100)
    
    // Computed properties
    const isFormValid = computed(() => {
      return selectedDrum.value !== null &&
             currentLayer.value >= 1 &&
             currentLayer.value <= maxLayers.value
    })
    
    const previewButtonText = computed(() => {
      if (previewLoading.value) return 'Loading...'
      if (!isFormValid.value) return 'Invalid Selection'
      return 'Load Preview'
    })
    
    // Methods
    const handleLoadPreview = async () => {
      if (!isFormValid.value) return
      
      await loadPreview(selectedDrum.value, currentLayer.value)
    }
    
    // Watchers
    watch(selectedDrum, () => {
      currentLayer.value = 1  // Reset layer when drum changes
    })
    
    // Lifecycle
    onMounted(() => {
      // Load initial preview if drum is selected
      if (selectedDrum.value !== null) {
        handleLoadPreview()
      }
    })
    
    return {
      selectedDrum,
      availableDrums,
      currentLayer,
      maxLayers,
      previewImageUrl,
      previewLoading,
      previewError,
      hasPreview,
      isFormValid,
      previewButtonText,
      handleLoadPreview
    }
  }
}
</script>

<style scoped>
.layer-preview {
  max-width: 600px;
  margin: 0 auto;
  padding: 2rem;
}

.form-group {
  margin-bottom: 1rem;
}

.load-btn {
  background: #007bff;
  color: white;
  border: none;
  padding: 0.75rem 1.5rem;
  border-radius: 4px;
  cursor: pointer;
}

.load-btn:disabled {
  background: #6c757d;
  cursor: not-allowed;
}

.error {
  color: #dc3545;
  margin: 1rem 0;
  padding: 0.5rem;
  border: 1px solid #dc3545;
  border-radius: 4px;
  background: #f8d7da;
}

.preview-container {
  margin-top: 2rem;
  text-align: center;
}

.preview-container img {
  max-width: 100%;
  height: auto;
  border: 1px solid #ddd;
  border-radius: 4px;
}
</style>
```

## Best Practices

### 1. **Naming Conventions**
```javascript
// Reactive variables: descriptive nouns
const selectedDrum = ref(0)
const previewLoading = ref(false)
const errorMessage = ref('')

// Computed properties: descriptive, often adjectives or "get" functions
const isFormValid = computed(() => ...)
const drumDisplayName = computed(() => ...)
const getMaxLayers = computed(() => ...)

// Functions: verbs describing actions
const loadPreview = () => {}
const selectDrum = (id) => {}
const clearError = () => {}
```

### 2. **Return Organization**
```javascript
export default {
  setup() {
    // ... logic here
    
    // Group returns logically
    return {
      // State
      selectedDrum,
      previewLoading,
      currentLayer,
      
      // Computed
      drumDisplayName,
      isFormValid,
      
      // Methods
      loadPreview,
      selectDrum,
      clearError
    }
  }
}
```

### 3. **Error Handling**
```javascript
const handleAsyncOperation = async () => {
  try {
    loading.value = true
    error.value = null
    
    const result = await someAsyncOperation()
    // Handle success
    
  } catch (err) {
    error.value = err.message
    console.error('Operation failed:', err)
  } finally {
    loading.value = false
  }
}
```

## Key Takeaways

1. **Composition API** provides better organization for complex logic
2. **ref()** for primitives, **reactive()** for objects
3. **Computed properties** automatically update when dependencies change
4. **Watchers** let you react to data changes with side effects
5. **Lifecycle hooks** handle component creation/destruction
6. **Composables** enable logic reuse across components
7. **Organize by feature**, not by type of code

## Next Steps

In the next chapter, we'll explore **Vue.js Reactivity System** in detail:
- How Vue tracks dependencies
- Performance implications
- Advanced reactivity patterns
- Debugging reactive code

The reactivity system is what makes Vue.js so powerful for industrial applications!

---

## 🚀 Mini Project: Advanced Component Architecture

### Exercise 7.1: Create Task Card Component

Build a sophisticated task card using advanced Composition API patterns. Create `src/components/TaskCard.vue`:

```vue
<template>
  <article 
    class="task-card" 
    :class="taskCardClasses"
    :aria-label="`Task: ${task.title}`"
    role="listitem"
  >
    <!-- Task header with actions -->
    <header class="task-header">
      <div class="task-status-controls">
        <button
          type="button"
          class="task-checkbox"
          :class="{ 'completed': task.completed }"
          @click="toggleTaskCompletion"
          :aria-label="checkboxAriaLabel"
        >
          <span class="checkbox-icon" aria-hidden="true">
            {{ task.completed ? '✓' : '' }}
          </span>
        </button>
        
        <div class="task-priority" :data-priority="task.priority">
          {{ priorityIcon }}
        </div>
      </div>

      <div class="task-actions">
        <button 
          type="button"
          class="btn-action edit"
          @click="toggleEditMode"
          :aria-label="`Edit task: ${task.title}`"
        >
          {{ isEditing ? '💾' : '✏️' }}
        </button>
        
        <button 
          type="button"
          class="btn-action delete"
          @click="handleDeleteTask"
          :aria-label="`Delete task: ${task.title}`"
        >
          🗑️
        </button>
      </div>
    </header>

    <!-- Task content -->
    <div class="task-content">
      <!-- Editable title -->
      <h3 class="task-title">
        <input 
          v-if="isEditing"
          ref="titleInputRef"
          v-model="editableTask.title"
          type="text"
          class="task-title-input"
          @keyup.enter="saveChanges"
          @keyup.escape="cancelEdit"
          :aria-label="'Edit task title'"
        />
        <span v-else :class="{ 'completed': task.completed }">
          {{ task.title }}
        </span>
      </h3>

      <!-- Editable description -->
      <div class="task-description" v-if="task.description || isEditing">
        <textarea 
          v-if="isEditing"
          v-model="editableTask.description"
          class="task-description-input"
          rows="3"
          placeholder="Add a description..."
          @keyup.escape="cancelEdit"
        ></textarea>
        <p v-else class="task-description-text">
          {{ task.description }}
        </p>
      </div>

      <!-- Task metadata -->
      <div class="task-metadata">
        <!-- Category -->
        <div class="task-category" v-if="categoryInfo">
          <span class="category-emoji" aria-hidden="true">{{ categoryInfo.emoji }}</span>
          <span class="category-name">{{ categoryInfo.name }}</span>
        </div>

        <!-- Due date -->
        <div class="task-due-date" v-if="task.dueDate" :class="dueDateClasses">
          <span class="due-date-icon" aria-hidden="true">📅</span>
          <time :datetime="task.dueDate" class="due-date-text">
            {{ formatDueDate(task.dueDate) }}
          </time>
          <span v-if="isOverdue" class="overdue-badge" aria-label="Overdue">⚠️</span>
        </div>

        <!-- Time tracking -->
        <div class="task-timing" v-if="showTiming">
          <small class="timing-text">
            Created {{ formatRelativeTime(task.createdAt) }}
            <span v-if="task.completedAt">
              • Completed {{ formatRelativeTime(task.completedAt) }}
            </span>
          </small>
        </div>
      </div>
    </div>

    <!-- Task progress indicators -->
    <div class="task-progress" v-if="showProgress">
      <!-- Completion animation -->
      <div 
        class="completion-animation" 
        :class="{ 'animate': recentlyCompleted }"
      ></div>
      
      <!-- Task tags/labels -->
      <div class="task-tags" v-if="taskTags.length > 0">
        <span 
          v-for="tag in taskTags"
          :key="tag"
          class="task-tag"
        >
          {{ tag }}
        </span>
      </div>
    </div>

    <!-- Edit mode controls -->
    <footer class="task-edit-controls" v-if="isEditing">
      <div class="edit-form-group">
        <label for="category-select" class="edit-label">Category:</label>
        <select 
          id="category-select"
          v-model="editableTask.category"
          class="edit-select"
        >
          <option value="">No category</option>
          <option 
            v-for="category in categories"
            :key="category.id"
            :value="category.id"
          >
            {{ category.emoji }} {{ category.name }}
          </option>
        </select>
      </div>

      <div class="edit-form-group">
        <label for="priority-select" class="edit-label">Priority:</label>
        <select 
          id="priority-select"
          v-model="editableTask.priority"
          class="edit-select"
        >
          <option value="low">🟢 Low</option>
          <option value="medium">🟡 Medium</option>
          <option value="high">🔴 High</option>
        </select>
      </div>

      <div class="edit-form-group">
        <label for="due-date-input" class="edit-label">Due Date:</label>
        <input 
          id="due-date-input"
          v-model="editableTask.dueDate"
          type="date"
          class="edit-input"
        />
      </div>

      <div class="edit-actions">
        <button 
          type="button"
          class="btn btn-save"
          @click="saveChanges"
          :disabled="!isFormValid"
        >
          Save Changes
        </button>
        <button 
          type="button"
          class="btn btn-cancel"
          @click="cancelEdit"
        >
          Cancel
        </button>
      </div>
    </footer>
  </article>
</template>

<script setup>
import { ref, reactive, computed, watch, nextTick, onMounted, onUnmounted } from 'vue'
import { useDateFormatting } from '../composables/useDateFormatting'
import { useTaskValidation } from '../composables/useTaskValidation'
import { useTaskAnimation } from '../composables/useTaskAnimation'

// Props
const props = defineProps({
  task: {
    type: Object,
    required: true
  },
  categories: {
    type: Array,
    default: () => []
  },
  showTiming: {
    type: Boolean,
    default: true
  },
  showProgress: {
    type: Boolean,
    default: true
  }
})

// Emits
const emit = defineEmits(['task-updated', 'task-deleted'])

// Composables
const { formatDueDate, formatRelativeTime } = useDateFormatting()
const { validateTask } = useTaskValidation()
const { recentlyCompleted, triggerCompletionAnimation } = useTaskAnimation()

// Reactive state
const isEditing = ref(false)
const titleInputRef = ref(null)
const editableTask = reactive({
  title: '',
  description: '',
  category: '',
  priority: '',
  dueDate: ''
})

// Computed properties
const taskCardClasses = computed(() => ({
  'task-completed': props.task.completed,
  'task-overdue': isOverdue.value && !props.task.completed,
  'task-editing': isEditing.value,
  [`priority-${props.task.priority}`]: props.task.priority,
  'task-has-description': props.task.description
}))

const checkboxAriaLabel = computed(() => 
  props.task.completed 
    ? `Mark task "${props.task.title}" as incomplete`
    : `Mark task "${props.task.title}" as complete`
)

const priorityIcon = computed(() => {
  const icons = {
    low: '🟢',
    medium: '🟡', 
    high: '🔴'
  }
  return icons[props.task.priority] || '⚪'
})

const categoryInfo = computed(() => 
  props.categories.find(cat => cat.id === props.task.category)
)

const isOverdue = computed(() => {
  if (!props.task.dueDate || props.task.completed) return false
  
  const dueDate = new Date(props.task.dueDate)
  const today = new Date()
  today.setHours(23, 59, 59, 999)
  
  return dueDate < today
})

const dueDateClasses = computed(() => ({
  'due-today': isDueToday.value,
  'due-soon': isDueSoon.value,
  'overdue': isOverdue.value
}))

const isDueToday = computed(() => {
  if (!props.task.dueDate) return false
  
  const dueDate = new Date(props.task.dueDate)
  const today = new Date()
  
  return dueDate.toDateString() === today.toDateString()
})

const isDueSoon = computed(() => {
  if (!props.task.dueDate || isOverdue.value || isDueToday.value) return false
  
  const dueDate = new Date(props.task.dueDate)
  const tomorrow = new Date()
  tomorrow.setDate(tomorrow.getDate() + 1)
  const nextWeek = new Date()
  nextWeek.setDate(nextWeek.getDate() + 7)
  
  return dueDate >= tomorrow && dueDate <= nextWeek
})

const taskTags = computed(() => {
  const tags = []
  
  if (isDueToday.value) tags.push('Due Today')
  if (isDueSoon.value) tags.push('Due Soon')
  if (isOverdue.value && !props.task.completed) tags.push('Overdue')
  if (props.task.completed) tags.push('Completed')
  
  return tags
})

const isFormValid = computed(() => {
  return editableTask.title.trim().length >= 3
})

// Methods
const toggleTaskCompletion = () => {
  const updatedTask = {
    ...props.task,
    completed: !props.task.completed,
    completedAt: !props.task.completed ? new Date().toISOString() : null
  }
  
  // Trigger completion animation
  if (!props.task.completed) {
    triggerCompletionAnimation()
  }
  
  emit('task-updated', updatedTask)
}

const toggleEditMode = async () => {
  if (isEditing.value) {
    await saveChanges()
  } else {
    startEdit()
  }
}

const startEdit = async () => {
  // Copy current task data to editable state
  Object.assign(editableTask, {
    title: props.task.title,
    description: props.task.description || '',
    category: props.task.category || '',
    priority: props.task.priority || 'medium',
    dueDate: props.task.dueDate || ''
  })
  
  isEditing.value = true
  
  // Focus title input on next tick
  await nextTick()
  if (titleInputRef.value) {
    titleInputRef.value.focus()
    titleInputRef.value.select()
  }
}

const saveChanges = async () => {
  // Validate the task
  const validation = validateTask(editableTask)
  if (!validation.isValid) {
    // You could emit an error event or show validation errors
    console.error('Validation errors:', validation.errors)
    return
  }
  
  const updatedTask = {
    ...props.task,
    title: editableTask.title.trim(),
    description: editableTask.description.trim(),
    category: editableTask.category,
    priority: editableTask.priority,
    dueDate: editableTask.dueDate || null
  }
  
  emit('task-updated', updatedTask)
  isEditing.value = false
}

const cancelEdit = () => {
  isEditing.value = false
  // Reset editable task data
  Object.assign(editableTask, {
    title: props.task.title,
    description: props.task.description || '',
    category: props.task.category || '',
    priority: props.task.priority || 'medium',
    dueDate: props.task.dueDate || ''
  })
}

const handleDeleteTask = () => {
  if (confirm(`Are you sure you want to delete "${props.task.title}"?`)) {
    emit('task-deleted', props.task)
  }
}

// Keyboard shortcuts
const handleKeyboardShortcuts = (e) => {
  // Only handle if this task card is focused or one of its elements
  if (!e.target.closest('.task-card')) return
  
  // Space: Toggle completion (when not editing)
  if (e.code === 'Space' && !isEditing.value && e.target.tagName !== 'INPUT' && e.target.tagName !== 'TEXTAREA') {
    e.preventDefault()
    toggleTaskCompletion()
  }
  
  // Enter: Start/save edit
  if (e.code === 'Enter' && !isEditing.value && e.target.tagName !== 'BUTTON') {
    e.preventDefault()
    startEdit()
  }
  
  // Delete: Delete task (when not editing)
  if (e.code === 'Delete' && !isEditing.value && e.target.tagName !== 'INPUT' && e.target.tagName !== 'TEXTAREA') {
    e.preventDefault()
    handleDeleteTask()
  }
}

// Lifecycle
onMounted(() => {
  document.addEventListener('keydown', handleKeyboardShortcuts)
})

onUnmounted(() => {
  document.removeEventListener('keydown', handleKeyboardShortcuts)
})

// Watch for task changes to update local state
watch(() => props.task, (newTask) => {
  if (!isEditing.value) {
    Object.assign(editableTask, {
      title: newTask.title,
      description: newTask.description || '',
      category: newTask.category || '',
      priority: newTask.priority || 'medium',
      dueDate: newTask.dueDate || ''
    })
  }
}, { deep: true })
</script>

<style scoped>
/* TaskCard styles will be inherited from your main CSS file */
.task-card {
  transition: all 0.3s ease;
}

.task-card:hover {
  transform: translateY(-2px);
  box-shadow: 0 4px 12px rgba(0,0,0,0.15);
}

.completion-animation {
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: linear-gradient(45deg, #4CAF50, #8BC34A);
  opacity: 0;
  border-radius: inherit;
  pointer-events: none;
}

.completion-animation.animate {
  animation: completionPulse 0.6s ease-out;
}

@keyframes completionPulse {
  0% { opacity: 0; transform: scale(1); }
  50% { opacity: 0.3; transform: scale(1.02); }
  100% { opacity: 0; transform: scale(1); }
}

.task-editing {
  border: 2px solid var(--color-primary);
  background: var(--color-background-soft);
}

.edit-form-group {
  margin-bottom: 0.75rem;
}

.edit-label {
  display: block;
  margin-bottom: 0.25rem;
  font-weight: 500;
  color: var(--color-text);
}

.edit-select,
.edit-input {
  width: 100%;
  padding: 0.5rem;
  border: 1px solid var(--color-border);
  border-radius: 4px;
  font-size: 0.875rem;
}

.edit-actions {
  display: flex;
  gap: 0.5rem;
  margin-top: 1rem;
}

.btn {
  padding: 0.5rem 1rem;
  border-radius: 4px;
  font-size: 0.875rem;
  cursor: pointer;
  transition: all 0.2s ease;
}

.btn-save {
  background: var(--color-primary);
  color: white;
  border: none;
}

.btn-save:disabled {
  opacity: 0.5;
  cursor: not-allowed;
}

.btn-cancel {
  background: var(--color-background);
  color: var(--color-text);
  border: 1px solid var(--color-border);
}
</style>
```

### Exercise 7.2: Create Supporting Composables

Create the composables needed by your TaskCard component:

#### `src/composables/useTaskAnimation.js`:

```javascript
import { ref, onUnmounted } from 'vue'

export function useTaskAnimation() {
  const recentlyCompleted = ref(false)
  const animationTimeout = ref(null)

  const triggerCompletionAnimation = () => {
    // Clear any existing timeout
    if (animationTimeout.value) {
      clearTimeout(animationTimeout.value)
    }

    recentlyCompleted.value = true
    
    // Reset animation state after animation completes
    animationTimeout.value = setTimeout(() => {
      recentlyCompleted.value = false
      animationTimeout.value = null
    }, 600) // Match CSS animation duration
  }

  const cleanup = () => {
    if (animationTimeout.value) {
      clearTimeout(animationTimeout.value)
      animationTimeout.value = null
    }
  }

  // Cleanup on unmount
  onUnmounted(cleanup)

  return {
    recentlyCompleted,
    triggerCompletionAnimation,
    cleanup
  }
}
```

#### `src/composables/useDateFormatting.js`:

```javascript
export function useDateFormatting() {
  const formatDueDate = (dateString) => {
    if (!dateString) return ''
    
    const date = new Date(dateString)
    const today = new Date()
    const tomorrow = new Date(today)
    tomorrow.setDate(tomorrow.getDate() + 1)
    
    // Check if it's today
    if (date.toDateString() === today.toDateString()) {
      return 'Today'
    }
    
    // Check if it's tomorrow
    if (date.toDateString() === tomorrow.toDateString()) {
      return 'Tomorrow'
    }
    
    // Check if it's this week
    const daysDiff = Math.ceil((date - today) / (1000 * 60 * 60 * 24))
    if (daysDiff >= 0 && daysDiff <= 7) {
      return date.toLocaleDateString('en-US', { weekday: 'long' })
    }
    
    // Otherwise, show full date
    return date.toLocaleDateString('en-US', { 
      month: 'short', 
      day: 'numeric',
      year: date.getFullYear() !== today.getFullYear() ? 'numeric' : undefined
    })
  }

  const formatRelativeTime = (dateString) => {
    if (!dateString) return ''
    
    const date = new Date(dateString)
    const now = new Date()
    const diffInSeconds = Math.floor((now - date) / 1000)
    
    if (diffInSeconds < 60) {
      return 'just now'
    }
    
    const diffInMinutes = Math.floor(diffInSeconds / 60)
    if (diffInMinutes < 60) {
      return `${diffInMinutes} minute${diffInMinutes === 1 ? '' : 's'} ago`
    }
    
    const diffInHours = Math.floor(diffInMinutes / 60)
    if (diffInHours < 24) {
      return `${diffInHours} hour${diffInHours === 1 ? '' : 's'} ago`
    }
    
    const diffInDays = Math.floor(diffInHours / 24)
    if (diffInDays < 7) {
      return `${diffInDays} day${diffInDays === 1 ? '' : 's'} ago`
    }
    
    return date.toLocaleDateString('en-US', { 
      month: 'short', 
      day: 'numeric',
      year: date.getFullYear() !== now.getFullYear() ? 'numeric' : undefined
    })
  }

  return {
    formatDueDate,
    formatRelativeTime
  }
}
```

#### `src/composables/useTaskValidation.js`:

```javascript
export function useTaskValidation() {
  const validateTask = (task) => {
    const errors = []
    
    // Title validation
    if (!task.title || typeof task.title !== 'string') {
      errors.push('Task title is required')
    } else if (task.title.trim().length === 0) {
      errors.push('Task title cannot be empty')
    } else if (task.title.trim().length < 3) {
      errors.push('Task title must be at least 3 characters long')
    } else if (task.title.length > 200) {
      errors.push('Task title cannot exceed 200 characters')
    }
    
    // Description validation
    if (task.description && task.description.length > 1000) {
      errors.push('Task description cannot exceed 1000 characters')
    }
    
    // Due date validation
    if (task.dueDate) {
      const dueDate = new Date(task.dueDate)
      if (isNaN(dueDate.getTime())) {
        errors.push('Invalid due date format')
      } else {
        const today = new Date()
        today.setHours(0, 0, 0, 0)
        
        if (dueDate < today) {
          errors.push('Due date cannot be in the past')
        }
      }
    }
    
    // Priority validation
    const validPriorities = ['low', 'medium', 'high']
    if (task.priority && !validPriorities.includes(task.priority)) {
      errors.push('Invalid priority level')
    }
    
    return {
      isValid: errors.length === 0,
      errors
    }
  }

  return {
    validateTask
  }
}
```

### Exercise 7.3: Update Your Task List Component

Update `src/components/TaskList.vue` to use the new TaskCard:

```vue
<template>
  <section class="task-list" role="region" aria-label="Task list">
    <div v-if="tasks.length === 0" class="empty-state">
      <div class="empty-icon" aria-hidden="true">📝</div>
      <h3 class="empty-title">No tasks yet</h3>
      <p class="empty-description">
        Create your first task to get started with your productivity journey!
      </p>
    </div>

    <div v-else class="task-grid">
      <TransitionGroup name="task" tag="ul" class="task-list-container">
        <li v-for="task in tasks" :key="task.id" class="task-list-item">
          <TaskCard
            :task="task"
            :categories="categories"
            :show-timing="showTiming"
            :show-progress="showProgress"
            @task-updated="$emit('task-updated', $event)"
            @task-deleted="$emit('task-deleted', $event)"
          />
        </li>
      </TransitionGroup>
    </div>
  </section>
</template>

<script setup>
import TaskCard from './TaskCard.vue'

defineProps({
  tasks: {
    type: Array,
    default: () => []
  },
  categories: {
    type: Array,
    default: () => []
  },
  showTiming: {
    type: Boolean,
    default: true
  },
  showProgress: {
    type: Boolean,
    default: true
  }
})

defineEmits(['task-updated', 'task-deleted'])
</script>

<style scoped>
.task-list {
  margin: 2rem 0;
}

.empty-state {
  text-align: center;
  padding: 3rem 1rem;
  color: var(--color-text-light);
}

.empty-icon {
  font-size: 4rem;
  margin-bottom: 1rem;
}

.empty-title {
  font-size: 1.5rem;
  margin-bottom: 0.5rem;
  color: var(--color-text);
}

.empty-description {
  font-size: 1rem;
  max-width: 400px;
  margin: 0 auto;
}

.task-grid {
  display: grid;
  gap: 1rem;
  grid-template-columns: repeat(auto-fill, minmax(300px, 1fr));
}

.task-list-container {
  list-style: none;
  padding: 0;
  margin: 0;
  display: contents;
}

.task-list-item {
  display: flex;
}

/* Task transition animations */
.task-enter-active,
.task-leave-active {
  transition: all 0.3s ease;
}

.task-enter-from {
  opacity: 0;
  transform: translateY(-20px) scale(0.95);
}

.task-leave-to {
  opacity: 0;
  transform: translateX(20px) scale(0.95);
}

.task-move {
  transition: transform 0.3s ease;
}
</style>
```

### Success Criteria
- [ ] Advanced TaskCard component with inline editing
- [ ] Task animation composable working
- [ ] Date formatting composable functional  
- [ ] Task validation composable implemented
- [ ] Component lifecycle management correct
- [ ] Event handling and communication working
- [ ] Keyboard shortcuts functional
- [ ] Accessibility features implemented

### 💡 Key Learning Points

1. **Advanced Composition API Patterns**: Creating complex, reusable logic with composables
2. **Component Communication**: Sophisticated props and event handling
3. **Reactive Programming**: Deep understanding of Vue's reactivity system
4. **Code Organization**: Separating concerns for maintainability
5. **Accessibility**: Building inclusive user interfaces

### 🎯 Looking Ahead

In Chapter 8, you'll explore Vue's reactivity system in detail, learning how to optimize performance and handle complex reactive scenarios. The composable patterns you've built here form the foundation for advanced Vue.js development!

**Your advanced component architecture is complete** - experience the power of the Composition API!