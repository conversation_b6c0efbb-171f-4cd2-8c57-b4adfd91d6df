"""
Drum management endpoints
IMPORTANT: These endpoints interact directly with the recoater hardware drums, use only with DebugView!

- GET /drums/{drum_id}/geometry: Download geometry file directly from recoater hardware (not cache)
- GET /drums/{drum_id}/geometry/preview
- POST /drums/{drum_id}/geometry
- DELETE /drums/{drum_id}/geometry
"""
import logging
from fastapi import APIRouter, HTTPException, Depends, Response, Path, UploadFile, File

from app.dependencies import get_recoater_client, get_multilayer_job_manager
from infrastructure.recoater_client import (
    RecoaterClient,
    RecoaterConnectionError,
    RecoaterAPIError,
)
from app.services.job_management.multimaterial_job_service import MultiMaterialJobService
from .models import FileUploadResponse, FileDeleteResponse

logger = logging.getLogger(__name__)
router = APIRouter()

# API call to get CLI file from cache and display returned data
from fastapi import APIRouter, HTTPException, Depends, Response, Path, UploadFile, File, Query

@router.get("/drums/{drum_id}/geometry/preview")
async def get_drum_geometry_preview(
    drum_id: int = Path(..., ge=0, le=2, description="The drum's ID (0-2)"),
    layer: int = Query(1, ge=1, description="Layer number to preview (1-based, defaults to 1)"),
    job_manager: MultiMaterialJobService = Depends(get_multilayer_job_manager),
) -> Response:
    """Get geometry file from a specific drum as PNG preview."""
    try:
        logger.info(f"Getting geometry preview from drum {drum_id} cache, layer {layer}")
        
        # Get cached file for this drum
        drum_data = job_manager.get_cached_file_for_drum(drum_id)
        if drum_data is None:
            raise HTTPException(
                status_code=404, 
                detail=f"No CLI file cached for drum {drum_id}. Please upload a file first."
            )
        
        # Get the specified layer for preview
        parsed_file = drum_data['parsed_file']
        if not parsed_file.layers:
            raise HTTPException(
                status_code=400,
                detail=f"Drum {drum_id} cached file has no layers"
            )
        
        # Validate layer number
        layer_count = len(parsed_file.layers)
        if layer > layer_count:
            raise HTTPException(
                status_code=400,
                detail=f"Layer {layer} exceeds available layers. Drum {drum_id} has {layer_count} layers (range: 1-{layer_count})"
            )
        
        # Render specified layer to PNG (convert 1-based to 0-based index)
        target_layer = parsed_file.layers[layer - 1]
        image_data = job_manager.cli_parser.render_layer_to_png(target_layer, drum_id=drum_id)
        
        logger.info(f"Successfully rendered layer {layer} for drum {drum_id}")
        return Response(
            content=image_data,
            media_type="image/png",
            headers={"Content-Disposition": f"inline; filename=drum_{drum_id}_layer_{layer}_preview.png"},
        )
    except HTTPException:
        # Re-raise HTTP exceptions as-is (they have proper status codes and messages)
        raise
    except Exception as e:
        logger.error(f"Unexpected error generating preview for drum {drum_id}, layer {layer}: {e}")
        raise HTTPException(status_code=500, detail=f"Internal error generating preview: {e}")

# Direct API call to upload file to selected Drum
@router.post("/drums/{drum_id}/geometry", response_model=FileUploadResponse)
async def upload_drum_geometry(
    drum_id: int = Path(..., ge=0, le=2, description="The drum's ID (0-2)"),
    file: UploadFile = File(...),
    recoater_client: RecoaterClient = Depends(get_recoater_client),
) -> FileUploadResponse:
    """Upload geometry file (PNG or CLI) to a specific drum."""
    try:
        logger.info(f"Uploading geometry file to drum {drum_id}: {file.filename}")
        file_data = await file.read()
        recoater_client.upload_drum_geometry(
            drum_id=drum_id,
            file_data=file_data,
            content_type=file.content_type or "application/octet-stream",
        )
        return FileUploadResponse(
            success=True,
            message=f"File {file.filename} uploaded successfully to drum {drum_id}",
            drum_id=drum_id,
            file_size=len(file_data),
            content_type=file.content_type or "application/octet-stream",
        )
    except RecoaterConnectionError as e:
        logger.error(f"Connection error uploading file to drum {drum_id}: {e}")
        raise HTTPException(status_code=503, detail=f"Connection error: {e}")
    except RecoaterAPIError as e:
        logger.error(f"API error uploading file to drum {drum_id}: {e}")
        raise HTTPException(status_code=400, detail=f"API error: {e}")
    except Exception as e:
        logger.error(f"Unexpected error uploading file to drum {drum_id}: {e}")
        raise HTTPException(status_code=500, detail=f"Internal error: {e}")

# Direct API call to download CLI file from selected Drum
@router.get("/drums/{drum_id}/geometry")
async def download_drum_geometry(
    drum_id: int = Path(..., ge=0, le=2, description="The drum's ID (0-2)"),
    recoater_client: RecoaterClient = Depends(get_recoater_client),
) -> Response:
    """
    Download geometry file directly from the recoater hardware as a PNG image.

    Warning: This endpoint does NOT read from the backend cache. If you are using the
    cached multi-material workflow, prefer reusing your original uploaded CLI file.
    The upload and delete actions in the UI refer to the backend cache, not hardware.
    """
    try:
        logger.info(f"Downloading geometry file from drum {drum_id}")
        image_data = recoater_client.download_drum_geometry(drum_id)
        return Response(
            content=image_data,
            media_type="image/png",
            headers={"Content-Disposition": f"attachment; filename=drum_{drum_id}_geometry.png"},
        )
    except RecoaterConnectionError as e:
        logger.error(f"Connection error downloading file from drum {drum_id}: {e}")
        raise HTTPException(status_code=503, detail=f"Connection error: {e}")
    except RecoaterAPIError as e:
        logger.error(f"API error downloading file from drum {drum_id}: {e}")
        raise HTTPException(status_code=400, detail=f"API error: {e}")
    except Exception as e:
        logger.error(f"Unexpected error downloading file from drum {drum_id}: {e}")
        raise HTTPException(status_code=500, detail=f"Internal error: {e}")

# Direct API call to delete CLI file from selected Drum
@router.delete("/drums/{drum_id}/geometry", response_model=FileDeleteResponse)
async def delete_drum_geometry(
    drum_id: int = Path(..., ge=0, le=2, description="The drum's ID (0-2)"),
    recoater_client: RecoaterClient = Depends(get_recoater_client),
) -> FileDeleteResponse:
    """Delete geometry file from a specific drum."""
    try:
        logger.info(f"Deleting geometry file from drum {drum_id}")
        recoater_client.delete_drum_geometry(drum_id)
        return FileDeleteResponse(
            success=True,
            message=f"Geometry file deleted successfully from drum {drum_id}",
            drum_id=drum_id,
        )
    except RecoaterConnectionError as e:
        logger.error(f"Connection error deleting file from drum {drum_id}: {e}")
        raise HTTPException(status_code=503, detail=f"Connection error: {e}")
    except RecoaterAPIError as e:
        logger.error(f"API error deleting file from drum {drum_id}: {e}")
        raise HTTPException(status_code=400, detail=f"API error: {e}")
    except Exception as e:
        logger.error(f"Unexpected error deleting file from drum {drum_id}: {e}")
        raise HTTPException(status_code=500, detail=f"Internal error: {e}")
    