# Job Error Card Removal - UI Cleanup

## Current UI Layout (Redundant)

```
┌─────────────────────────────────────────────────────────┐
│                Job Progress Card                        │
├─────────────────────────────────────────────────────────┤
│                                                         │
│  ┌─────────────────────────────────────────────────┐   │
│  │          System Error Status                    │   │ ✅ KEEP
│  │  🔴 Errors Detected                            │   │
│  │  ┌─────────────────────────────────────────┐   │   │
│  │  │ Backend Error: Active                   │   │   │
│  │  └─────────────────────────────────────────┘   │   │
│  │  [Clear Errors]                                │   │
│  └─────────────────────────────────────────────────┘   │
│                                                         │
│  ┌─────────────────────────────────────────────────┐   │
│  │          Job Error                              │   │ ❌ REMOVE
│  │  🟡 Job Issue                                  │   │
│  │  Recoater reported error during layer completion │   │
│  │  3:59:17 PM                                    │   │
│  └─────────────────────────────────────────────────┘   │ (Redundant & Flickering)
│                                                         │
│  ┌─────────────────────────────────────────────────┐   │
│  │          Recent Errors                          │   │ ✅ KEEP
│  │  [Clear History]                               │   │
│  │  • Error 1                                     │   │
│  │  • Error 2                                     │   │
│  └─────────────────────────────────────────────────┘   │
│                                                         │
│  ┌─────────────────────────────────────────────────┐   │
│  │          Coordination Status                    │   │ ✅ KEEP
│  │  Job Status: Error                             │   │
│  │  System Message: Specific error details...     │   │
│  └─────────────────────────────────────────────────┘   │
│                                                         │
└─────────────────────────────────────────────────────────┘
```

## After Cleanup (Streamlined)

```
┌─────────────────────────────────────────────────────────┐
│                Job Progress Card                        │
├─────────────────────────────────────────────────────────┤
│                                                         │
│  ┌─────────────────────────────────────────────────┐   │
│  │          System Error Status                    │   │ ✅ KEPT
│  │  🔴 Errors Detected                            │   │
│  │  ┌─────────────────────────────────────────┐   │   │
│  │  │ Backend Error: Active                   │   │   │
│  │  └─────────────────────────────────────────┘   │   │
│  │  [Clear Errors]                                │   │
│  └─────────────────────────────────────────────────┘   │
│                                                         │ ❌ REMOVED
│                                                         │ (No more flickering
│                                                         │  Job Error card)
│  ┌─────────────────────────────────────────────────┐   │
│  │          Recent Errors                          │   │ ✅ KEPT
│  │  [Clear History]                               │   │
│  │  • Error 1                                     │   │
│  │  • Error 2                                     │   │
│  └─────────────────────────────────────────────────┘   │
│                                                         │
│  ┌─────────────────────────────────────────────────┐   │
│  │          Coordination Status                    │   │ ✅ KEPT
│  │  Job Status: Error                             │   │
│  │  System Message: Specific error details...     │   │
│  └─────────────────────────────────────────────────┘   │
│                                                         │
└─────────────────────────────────────────────────────────┘
```

## Redundancy Analysis

### Job Error Card Problems:
1. **Functional Redundancy**: Same information already shown in:
   - System Error Status (error flags)
   - Coordination Status (specific messages)
   - Recent Errors (historical tracking)

2. **Visual Flickering**: 
   - Appears/disappears based on `errorMessage` presence
   - Contributes to the polling conflict issues
   - Creates visual distraction

3. **Information Overlap**:
   - Job Error: "Recoater reported error during layer completion"
   - Coordination Status: Shows same message
   - System Error Status: Shows error flags are active

### Benefits of Removal:
- ✅ Eliminates flickering behavior
- ✅ Reduces visual clutter
- ✅ Maintains all essential error information
- ✅ Improves operator focus on actionable items
- ✅ Follows UI cleanup memory guidelines

## Error Information Flow After Cleanup

```
Error Occurs
     │
     ▼
┌─────────────────────┐
│ CriticalErrorModal  │ ← Primary alert (modal)
│ "Acknowledge/Clear" │
└─────────────────────┘
     │
     ▼ (after acknowledgment)
┌─────────────────────┐
│ System Error Status │ ← Shows error flags active
│ "Clear Errors"      │
└─────────────────────┘
     │
     ▼ (during active job)
┌─────────────────────┐
│ Coordination Status │ ← Shows specific error details
│ "System Message"    │
└─────────────────────┘
     │
     ▼ (historical tracking)
┌─────────────────────┐
│ Recent Errors       │ ← Error history for analysis
│ "Clear History"     │
└─────────────────────┘
```

This creates a **cleaner information hierarchy** without redundant Job Error card flickering!