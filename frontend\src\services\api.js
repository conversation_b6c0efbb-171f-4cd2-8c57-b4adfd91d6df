/**
 * API Service
 * ===========
 *
 * Centralized service for making HTTP requests to the backend API.
 * All axios calls should go through this service to maintain consistency
 * and enable easy mocking for tests.
 */

import axios from 'axios'

// Create axios instance with default configuration
const apiClient = axios.create({
  baseURL: '/api/v1',
  timeout: 10000,
  headers: {
    'Content-Type': 'application/json'
  }
})

// Request interceptor for logging
apiClient.interceptors.request.use(
  (config) => {
    console.log(`API Request: ${config.method?.toUpperCase()} ${config.url}`)
    return config
  },
  (error) => {
    console.error('API Request Error:', error)
    return Promise.reject(error)
  }
)

// Response interceptor for logging and error handling
apiClient.interceptors.response.use(
  (response) => {
    console.log(`API Response: ${response.status} ${response.config.url}`)
    return response
  },
  (error) => {
    console.error('API Response Error:', error.response?.status, error.message)
    return Promise.reject(error)
  }
)

/**
 * API service object containing all API methods
 */
const apiService = {
  /**
   * Get the current status of the recoater system
   * @returns {Promise} Axios response promise
   */
  getStatus() {
    return apiClient.get('/status/')
  },

  /**
   * Perform a health check
   * @returns {Promise} Axios response promise
   */
  getHealth() {
    return apiClient.get('/status/health')
  },

  /**
   * Restart the recoater server
   * @returns {Promise} Axios response promise
   */
  restartServer() {
    return apiClient.post('/status/state?action=restart')
  },

  /**
   * Shutdown the recoater server
   * @returns {Promise} Axios response promise
   */
  shutdownServer() {
    return apiClient.post('/status/state?action=shutdown')
  },

  /**
   * Get recoater configuration
   * @returns {Promise} Axios response promise
   */
  getConfig() {
    return apiClient.get('/config')
  },

  /**
   * Set recoater configuration
   * @param {Object} config - Configuration object
   * @returns {Promise} Axios response promise
   */
  setConfig(config) {
    return apiClient.put('/config', config)
  },

  /**
   * Get drums information
   * @returns {Promise} Axios response promise
   */
  getDrums() {
    return apiClient.get('/drums')
  },

  /**
   * Get specific drum information
   * @param {number} drumId - Drum ID
   * @returns {Promise} Axios response promise
   */
  getDrum(drumId) {
    return apiClient.get(`/drums/${drumId}`)
  },



  // Drum Control API Methods

  /**
   * Get drum motion status
   * @param {number} drumId - Drum ID
   * @returns {Promise} Axios response promise
   */
  getDrumMotion(drumId) {
    return apiClient.get(`/recoater/drums/${drumId}/motion`)
  },

  /**
   * Set drum motion
   * @param {number} drumId - Drum ID
   * @param {Object} motionData - Motion parameters (mode, speed, distance, turns)
   * @returns {Promise} Axios response promise
   */
  setDrumMotion(drumId, motionData) {
    return apiClient.post(`/recoater/drums/${drumId}/motion`, motionData)
  },

  /**
   * Cancel drum motion
   * @param {number} drumId - Drum ID
   * @returns {Promise} Axios response promise
   */
  cancelDrumMotion(drumId) {
    return apiClient.delete(`/recoater/drums/${drumId}/motion`)
  },

  /**
   * Get drum ejection pressure
   * @param {number} drumId - Drum ID
   * @param {string} unit - Pressure unit ('pascal' or 'bar')
   * @returns {Promise} Axios response promise
   */
  getDrumEjection(drumId, unit = 'pascal') {
    return apiClient.get(`/recoater/drums/${drumId}/ejection`, { params: { unit } })
  },

  /**
   * Set drum ejection pressure
   * @param {number} drumId - Drum ID
   * @param {Object} ejectionData - Ejection parameters (target, unit)
   * @returns {Promise} Axios response promise
   */
  setDrumEjection(drumId, ejectionData) {
    return apiClient.put(`/recoater/drums/${drumId}/ejection`, ejectionData)
  },

  /**
   * Get drum suction pressure
   * @param {number} drumId - Drum ID
   * @returns {Promise} Axios response promise
   */
  getDrumSuction(drumId) {
    return apiClient.get(`/recoater/drums/${drumId}/suction`)
  },

  /**
   * Set drum suction pressure
   * @param {number} drumId - Drum ID
   * @param {Object} suctionData - Suction parameters (target)
   * @returns {Promise} Axios response promise
   */
  setDrumSuction(drumId, suctionData) {
    return apiClient.put(`/recoater/drums/${drumId}/suction`, suctionData)
  },

  // Blade Control API Methods

  /**
   * Get blade screws info
   * @param {number} drumId - Drum ID
   * @returns {Promise} Axios response promise
   */
  getBladeScrewsInfo(drumId) {
    return apiClient.get(`/recoater/drums/${drumId}/blade/screws`)
  },

  /**
   * Get blade screws motion status
   * @param {number} drumId - Drum ID
   * @returns {Promise} Axios response promise
   */
  getBladeScrewsMotion(drumId) {
    return apiClient.get(`/recoater/drums/${drumId}/blade/screws/motion`)
  },

  /**
   * Set blade screws motion
   * @param {number} drumId - Drum ID
   * @param {Object} motionData - Motion parameters (mode, distance)
   * @returns {Promise} Axios response promise
   */
  setBladeScrewsMotion(drumId, motionData) {
    return apiClient.post(`/recoater/drums/${drumId}/blade/screws/motion`, motionData)
  },

  /**
   * Cancel blade screws motion
   * @param {number} drumId - Drum ID
   * @returns {Promise} Axios response promise
   */
  cancelBladeScrewsMotion(drumId) {
    return apiClient.delete(`/recoater/drums/${drumId}/blade/screws/motion`)
  },

  /**
   * Get individual blade screw info
   * @param {number} drumId - Drum ID
   * @param {number} screwId - Screw ID
   * @returns {Promise} Axios response promise
   */
  getBladeScrew(drumId, screwId) {
    return apiClient.get(`/recoater/drums/${drumId}/blade/screws/${screwId}`)
  },

  /**
   * Get individual blade screw motion status
   * @param {number} drumId - Drum ID
   * @param {number} screwId - Screw ID
   * @returns {Promise} Axios response promise
   */
  getBladeScrewMotion(drumId, screwId) {
    return apiClient.get(`/recoater/drums/${drumId}/blade/screws/${screwId}/motion`)
  },

  /**
   * Set individual blade screw motion
   * @param {number} drumId - Drum ID
   * @param {number} screwId - Screw ID
   * @param {Object} motionData - Motion parameters (distance)
   * @returns {Promise} Axios response promise
   */
  setBladeScrewMotion(drumId, screwId, motionData) {
    return apiClient.post(`/recoater/drums/${drumId}/blade/screws/${screwId}/motion`, motionData)
  },

  /**
   * Cancel individual blade screw motion
   * @param {number} drumId - Drum ID
   * @param {number} screwId - Screw ID
   * @returns {Promise} Axios response promise
   */
  cancelBladeScrewMotion(drumId, screwId) {
    return apiClient.delete(`/recoater/drums/${drumId}/blade/screws/${screwId}/motion`)
  },

  // Leveler Control API Methods

  /**
   * Get leveler pressure information
   * @returns {Promise} Axios response promise
   */
  getLevelerPressure() {
    return apiClient.get('/recoater/leveler/pressure')
  },

  /**
   * Set leveler pressure target
   * @param {number} target - Target pressure in Pa
   * @returns {Promise} Axios response promise
   */
  setLevelerPressure(target) {
    return apiClient.put('/recoater/leveler/pressure', { target })
  },

  /**
   * Get leveler sensor state
   * @returns {Promise} Axios response promise
   */
  getLevelerSensor() {
    return apiClient.get('/recoater/leveler/sensor')
  },

  // Print Control API Methods

  /**
   * Get layer parameters
   * @returns {Promise} Axios response promise
   */
  getLayerParameters() {
    return apiClient.get('/print/layer/parameters')
  },

  /**
   * Set layer parameters
   * @param {Object} parameters - Layer parameters (filling_id, speed, powder_saving, x_offset)
   * @returns {Promise} Axios response promise
   */
  setLayerParameters(parameters) {
    return apiClient.put('/print/layer/parameters', parameters)
  },

  /**
   * Get layer preview image
   * @param {number} layer - The layer number to preview (defaults to 1)
   * @returns {Promise} Axios response promise with image data
   */
  getLayerPreview(layer = 1) {
    return apiClient.get('/print/layer/preview', {
      params: { layer },
      responseType: 'blob'
    })
  },

  /**
   * Get drum geometry preview image
   * @param {number} drumId - The ID of the drum to get preview from
   * @param {number} layer - The layer number to preview (defaults to 1)
   * @returns {Promise} Axios response promise with image data
   */
  getDrumGeometryPreview(drumId, layer = 1) {
    return apiClient.get(`/print/drums/${drumId}/geometry/preview`, {
      params: { layer },
      responseType: 'blob'
    })
  },

  /**
   * Debug: Get layer preview image directly from hardware
   * @param {number} layer - The layer number to preview (defaults to 1)
   * @returns {Promise} Axios response promise with image data
   */
  debugGetLayerPreview(layer = 1) {
    return apiClient.get('/print/layer/directpreview', {
      params: { layer },
      responseType: 'blob'
    })
  },

  /**
   * Get drum geometry preview image (for PrintView - cached workflow)
   * @param {number} drumId - The ID of the drum to get preview from
   * @param {number} layer - The layer number to preview (defaults to 1)
   * @returns {Promise} Axios response promise with image data
   */
  getDrumGeometryPreview(drumId, layer = 1) {
    return apiClient.get(`/print/drums/${drumId}/geometry/preview`, {
      params: { layer },
      responseType: 'blob'
    })
  },

  /**
   * Delete geometry file from a specific drum
   * @param {number} drumId - The ID of the drum to delete from
   * @returns {Promise} Axios response promise
   */
  deleteDrumGeometry(drumId) {
    return apiClient.delete(`/print/drums/${drumId}/geometry`)
  },

  /**
   * Start print job
   * @returns {Promise} Axios response promise
   */
  startPrintJob() {
    return apiClient.post('/print/job')
  },

  /**
   * Cancel print job
   * @returns {Promise} Axios response promise
   */
  cancelPrintJob() {
    return apiClient.delete('/print/job')
  },

  /**
   * Get print job status
   * @returns {Promise} Axios response promise
   */
  getPrintJobStatus() {
    return apiClient.get('/print/job/status')
  },

  // File Management Methods

  /**
   * Upload geometry file to a specific drum
   * @param {number} drumId - The ID of the drum to upload to
   * @param {File} file - The file to upload (PNG or CLI)
   * @returns {Promise} Axios response promise
   */
  uploadDrumGeometry(drumId, file) {
    const formData = new FormData()
    formData.append('file', file)

    return apiClient.post(`/print/drums/${drumId}/geometry`, formData, {
      headers: {
        'Content-Type': 'multipart/form-data'
      }
    })
  },

  /**
   * Download geometry file from a specific drum
   * @param {number} drumId - The ID of the drum to download from
   * @returns {Promise} Axios response promise with blob data
   */
  downloadDrumGeometry(drumId) {
    return apiClient.get(`/print/drums/${drumId}/geometry`, {
      responseType: 'blob'
    })
  },

  /**
   * Delete geometry file from a specific drum
   * @param {number} drumId - The ID of the drum to delete from
   * @returns {Promise} Axios response promise
   */
  // Clear cached CLI file for a specific drum (new workflow)
  clearDrumCache(drumId) {
    return apiClient.post(`/print/cli/clear-drum-cache/${drumId}`)
  },

  // CLI File Management Methods

  /**
   * Upload and parse a multi-layer CLI file
   * @param {File} file - The CLI file to upload
   * @returns {Promise} Axios response promise with file_id and total_layers
   */
  uploadCliFile(file) {
    const formData = new FormData()
    formData.append('file', file)

    return apiClient.post('/print/cli/upload', formData, {
      headers: {
        'Content-Type': 'multipart/form-data'
      },
      timeout: 300000
    })
  },

  /**
   * Upload a CLI file for a specific drum (new multimaterial workflow)
   * @param {File} file - The CLI file to upload
   * @param {number} drumId - The drum ID (0, 1, or 2)
   * @returns {Promise} Axios response promise with file_id and total_layers
   */
  uploadCliFileToDrum(file, drumId) {
    const formData = new FormData()
    formData.append('file', file)

    return apiClient.post(`/print/cli/upload/${drumId}`, formData, {
      headers: {
        'Content-Type': 'multipart/form-data'
      },
      timeout: 300000
    })
  },

  /**
   * Get status of cached CLI files for all drums
   * @returns {Promise} Axios response promise with drum cache status
   */
  getDrumCacheStatus() {
    return apiClient.get('/print/cli/drum-cache-status')
  },

  /**
   * Get a preview of a specific layer from a parsed CLI file
   * @param {string} fileId - The unique ID of the uploaded CLI file
   * @param {number} layerNum - The layer number to preview (1-based)
   * @returns {Promise} Axios response promise with image blob data
   */
  getCliLayerPreview(fileId, layerNum) {
    return apiClient.get(`/print/cli/${fileId}/layer/${layerNum}/preview`, {
      responseType: 'blob'
    })
  },
  /**
   * Send a single layer from preview-cached CLI to a drum and cache as a single-layer file (1 layer)
   * @param {string} fileId - preview cache file ID
   * @param {number} layerNum - 1-based layer number
   * @param {number} drumId - 0, 1, or 2
   * @returns {Promise} Axios response promise
   */
  sendLayerToDrum(fileId, layerNum, drumId) {
    return apiClient.post(`/print/cli/${fileId}/layer/${layerNum}/send/${drumId}`)
  },
  /**
   * Send a range of layers from preview-cached CLI to a drum
   * @param {string} fileId - preview cache file ID
   * @param {number} drumId - 0, 1, or 2
   * @param {number} startLayer - start layer (1-based)
   * @param {number} endLayer - end layer (1-based)
   * @returns {Promise} Axios response promise
   */
  sendLayerRangeToDrum(fileId, drumId, startLayer, endLayer) {
    return apiClient.post(`/print/cli/${fileId}/layers/send/${drumId}`, {
      start_layer: startLayer,
      end_layer: endLayer
    })
  },




  // Configuration API Methods

  /**
   * Get the current recoater configuration
   * @returns {Promise} Axios response promise with configuration data
   */
  async getConfiguration() {
    const response = await apiClient.get('/config/')
    return response.data
  },

  /**
   * Set the recoater configuration
   * @param {Object} config - Configuration object
   * @returns {Promise} Axios response promise
   */
  async setConfiguration(config) {
    const response = await apiClient.put('/config/', config)
    return response.data
  },

  // Multi-Material Job API Methods

  /**
   * Start a multi-material print job
   * @param {Object} fileIds - Optional mapping of drum_id (0,1,2) to file_id. If not provided, uses cached drum files.
   * @returns {Promise} Axios response promise
   */
  startMultiMaterialJob() {
    // Backend uses cached per-drum files; no payload is required
    return apiClient.post('/print/cli/start-multimaterial-job')
  },

  /**
   * Get the current status of the active multi-material job
   * @returns {Promise} Axios response promise
   */
  getMultiMaterialJobStatus() {
    return apiClient.get('/print/multimaterial-job/status')
  },

  /**
   * Cancel the current multi-material job
   * @returns {Promise} Axios response promise
   */
  cancelMultiMaterialJob() {
    return apiClient.post('/print/multimaterial-job/cancel')
  },

  /**
   * Clear backend_error and plc_error flags
   * @returns {Promise} Axios response promise
   */
  clearErrorFlags() {
    return apiClient.post('/errors/clear')
  },

  /**
   * Get status of a specific drum in the multi-material job
   * @param {number} drumId - The drum ID (0, 1, or 2)
   * @returns {Promise} Axios response promise
   */
  getDrumStatus(drumId) {
    return apiClient.get(`/print/multimaterial-job/drum-status/${drumId}`)
  },

  // Debug API Methods - Direct hardware interaction for debugging

  /**
   * Debug: Upload geometry file directly to drum hardware
   * @param {number} drumId - The ID of the drum to upload to
   * @param {File} file - The file to upload (PNG or CLI)
   * @returns {Promise} Axios response promise
   */
  debugUploadDrumGeometry(drumId, file) {
    const formData = new FormData()
    formData.append('file', file)

    return apiClient.post(`/print/drums/${drumId}/geometry`, formData, {
      headers: {
        'Content-Type': 'multipart/form-data'
      }
    })
  },

  /**
   * Debug: Download geometry file directly from drum hardware
   * @param {number} drumId - The ID of the drum to download from
   * @returns {Promise} Axios response promise with blob data
   */
  debugDownloadDrumGeometry(drumId) {
    return apiClient.get(`/print/drums/${drumId}/geometry`, {
      responseType: 'blob'
    })
  },

  /**
   * Debug: Delete geometry file directly from drum hardware
   * @param {number} drumId - The ID of the drum to delete from
   * @returns {Promise} Axios response promise
   */
  debugDeleteDrumGeometry(drumId) {
    return apiClient.delete(`/print/drums/${drumId}/geometry`)
  },

  /**
   * Debug: Get drum geometry preview directly from hardware
   * @param {number} drumId - The ID of the drum to get preview from
   * @param {number} layer - The layer number to preview (defaults to 1)
   * @returns {Promise} Axios response promise with image data
   */
  debugGetDrumGeometryPreview(drumId, layer = 1) {
    return apiClient.get(`/print/drums/${drumId}/geometry/preview`, {
      params: { layer },
      responseType: 'blob'
    })
  }
}

export { apiService }
export default apiService
