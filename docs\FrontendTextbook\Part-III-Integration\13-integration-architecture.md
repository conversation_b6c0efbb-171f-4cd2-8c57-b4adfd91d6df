# Chapter 13: Integration Architecture
## How HTML, CSS, JavaScript, and Vue.js Work Together

```
╔══════════════════════════════════════════════════════════════════════════════════════╗
║                         CHAPTER 13: INTEGRATION ARCHITECTURE                         ║
║                    Connecting All Technologies in Real Applications                  ║
╚══════════════════════════════════════════════════════════════════════════════════════╝
```

## Chapter Objectives

By the end of this chapter, you will understand:
- How HTML, CSS, JavaScript, and Vue.js integrate to create complete applications
- The transformation from traditional web development to modern Vue.js applications
- Real-world integration patterns used in industrial applications
- The benefits of component-based architecture

## The Complete Integration Story

### Traditional Web Development vs Vue.js

**Traditional Approach (What We DON'T Do):**
```html
<!-- Separate files, manual integration -->
<!-- index.html -->
<div class="layer-preview-container">
  <select id="preview-source">
    <option value="drum-0">Drum 0</option>
  </select>
  <div id="layer-input-group" style="display: none;">
    <input type="number" id="layer-number" min="1" value="1">
  </div>
  <button id="load-button" disabled>Load Preview</button>
  <img id="preview-image" style="display: none;">
</div>

<script>
// Manual DOM manipulation
const previewSource = document.getElementById('preview-source')
const layerInputGroup = document.getElementById('layer-input-group')
const loadButton = document.getElementById('load-button')

previewSource.addEventListener('change', function() {
  if (this.value.startsWith('drum-')) {
    layerInputGroup.style.display = 'block'
  } else {
    layerInputGroup.style.display = 'none'
  }
})

loadButton.addEventListener('click', async function() {
  // Manual state management
  loadButton.disabled = true
  loadButton.textContent = 'Loading...'
  
  try {
    const response = await fetch('/api/preview')
    const blob = await response.blob()
    document.getElementById('preview-image').src = URL.createObjectURL(blob)
  } finally {
    loadButton.disabled = false
    loadButton.textContent = 'Load Preview'
  }
})
</script>
```

**Vue.js Approach (What We DO):**
```vue
<!-- Single file component - everything integrated -->
<template>
  <div class="layer-preview-container">
    <!-- Two-way binding replaces manual event listeners -->
    <select v-model="previewSource">
      <option value="drum-0">Drum 0</option>
    </select>
    
    <!-- Conditional rendering replaces manual DOM manipulation -->
    <div v-if="previewSource.startsWith('drum-')" class="layer-input-group">
      <input 
        v-model.number="selectedLayerNumber" 
        type="number" 
        :min="1" 
        :max="getMaxLayersForSelectedDrum()"
      >
    </div>
    
    <!-- Reactive button state -->
    <button 
      @click="loadPreview" 
      :disabled="!statusStore.isConnected || previewLoading"
    >
      {{ previewLoading ? 'Loading...' : 'Load Preview' }}
    </button>
    
    <!-- Conditional image display -->
    <img v-if="previewImageUrl" :src="previewImageUrl">
  </div>
</template>

<script setup>
import { ref } from 'vue'
import { useStatusStore } from '@/stores/status'
import apiService from '@/services/api'

// Reactive state - automatically updates UI
const selectedLayerNumber = ref(1)
const previewLoading = ref(false)
const previewSource = ref('drum-0')
const previewImageUrl = ref('')
const statusStore = useStatusStore()

// Reactive method - automatically handles state
const loadPreview = async () => {
  previewLoading.value = true
  try {
    const response = await apiService.getDrumGeometryPreview(
      parseInt(previewSource.value.split('-')[1]), 
      selectedLayerNumber.value
    )
    previewImageUrl.value = URL.createObjectURL(response.data)
  } finally {
    previewLoading.value = false
  }
}
</script>

<style scoped>
.layer-preview-container {
  display: flex;
  gap: 1rem;
  padding: 1rem;
}
</style>
```

## Architecture Visualization

```
┌─────────────────────────────────────────────────────────────────────────────────────┐
│                              COMPLETE INTEGRATION                                  │
├─────────────────────────────────────────────────────────────────────────────────────┤
│                                                                                     │
│ HTML Structure            CSS Styling             JavaScript Logic                   │
│       │                        │                        │                          │
│       ▼                        ▼                        ▼                          │
│ ┌─────────────┐          ┌─────────────┐          ┌─────────────┐                   │
│ │ <template>  │          │ <style>     │          │ <script>    │                   │
│ │             │          │             │          │             │                   │
│ │ • Elements  │◄────────▶│ • Classes   │◄────────▶│ • Reactive  │                   │
│ │ • Structure │          │ • Styles    │          │   State     │                   │
│ │ • v-model   │          │ • Layout    │          │ • Methods   │                   │
│ │ • v-if      │          │ • Colors    │          │ • Computed  │                   │
│ │ • @click    │          │             │          │             │                   │
│ └─────────────┘          └─────────────┘          └─────────────┘                   │
│       │                        │                        │                          │
│       └────────────────────────┼────────────────────────┘                          │
│                                ▼                                                   │
│                        ┌─────────────┐                                             │
│                        │   Vue.js    │                                             │
│                        │ Reactivity  │                                             │
│                        │   System    │                                             │
│                        │             │                                             │
│                        │ • Watches   │                                             │
│                        │ • Updates   │                                             │
│                        │ • Syncs     │                                             │
│                        └─────────────┘                                             │
│                                │                                                   │
│                                ▼                                                   │
│                        ┌─────────────┐                                             │
│                        │    Pinia    │                                             │
│                        │ State Store │                                             │
│                        │             │                                             │
│                        │ • Global    │                                             │
│                        │   State     │                                             │
│                        │ • Shared    │                                             │
│                        │   Logic     │                                             │
│                        └─────────────┘                                             │
│                                                                                     │
└─────────────────────────────────────────────────────────────────────────────────────┘
```

## Data Flow Integration

### Complete User Interaction Flow

```
┌─────────────────────────────────────────────────────────────────────────────────────┐
│                               USER INTERACTION FLOW                                │
├─────────────────────────────────────────────────────────────────────────────────────┤
│                                                                                     │
│ 1. HTML Elements                2. Vue Directives            3. JavaScript Logic   │
│    │                                  │                           │                │
│    ▼                                  ▼                           ▼                │
│ ┌─────────────┐               ┌─────────────┐             ┌─────────────┐          │
│ │ User clicks │──────────────▶│ @click      │────────────▶│ loadPreview │          │
│ │ button      │               │ directive   │             │ function    │          │
│ └─────────────┘               └─────────────┘             └─────────────┘          │
│                                                                   │                │
│                                                                   ▼                │
│ 4. Reactive State             5. CSS Updates              6. DOM Updates           │
│    │                                  │                           │                │
│    ▼                                  ▼                           ▼                │
│ ┌─────────────┐               ┌─────────────┐             ┌─────────────┐          │
│ │ previewLoad │──────────────▶│ :disabled   │────────────▶│ Button      │          │
│ │ ing = true  │               │ class       │             │ becomes     │          │
│ └─────────────┘               └─────────────┘             └─disabled────┘          │
│                                                                   │                │
│                                                                   ▼                │
│ 7. API Call                   8. Response                  9. UI Update            │
│    │                                  │                           │                │
│    ▼                                  ▼                           ▼                │
│ ┌─────────────┐               ┌─────────────┐             ┌─────────────┐          │
│ │ await api   │──────────────▶│ response    │────────────▶│ Image       │          │
│ │ Service.get │               │ data        │             │ displays    │          │
│ └─────────────┘               └─────────────┘             └─────────────┘          │
│                                                                                     │
└─────────────────────────────────────────────────────────────────────────────────────┘
```

## Component Architecture Benefits

### Single File Components (.vue)

**The Power of Co-location:**
```vue
<!-- Everything for one feature in one place -->
<template>
  <!-- HTML: What the user sees -->
  <div class="drum-selector">
    <label>Select Drum:</label>
    <select v-model="selectedDrum">
      <option v-for="drum in availableDrums" :key="drum.id" :value="drum.id">
        {{ drum.name }}
      </option>
    </select>
  </div>
</template>

<script setup>
// JavaScript: How it behaves
import { ref, computed } from 'vue'
import { usePrintJobStore } from '@/stores/printJob'

const printJobStore = usePrintJobStore()
const selectedDrum = ref(0)

const availableDrums = computed(() => {
  return Object.entries(printJobStore.uploadedFiles)
    .filter(([id, file]) => file !== null)
    .map(([id, file]) => ({
      id: parseInt(id),
      name: `Drum ${id} (${file.name})`
    }))
})
</script>

<style scoped>
/* CSS: How it looks - only affects this component */
.drum-selector {
  display: flex;
  flex-direction: column;
  gap: 0.5rem;
  padding: 1rem;
  border: 1px solid var(--border-color);
  border-radius: 0.5rem;
}

.drum-selector label {
  font-weight: 600;
  color: var(--label-color);
}

.drum-selector select {
  padding: 0.5rem;
  border: 1px solid var(--input-border-color);
  border-radius: 0.25rem;
}
</style>
```

### Composition and Reusability

**Breaking Complex UI into Components:**
```vue
<!-- Parent Component: PrintView.vue -->
<template>
  <div class="print-view">
    <DrumSelector v-model="selectedDrum" />
    <LayerSelector v-model="selectedLayer" :drum-id="selectedDrum" />
    <PreviewDisplay :drum-id="selectedDrum" :layer="selectedLayer" />
  </div>
</template>

<script setup>
import DrumSelector from '@/components/DrumSelector.vue'
import LayerSelector from '@/components/LayerSelector.vue'
import PreviewDisplay from '@/components/PreviewDisplay.vue'

const selectedDrum = ref(0)
const selectedLayer = ref(1)
</script>
```

## State Management Integration

### How Pinia Connects Everything

```javascript
// Store: Central state management
export const usePrintJobStore = defineStore('printJob', () => {
  const uploadedFiles = ref({
    0: null,
    1: null, 
    2: null
  })
  
  const updateFile = (drumId, fileData) => {
    uploadedFiles.value[drumId] = fileData
  }
  
  return { uploadedFiles, updateFile }
})

// Component A: Upload component
const printJobStore = usePrintJobStore()
const handleFileUpload = (drumId, file) => {
  printJobStore.updateFile(drumId, {
    name: file.name,
    size: file.size,
    layerCount: parseLayerCount(file)
  })
}

// Component B: Preview component (automatically updates)
const printJobStore = usePrintJobStore()
const getMaxLayers = (drumId) => {
  return printJobStore.uploadedFiles[drumId]?.layerCount || 0
}
```

**Visual State Flow:**
```
┌─────────────────────────────────────────────────────────────────────────────────────┐
│                              PINIA STATE INTEGRATION                               │
├─────────────────────────────────────────────────────────────────────────────────────┤
│                                                                                     │
│ Upload Component          Pinia Store              Preview Component               │
│       │                       │                           │                       │
│       ▼                       ▼                           ▼                       │
│ ┌─────────────┐         ┌─────────────┐           ┌─────────────┐                 │
│ │ User        │         │ Central     │           │ Reads file  │                 │
│ │ uploads     │────────▶│ State       │──────────▶│ data        │                 │
│ │ file        │         │             │           │             │                 │
│ └─────────────┘         │ • Files     │           │ • Max layers│                 │
│                         │ • Metadata  │           │ • File name │                 │
│                         │ • Status    │           │ • etc.      │                 │
│                         └─────────────┘           └─────────────┘                 │
│                                 │                                                 │
│                                 ▼                                                 │
│                         ┌─────────────┐                                           │
│                         │ All other   │                                           │
│                         │ components  │                                           │
│                         │ auto-update │                                           │
│                         └─────────────┘                                           │
│                                                                                     │
└─────────────────────────────────────────────────────────────────────────────────────┘
```

## Performance Benefits of Integration

### What Vue.js Gives Us

1. **Automatic UI Updates**: Change `previewLoading.value = true` and button automatically shows "Loading..."

2. **Declarative Code**: Instead of "when this happens, do that", we declare "the UI should look like this based on data"

3. **Single Source of Truth**: All UI state comes from reactive JavaScript variables

4. **No Manual DOM Manipulation**: Vue automatically updates the DOM when data changes

5. **Component Isolation**: CSS is scoped, JavaScript is contained, templates are clear

### Performance Comparison

**Traditional (Manual Updates):**
```javascript
// Every state change requires manual UI updates
function startLoading() {
  isLoading = true
  document.getElementById('button').disabled = true
  document.getElementById('button').textContent = 'Loading...'
  document.getElementById('spinner').style.display = 'block'
}

function stopLoading() {
  isLoading = false
  document.getElementById('button').disabled = false
  document.getElementById('button').textContent = 'Load Preview'
  document.getElementById('spinner').style.display = 'none'
}
```

**Vue.js (Automatic Updates):**
```javascript
// One state change automatically updates all related UI
const startLoading = () => {
  previewLoading.value = true  // Everything else updates automatically
}
```

## Integration Best Practices

### 1. Component Design Principles
- **Single Responsibility**: Each component does one thing well
- **Clear Interfaces**: Props in, events out
- **Encapsulation**: Styles and logic contained within component

### 2. State Management
- **Local State**: Use `ref()` for component-specific data
- **Global State**: Use Pinia for shared data
- **Computed Properties**: Derive data instead of duplicating

### 3. Performance Optimization
- **Lazy Loading**: Load components only when needed
- **Efficient Reactivity**: Use `computed()` for derived values
- **Memory Management**: Clean up resources in `onUnmounted()`

## Real-World Benefits

### Before Vue.js (Problems)
- Manual DOM synchronization
- Event listener management
- State inconsistencies
- Code scattered across files
- Difficult debugging

### After Vue.js (Solutions)
- Automatic reactivity
- Declarative templates
- Single source of truth
- Component organization
- Great developer tools

## Key Takeaways

### Mental Model for Integration
1. **HTML** = The skeleton (what content exists)
2. **CSS** = The appearance (how content looks)  
3. **JavaScript** = The brain (how content behaves)
4. **Vue.js** = The nervous system (connects everything automatically)

### Professional Development Benefits
- **Faster Development**: Less boilerplate code
- **Fewer Bugs**: Automatic UI synchronization
- **Better Maintenance**: Clear component boundaries
- **Team Collaboration**: Consistent patterns

## Complete Real-World Example: Layer Preview System

Let's implement a complete feature that shows all technologies working together.

### The Complete Layer Preview Component

```vue
<!-- LayerPreviewSystem.vue -->
<template>
  <div class="layer-preview-system">
    <!-- Error Boundary -->
    <div v-if="systemError" class="system-error">
      <h3>System Error</h3>
      <p>{{ systemError }}</p>
      <button @click="retrySystem">Retry</button>
    </div>
    
    <!-- Main Interface -->
    <div v-else class="preview-interface">
      <!-- HTML: Semantic structure -->
      <header class="preview-header">
        <h1>Layer Preview System</h1>
        <div class="connection-status" :class="connectionClass">
          {{ connectionStatus }}
        </div>
      </header>
      
      <!-- HTML: Form with proper validation -->
      <form class="preview-controls" @submit.prevent="loadPreview">
        <fieldset :disabled="loading">
          <legend>Preview Configuration</legend>
          
          <!-- Drum Selection -->
          <div class="control-group">
            <label for="drum-select">Select Drum:</label>
            <select 
              id="drum-select"
              v-model="selectedDrum" 
              required
              @change="handleDrumChange"
            >
              <option value="">Choose a drum...</option>
              <option 
                v-for="drum in availableDrums" 
                :key="drum.id" 
                :value="drum.id"
              >
                Drum {{ drum.id }} ({{ drum.fileName || 'No file' }})
              </option>
            </select>
          </div>
          
          <!-- Layer Selection -->
          <div v-if="selectedDrum" class="control-group">
            <label for="layer-input">Layer Number:</label>
            <input 
              id="layer-input"
              v-model.number="selectedLayer" 
              type="number" 
              :min="1" 
              :max="maxLayers"
              required
            >
            <span class="layer-info">/ {{ maxLayers }}</span>
          </div>
          
          <!-- Load Button -->
          <button 
            type="submit" 
            class="load-button"
            :disabled="!canLoadPreview"
          >
            <span v-if="loading" class="loading-spinner"></span>
            {{ loading ? 'Loading...' : 'Load Preview' }}
          </button>
        </fieldset>
      </form>
      
      <!-- HTML: Preview Display Area -->
      <main class="preview-display">
        <!-- Loading State -->
        <div v-if="loading" class="preview-loading">
          <div class="loading-animation"></div>
          <p>Generating layer preview...</p>
        </div>
        
        <!-- Error State -->
        <div v-else-if="previewError" class="preview-error">
          <h3>Preview Error</h3>
          <p>{{ previewError }}</p>
          <button @click="retryPreview">Try Again</button>
        </div>
        
        <!-- Success State -->
        <div v-else-if="previewImage" class="preview-content">
          <img 
            :src="previewImage" 
            :alt="`Layer ${selectedLayer} preview for Drum ${selectedDrum}`"
            class="preview-image"
            @load="handleImageLoad"
            @error="handleImageError"
          >
          <div class="preview-info">
            <p>Drum {{ selectedDrum }}, Layer {{ selectedLayer }}</p>
            <p>Generated: {{ previewTimestamp }}</p>
          </div>
        </div>
        
        <!-- Empty State -->
        <div v-else class="preview-empty">
          <p>Select a drum and layer to view preview</p>
        </div>
      </main>
    </div>
  </div>
</template>

<script setup>
// JavaScript: Modern ES6+ with Vue 3 Composition API
import { ref, computed, watch, onMounted, onBeforeUnmount } from 'vue'
import { useStatusStore } from '@/stores/statusStore'
import { usePrintJobStore } from '@/stores/printJobStore'
import apiService from '@/services/apiService'

// State Management: Pinia stores for global state
const statusStore = useStatusStore()
const printJobStore = usePrintJobStore()

// Reactive State: Local component state
const selectedDrum = ref('')
const selectedLayer = ref(1)
const loading = ref(false)
const previewImage = ref('')
const previewError = ref('')
const systemError = ref('')
const previewTimestamp = ref('')

// Computed Properties: Derived state
const connectionStatus = computed(() => {
  return statusStore.isConnected ? 'Connected' : 'Disconnected'
})

const connectionClass = computed(() => {
  return statusStore.isConnected ? 'connected' : 'disconnected'
})

const availableDrums = computed(() => {
  return Object.entries(printJobStore.uploadedFiles)
    .filter(([id, file]) => file !== null)
    .map(([id, file]) => ({
      id: parseInt(id),
      fileName: file.name,
      layerCount: file.layerCount
    }))
})

const maxLayers = computed(() => {
  if (!selectedDrum.value) return 1
  const drum = availableDrums.value.find(d => d.id === parseInt(selectedDrum.value))
  return drum?.layerCount || 1
})

const canLoadPreview = computed(() => {
  return selectedDrum.value && 
         selectedLayer.value && 
         !loading.value && 
         statusStore.isConnected
})

// Event Handlers: User interactions
const handleDrumChange = () => {
  selectedLayer.value = 1
  previewImage.value = ''
  previewError.value = ''
}

const loadPreview = async () => {
  if (!canLoadPreview.value) return
  
  loading.value = true
  previewError.value = ''
  
  try {
    // API Integration: Async operation with error handling
    const response = await apiService.getDrumGeometryPreview(
      parseInt(selectedDrum.value), 
      selectedLayer.value
    )
    
    // DOM API: Object URL for blob data
    if (previewImage.value) {
      URL.revokeObjectURL(previewImage.value)
    }
    
    previewImage.value = URL.createObjectURL(response.data)
    previewTimestamp.value = new Date().toLocaleString()
    
  } catch (error) {
    previewError.value = error.message || 'Failed to load preview'
    console.error('Preview load error:', error)
  } finally {
    loading.value = false
  }
}

const retryPreview = () => {
  previewError.value = ''
  loadPreview()
}

const retrySystem = () => {
  systemError.value = ''
  // Trigger store refresh
  statusStore.refreshConnection()
}

const handleImageLoad = () => {
  console.log('Preview image loaded successfully')
}

const handleImageError = () => {
  previewError.value = 'Failed to display preview image'
}

// Watchers: Reactive side effects
watch(() => statusStore.isConnected, (newValue) => {
  if (!newValue) {
    previewImage.value = ''
    previewError.value = 'Connection lost'
  }
})

// Lifecycle: Component setup and cleanup
onMounted(() => {
  // Initialize stores
  statusStore.startConnectionMonitoring()
  printJobStore.loadUploadedFiles()
})

onBeforeUnmount(() => {
  // Cleanup: Prevent memory leaks
  if (previewImage.value) {
    URL.revokeObjectURL(previewImage.value)
  }
  statusStore.stopConnectionMonitoring()
})
</script>

<style scoped>
/* CSS: Component-scoped styling with design system */

/* CSS Variables: Design system tokens */
.layer-preview-system {
  --primary-color: #2563eb;
  --success-color: #16a34a;
  --error-color: #dc2626;
  --warning-color: #d97706;
  --neutral-color: #64748b;
  --background-color: #ffffff;
  --border-color: #e2e8f0;
  --text-color: #1e293b;
  --border-radius: 0.5rem;
  --spacing-sm: 0.5rem;
  --spacing-md: 1rem;
  --spacing-lg: 1.5rem;
  --font-size-sm: 0.875rem;
  --font-size-base: 1rem;
  --font-size-lg: 1.125rem;
}

/* Layout: Modern Flexbox and Grid */
.layer-preview-system {
  display: flex;
  flex-direction: column;
  gap: var(--spacing-lg);
  padding: var(--spacing-lg);
  background: var(--background-color);
  border-radius: var(--border-radius);
  border: 1px solid var(--border-color);
  min-height: 600px;
}

.preview-interface {
  display: grid;
  grid-template-rows: auto auto 1fr;
  gap: var(--spacing-lg);
  height: 100%;
}

/* Header Styling */
.preview-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding-bottom: var(--spacing-md);
  border-bottom: 1px solid var(--border-color);
}

.preview-header h1 {
  margin: 0;
  color: var(--text-color);
  font-size: var(--font-size-lg);
}

.connection-status {
  display: flex;
  align-items: center;
  padding: var(--spacing-sm) var(--spacing-md);
  border-radius: calc(var(--border-radius) / 2);
  font-size: var(--font-size-sm);
  font-weight: 500;
}

.connection-status.connected {
  background-color: #dcfce7;
  color: var(--success-color);
}

.connection-status.disconnected {
  background-color: #fef2f2;
  color: var(--error-color);
}

/* Form Styling */
.preview-controls {
  padding: var(--spacing-lg);
  background: #f8fafc;
  border-radius: var(--border-radius);
  border: 1px solid var(--border-color);
}

.preview-controls fieldset {
  border: none;
  padding: 0;
  margin: 0;
  display: flex;
  flex-direction: column;
  gap: var(--spacing-md);
}

.preview-controls fieldset:disabled {
  opacity: 0.6;
  pointer-events: none;
}

.preview-controls legend {
  font-weight: 600;
  margin-bottom: var(--spacing-md);
  color: var(--text-color);
}

.control-group {
  display: flex;
  flex-direction: column;
  gap: var(--spacing-sm);
}

.control-group label {
  font-weight: 500;
  color: var(--text-color);
  font-size: var(--font-size-sm);
}

.control-group select,
.control-group input {
  padding: var(--spacing-sm) var(--spacing-md);
  border: 1px solid var(--border-color);
  border-radius: calc(var(--border-radius) / 2);
  font-size: var(--font-size-base);
  background: white;
}

.control-group select:focus,
.control-group input:focus {
  outline: none;
  border-color: var(--primary-color);
  box-shadow: 0 0 0 3px rgba(37, 99, 235, 0.1);
}

.layer-info {
  font-size: var(--font-size-sm);
  color: var(--neutral-color);
}

/* Button Styling */
.load-button {
  display: flex;
  align-items: center;
  justify-content: center;
  gap: var(--spacing-sm);
  padding: var(--spacing-md) var(--spacing-lg);
  background: var(--primary-color);
  color: white;
  border: none;
  border-radius: var(--border-radius);
  font-size: var(--font-size-base);
  font-weight: 500;
  cursor: pointer;
  transition: all 0.2s ease;
}

.load-button:hover:not(:disabled) {
  background: #1d4ed8;
  transform: translateY(-1px);
}

.load-button:disabled {
  opacity: 0.6;
  cursor: not-allowed;
  transform: none;
}

/* Loading Animation */
.loading-spinner {
  width: 16px;
  height: 16px;
  border: 2px solid rgba(255, 255, 255, 0.3);
  border-top: 2px solid white;
  border-radius: 50%;
  animation: spin 1s linear infinite;
}

@keyframes spin {
  0% { transform: rotate(0deg); }
  100% { transform: rotate(360deg); }
}

/* Preview Display */
.preview-display {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  padding: var(--spacing-lg);
  border: 2px dashed var(--border-color);
  border-radius: var(--border-radius);
  min-height: 400px;
  position: relative;
}

.preview-loading {
  display: flex;
  flex-direction: column;
  align-items: center;
  gap: var(--spacing-md);
}

.loading-animation {
  width: 48px;
  height: 48px;
  border: 4px solid var(--border-color);
  border-top: 4px solid var(--primary-color);
  border-radius: 50%;
  animation: spin 1s linear infinite;
}

.preview-content {
  display: flex;
  flex-direction: column;
  align-items: center;
  gap: var(--spacing-md);
  width: 100%;
}

.preview-image {
  max-width: 100%;
  max-height: 400px;
  border-radius: var(--border-radius);
  box-shadow: 0 4px 6px -1px rgba(0, 0, 0, 0.1);
}

.preview-info {
  text-align: center;
  color: var(--neutral-color);
  font-size: var(--font-size-sm);
}

.preview-info p {
  margin: 0.25rem 0;
}

.preview-empty {
  color: var(--neutral-color);
  font-style: italic;
}

/* Error States */
.system-error,
.preview-error {
  display: flex;
  flex-direction: column;
  align-items: center;
  gap: var(--spacing-md);
  padding: var(--spacing-lg);
  background: #fef2f2;
  border: 1px solid #fecaca;
  border-radius: var(--border-radius);
  color: var(--error-color);
}

.system-error h3,
.preview-error h3 {
  margin: 0;
  color: var(--error-color);
}

.system-error button,
.preview-error button {
  padding: var(--spacing-sm) var(--spacing-md);
  background: var(--error-color);
  color: white;
  border: none;
  border-radius: calc(var(--border-radius) / 2);
  cursor: pointer;
}

/* Responsive Design */
@media (max-width: 768px) {
  .layer-preview-system {
    padding: var(--spacing-md);
  }
  
  .preview-header {
    flex-direction: column;
    gap: var(--spacing-sm);
    align-items: flex-start;
  }
  
  .preview-interface {
    grid-template-rows: auto auto auto 1fr;
  }
}

/* Accessibility */
.load-button:focus {
  outline: 3px solid rgba(37, 99, 235, 0.3);
  outline-offset: 2px;
}

@media (prefers-reduced-motion: reduce) {
  .loading-spinner,
  .loading-animation {
    animation: none;
  }
  
  .load-button {
    transition: none;
  }
}
</style>
```

This example shows **every technology working together**:

1. **HTML**: Semantic structure, proper forms, accessibility
2. **CSS**: Modern layout, design system, responsive design  
3. **JavaScript**: ES6+, async/await, error handling
4. **Vue.js**: Reactivity, computed properties, lifecycle hooks
5. **API Integration**: Fetch operations, blob handling
6. **State Management**: Pinia stores for global state
7. **Performance**: Object URL cleanup, efficient updates

## What's Next

Now you understand how all the technologies work together. The next chapters will show you performance optimization and professional development patterns.

**Chapter 14: Performance Optimization** will cover:
- Bundle size optimization
- Memory management  
- Network optimization
- Real-time performance monitoring

---

**See how to optimize** → [Chapter 14: Performance Optimization](14-performance-optimization.md)

*Integration is where the magic happens - this is where you see why Vue.js is so powerful for building industrial applications.*