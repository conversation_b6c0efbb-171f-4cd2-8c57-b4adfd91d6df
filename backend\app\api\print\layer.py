"""
Layer Management Endpoints
==========================

Provides endpoints for managing layer parameters and preview generation
for print job configuration and visualization.

Endpoints:
- GET /layer/parameters: Retrieve current layer printing parameters.
- PUT /layer/parameters: Update layer printing parameters.
- GET /layer/preview: Generate and retrieve layer preview as PNG image.
"""
import logging
from fastapi import APIRouter, HTTPException, Depends, Response, Query, Path

from app.dependencies import get_recoater_client, get_multilayer_job_manager
from infrastructure.recoater_client import (
    RecoaterClient,
    RecoaterConnectionError,
    RecoaterAPIError,
)
from app.services.job_management.multimaterial_job_service import MultiMaterialJobService
from .models import LayerParametersRequest, LayerParametersResponse

logger = logging.getLogger(__name__)
router = APIRouter()

# Direct API call to get layer parameters
@router.get("/layer/parameters", response_model=LayerParametersResponse)
async def get_layer_parameters(
    recoater_client: RecoaterClient = Depends(get_recoater_client),
) -> LayerParametersResponse:
    """Get the current parameters of the layer."""
    try:
        logger.info("Getting layer parameters")
        result = recoater_client.get_layer_parameters()
        return LayerParametersResponse(**result)
    except RecoaterConnectionError as e:
        logger.error(f"Connection error getting layer parameters: {e}")
        raise HTTPException(status_code=503, detail=f"Connection error: {e}")
    except RecoaterAPIError as e:
        logger.error(f"API error getting layer parameters: {e}")
        raise HTTPException(status_code=400, detail=f"API error: {e}")
    except Exception as e:
        logger.error(f"Unexpected error getting layer parameters: {e}")
        raise HTTPException(status_code=500, detail=f"Internal error: {e}")

# Direct API call to set layer parameters using JSON payload
@router.put("/layer/parameters")
async def set_layer_parameters(
    parameters: LayerParametersRequest,
    recoater_client: RecoaterClient = Depends(get_recoater_client),
):
    """Set the parameters of the current layer."""
    try:
        logger.info(f"Setting layer parameters: {parameters.model_dump()}")
        recoater_client.set_layer_parameters(
            filling_id=parameters.filling_id,
            speed=parameters.speed,
            powder_saving=parameters.powder_saving,
            x_offset=parameters.x_offset,
        )
        return {
            "success": True,
            "message": "Layer parameters set successfully",
            "parameters": parameters.model_dump(),
        }
    except RecoaterConnectionError as e:
        logger.error(f"Connection error setting layer parameters: {e}")
        raise HTTPException(status_code=503, detail=f"Connection error: {e}")
    except RecoaterAPIError as e:
        logger.error(f"API error setting layer parameters: {e}")
        raise HTTPException(status_code=400, detail=f"API error: {e}")
    except Exception as e:
        logger.error(f"Unexpected error setting layer parameters: {e}")
        raise HTTPException(status_code=500, detail=f"Internal error: {e}")

# Composite layer preview using cached CLI data from all drums
@router.get("/layer/preview")
async def get_layer_preview(
    layer: int = Query(1, ge=1, description="Layer number to preview (1-based, defaults to 1)"),
    job_manager: MultiMaterialJobService = Depends(get_multilayer_job_manager),
) -> Response:
    """Get composite layer preview as PNG image showing all drums overlaid."""
    try:
        logger.info(f"Getting composite layer preview for layer {layer}")
        
        # Get maximum layers across all drums
        max_layers = job_manager.get_max_layers()
        if max_layers == 0:
            raise HTTPException(
                status_code=404,
                detail="No CLI files cached for any drums. Please upload files first."
            )
        
        # Validate layer number
        if layer > max_layers:
            raise HTTPException(
                status_code=400,
                detail=f"Layer {layer} exceeds maximum available layers ({max_layers})"
            )
        
        # Collect layer data from each drum (only if they have enough layers)
        drum_layers = {}
        for drum_id in [0, 1, 2]:
            drum_data = job_manager.get_cached_file_for_drum(drum_id)
            if drum_data is not None and layer <= drum_data['layer_count']:
                # Get the specific layer (convert 1-based to 0-based index)
                target_layer = drum_data['parsed_file'].layers[layer - 1]
                drum_layers[drum_id] = target_layer
            else:
                # Drum doesn't have this layer or no file cached
                drum_layers[drum_id] = None
        
        # Generate composite preview using the CLI renderer
        image_data = job_manager.cli_parser.render_composite_layer_to_png(drum_layers)
        
        logger.info(f"Successfully generated composite preview for layer {layer}")
        return Response(
            content=image_data,
            media_type="image/png",
            headers={"Content-Disposition": f"inline; filename=composite_layer_{layer}_preview.png"},
        )
    except HTTPException:
        # Re-raise HTTP exceptions as-is (they have proper status codes and messages)
        raise
    except Exception as e:
        logger.error(f"Unexpected error generating composite preview for layer {layer}: {e}")
        raise HTTPException(status_code=500, detail=f"Internal error generating preview: {e}")

