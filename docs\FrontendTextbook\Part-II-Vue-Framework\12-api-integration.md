# Chapter 12: API Integration and HTTP Communication

## Learning Objectives
- Master HTTP communication in Vue.js applications
- Implement robust API client patterns
- Handle asynchronous operations with proper error handling
- Integrate APIs with Pinia stores
- Manage loading states and user feedback

## Understanding API Communication

### Frontend-Backend Communication Flow

```
Vue.js App → HTTP Request → Backend API → Database
    ↑                                         ↓
UI Update ← HTTP Response ← JSON Data ← Query Result
```

### API Communication Patterns

```
Synchronous (Blocking):
User Action → API Call → Wait → Response → UI Update
     ↓           ↓        ↓        ↓         ↓
   Click      Request   Freeze   Data    Refresh

Asynchronous (Non-blocking):
User Action → API Call → UI Loading → Response → UI Update
     ↓           ↓          ↓           ↓         ↓
   Click      Request   Show Spinner   Data    Hide Spinner
```

## Setting Up HTTP Client

### Basic Axios Configuration

```javascript
// services/api.js
import axios from 'axios'

// Create axios instance with default configuration
const apiClient = axios.create({
  baseURL: process.env.VUE_APP_API_BASE_URL || 'http://localhost:8000',
  timeout: 10000,
  headers: {
    'Content-Type': 'application/json'
  }
})

// Request interceptor
apiClient.interceptors.request.use(
  (config) => {
    // Add authentication token if available
    const token = localStorage.getItem('auth_token')
    if (token) {
      config.headers.Authorization = `Bearer ${token}`
    }
    
    console.log('Making request:', config.method?.toUpperCase(), config.url)
    return config
  },
  (error) => {
    console.error('Request error:', error)
    return Promise.reject(error)
  }
)

// Response interceptor
apiClient.interceptors.response.use(
  (response) => {
    console.log('Response received:', response.status, response.config.url)
    return response
  },
  (error) => {
    console.error('Response error:', error.response?.status, error.response?.data)
    
    // Handle common error scenarios
    if (error.response?.status === 401) {
      // Unauthorized - redirect to login
      localStorage.removeItem('auth_token')
      window.location.href = '/login'
    }
    
    return Promise.reject(error)
  }
)

export default apiClient
```

## API Service Layer

### Drum API Service

```javascript
// services/drumService.js
import apiClient from './api'

export const drumService = {
  // Get all drums
  async getDrums() {
    const response = await apiClient.get('/api/drums')
    return response.data
  },
  
  // Get specific drum
  async getDrum(drumId) {
    const response = await apiClient.get(`/api/drums/${drumId}`)
    return response.data
  },
  
  // Update drum configuration
  async updateDrumConfig(drumId, config) {
    const response = await apiClient.put(`/api/drums/${drumId}/config`, config)
    return response.data
  },
  
  // Start drum operation
  async startDrum(drumId) {
    const response = await apiClient.post(`/api/drums/${drumId}/start`)
    return response.data
  },
  
  // Stop drum operation
  async stopDrum(drumId) {
    const response = await apiClient.post(`/api/drums/${drumId}/stop`)
    return response.data
  },
  
  // Get drum status
  async getDrumStatus(drumId) {
    const response = await apiClient.get(`/api/drums/${drumId}/status`)
    return response.data
  },
  
  // Get layer preview
  async getLayerPreview(drumId, layerNumber) {
    const response = await apiClient.get(
      `/api/drums/${drumId}/layers/${layerNumber}/preview`,
      { responseType: 'blob' }
    )
    return response.data
  },
  
  // Upload CLI file
  async uploadCliFile(drumId, file, onProgress) {
    const formData = new FormData()
    formData.append('file', file)
    
    const response = await apiClient.post(
      `/api/drums/${drumId}/cli`,
      formData,
      {
        headers: {
          'Content-Type': 'multipart/form-data'
        },
        onUploadProgress: (progressEvent) => {
          if (onProgress) {
            const percentCompleted = Math.round(
              (progressEvent.loaded * 100) / progressEvent.total
            )
            onProgress(percentCompleted)
          }
        }
      }
    )
    return response.data
  }
}
```

### Error Handling Utilities

```javascript
// services/errorHandler.js
export class ApiError extends Error {
  constructor(message, status, data) {
    super(message)
    this.name = 'ApiError'
    this.status = status
    this.data = data
  }
}

export const handleApiError = (error) => {
  if (error.response) {
    // Server responded with error status
    const { status, data } = error.response
    const message = data?.detail || data?.message || `HTTP ${status} Error`
    
    return new ApiError(message, status, data)
  } else if (error.request) {
    // Network error
    return new ApiError('Network error - please check your connection', 0, null)
  } else {
    // Other error
    return new ApiError(error.message || 'Unknown error occurred', -1, null)
  }
}

export const withErrorHandling = (apiCall) => {
  return async (...args) => {
    try {
      return await apiCall(...args)
    } catch (error) {
      throw handleApiError(error)
    }
  }
}
```

## Integrating APIs with Stores

### Enhanced Drum Store with API Integration

```javascript
// stores/drumStore.js
import { defineStore } from 'pinia'
import { ref, computed } from 'vue'
import { drumService } from '@/services/drumService'
import { handleApiError } from '@/services/errorHandler'

export const useDrumStore = defineStore('drum', () => {
  // State
  const drums = ref([])
  const selectedDrumId = ref(null)
  const loadingStates = ref({
    drums: false,
    drumConfig: false,
    drumOperation: false
  })
  const errors = ref({
    drums: null,
    drumConfig: null,
    drumOperation: null
  })
  
  // Getters
  const selectedDrum = computed(() => {
    return drums.value.find(drum => drum.id === selectedDrumId.value)
  })
  
  const availableDrums = computed(() => {
    return drums.value.filter(drum => drum.connectionStatus === 'connected')
  })
  
  const isAnyLoading = computed(() => {
    return Object.values(loadingStates.value).some(loading => loading)
  })
  
  // Actions
  const loadDrums = async () => {
    loadingStates.value.drums = true
    errors.value.drums = null
    
    try {
      const drumsData = await drumService.getDrums()
      drums.value = drumsData
    } catch (error) {
      const apiError = handleApiError(error)
      errors.value.drums = apiError.message
      console.error('Failed to load drums:', apiError)
    } finally {
      loadingStates.value.drums = false
    }
  }
  
  const selectDrum = (drumId) => {
    selectedDrumId.value = drumId
    errors.value.drumConfig = null
  }
  
  const updateDrumConfig = async (drumId, config) => {
    loadingStates.value.drumConfig = true
    errors.value.drumConfig = null
    
    try {
      const updatedConfig = await drumService.updateDrumConfig(drumId, config)
      
      // Update local state
      const drum = drums.value.find(d => d.id === drumId)
      if (drum) {
        drum.config = updatedConfig
      }
      
      return updatedConfig
    } catch (error) {
      const apiError = handleApiError(error)
      errors.value.drumConfig = apiError.message
      throw apiError
    } finally {
      loadingStates.value.drumConfig = false
    }
  }
  
  const startDrum = async (drumId) => {
    loadingStates.value.drumOperation = true
    errors.value.drumOperation = null
    
    try {
      await drumService.startDrum(drumId)
      
      // Update local state
      const drum = drums.value.find(d => d.id === drumId)
      if (drum) {
        drum.status = 'starting'
      }
      
      // Start polling for status updates
      startStatusPolling(drumId)
    } catch (error) {
      const apiError = handleApiError(error)
      errors.value.drumOperation = apiError.message
      throw apiError
    } finally {
      loadingStates.value.drumOperation = false
    }
  }
  
  const stopDrum = async (drumId) => {
    loadingStates.value.drumOperation = true
    errors.value.drumOperation = null
    
    try {
      await drumService.stopDrum(drumId)
      
      // Update local state
      const drum = drums.value.find(d => d.id === drumId)
      if (drum) {
        drum.status = 'stopping'
      }
      
      stopStatusPolling(drumId)
    } catch (error) {
      const apiError = handleApiError(error)
      errors.value.drumOperation = apiError.message
      throw apiError
    } finally {
      loadingStates.value.drumOperation = false
    }
  }
  
  // Status polling
  const pollingIntervals = ref(new Map())
  
  const startStatusPolling = (drumId) => {
    // Don't start if already polling
    if (pollingIntervals.value.has(drumId)) return
    
    const intervalId = setInterval(async () => {
      try {
        const status = await drumService.getDrumStatus(drumId)
        
        // Update drum status
        const drum = drums.value.find(d => d.id === drumId)
        if (drum) {
          drum.status = status.status
          drum.temperature = status.temperature
          drum.lastUpdate = new Date().toISOString()
        }
        
        // Stop polling if drum is idle
        if (status.status === 'idle') {
          stopStatusPolling(drumId)
        }
      } catch (error) {
        console.error('Status polling error:', error)
        stopStatusPolling(drumId)
      }
    }, 2000) // Poll every 2 seconds
    
    pollingIntervals.value.set(drumId, intervalId)
  }
  
  const stopStatusPolling = (drumId) => {
    const intervalId = pollingIntervals.value.get(drumId)
    if (intervalId) {
      clearInterval(intervalId)
      pollingIntervals.value.delete(drumId)
    }
  }
  
  const stopAllPolling = () => {
    pollingIntervals.value.forEach((intervalId) => {
      clearInterval(intervalId)
    })
    pollingIntervals.value.clear()
  }
  
  return {
    // State
    drums,
    selectedDrumId,
    loadingStates,
    errors,
    // Getters
    selectedDrum,
    availableDrums,
    isAnyLoading,
    // Actions
    loadDrums,
    selectDrum,
    updateDrumConfig,
    startDrum,
    stopDrum,
    startStatusPolling,
    stopStatusPolling,
    stopAllPolling
  }
})
```

## File Upload with Progress

### File Upload Composable

```javascript
// composables/useFileUpload.js
import { ref } from 'vue'
import { drumService } from '@/services/drumService'

export function useFileUpload() {
  const isUploading = ref(false)
  const uploadProgress = ref(0)
  const uploadError = ref(null)
  
  const uploadFile = async (drumId, file) => {
    isUploading.value = true
    uploadProgress.value = 0
    uploadError.value = null
    
    try {
      const result = await drumService.uploadCliFile(
        drumId,
        file,
        (progress) => {
          uploadProgress.value = progress
        }
      )
      
      return result
    } catch (error) {
      uploadError.value = error.message
      throw error
    } finally {
      isUploading.value = false
      uploadProgress.value = 0
    }
  }
  
  const resetUpload = () => {
    isUploading.value = false
    uploadProgress.value = 0
    uploadError.value = null
  }
  
  return {
    isUploading,
    uploadProgress,
    uploadError,
    uploadFile,
    resetUpload
  }
}
```

### File Upload Component

```vue
<template>
  <div class="file-upload">
    <div class="upload-area" @drop.prevent="handleDrop" @dragover.prevent>
      <input
        ref="fileInput"
        type="file"
        accept=".cli"
        @change="handleFileSelect"
        style="display: none"
      >
      
      <div v-if="!isUploading && !uploadError" class="upload-prompt">
        <p>Drag CLI file here or <button @click="$refs.fileInput.click()">browse</button></p>
      </div>
      
      <div v-if="isUploading" class="upload-progress">
        <div class="progress-bar">
          <div class="progress-fill" :style="{ width: uploadProgress + '%' }"></div>
        </div>
        <p>Uploading... {{ uploadProgress }}%</p>
      </div>
      
      <div v-if="uploadError" class="upload-error">
        <p>Upload failed: {{ uploadError }}</p>
        <button @click="resetUpload">Try Again</button>
      </div>
    </div>
  </div>
</template>

<script>
import { useFileUpload } from '@/composables/useFileUpload'
import { useDrumStore } from '@/stores/drumStore'

export default {
  setup() {
    const drumStore = useDrumStore()
    const { isUploading, uploadProgress, uploadError, uploadFile, resetUpload } = useFileUpload()
    
    const handleFileSelect = (event) => {
      const file = event.target.files[0]
      if (file) {
        uploadFileToSelectedDrum(file)
      }
    }
    
    const handleDrop = (event) => {
      const files = event.dataTransfer.files
      if (files.length > 0) {
        uploadFileToSelectedDrum(files[0])
      }
    }
    
    const uploadFileToSelectedDrum = async (file) => {
      if (!drumStore.selectedDrumId) {
        alert('Please select a drum first')
        return
      }
      
      try {
        await uploadFile(drumStore.selectedDrumId, file)
        // Refresh drum data after successful upload
        await drumStore.loadDrums()
      } catch (error) {
        console.error('Upload failed:', error)
      }
    }
    
    return {
      isUploading,
      uploadProgress,
      uploadError,
      resetUpload,
      handleFileSelect,
      handleDrop
    }
  }
}
</script>
```

## WebSocket Integration

### WebSocket Service

```javascript
// services/websocketService.js
import { ref, reactive } from 'vue'

class WebSocketService {
  constructor() {
    this.socket = null
    this.isConnected = ref(false)
    this.connectionError = ref(null)
    this.listeners = reactive(new Map())
    this.reconnectAttempts = 0
    this.maxReconnectAttempts = 5
  }
  
  connect(url) {
    try {
      this.socket = new WebSocket(url)
      
      this.socket.onopen = () => {
        console.log('WebSocket connected')
        this.isConnected.value = true
        this.connectionError.value = null
        this.reconnectAttempts = 0
      }
      
      this.socket.onmessage = (event) => {
        try {
          const data = JSON.parse(event.data)
          this.handleMessage(data)
        } catch (error) {
          console.error('Failed to parse WebSocket message:', error)
        }
      }
      
      this.socket.onclose = () => {
        console.log('WebSocket disconnected')
        this.isConnected.value = false
        this.attemptReconnect()
      }
      
      this.socket.onerror = (error) => {
        console.error('WebSocket error:', error)
        this.connectionError.value = 'WebSocket connection failed'
      }
    } catch (error) {
      console.error('Failed to create WebSocket connection:', error)
      this.connectionError.value = error.message
    }
  }
  
  disconnect() {
    if (this.socket) {
      this.socket.close()
      this.socket = null
    }
  }
  
  send(message) {
    if (this.socket && this.socket.readyState === WebSocket.OPEN) {
      this.socket.send(JSON.stringify(message))
    } else {
      console.warn('WebSocket not connected, cannot send message')
    }
  }
  
  subscribe(eventType, callback) {
    if (!this.listeners.has(eventType)) {
      this.listeners.set(eventType, new Set())
    }
    this.listeners.get(eventType).add(callback)
    
    // Return unsubscribe function
    return () => {
      this.listeners.get(eventType)?.delete(callback)
    }
  }
  
  handleMessage(data) {
    const { type, payload } = data
    const callbacks = this.listeners.get(type)
    
    if (callbacks) {
      callbacks.forEach(callback => {
        try {
          callback(payload)
        } catch (error) {
          console.error('Error in WebSocket callback:', error)
        }
      })
    }
  }
  
  attemptReconnect() {
    if (this.reconnectAttempts < this.maxReconnectAttempts) {
      this.reconnectAttempts++
      console.log(`Attempting to reconnect... (${this.reconnectAttempts}/${this.maxReconnectAttempts})`)
      
      setTimeout(() => {
        this.connect(this.lastUrl)
      }, 1000 * this.reconnectAttempts) // Exponential backoff
    }
  }
}

export const websocketService = new WebSocketService()
```

### Using WebSocket in Store

```javascript
// stores/realtimeStore.js
export const useRealtimeStore = defineStore('realtime', () => {
  const systemStatus = ref({})
  const drumUpdates = ref({})
  
  const initializeWebSocket = () => {
    websocketService.connect('ws://localhost:8000/ws')
    
    // Subscribe to system status updates
    websocketService.subscribe('system_status', (data) => {
      systemStatus.value = data
    })
    
    // Subscribe to drum updates
    websocketService.subscribe('drum_update', (data) => {
      drumUpdates.value[data.drumId] = data
    })
  }
  
  const cleanup = () => {
    websocketService.disconnect()
  }
  
  return {
    systemStatus,
    drumUpdates,
    initializeWebSocket,
    cleanup
  }
})
```

## Best Practices

### 1. **Error Handling Strategy**
```javascript
// Consistent error handling
const handleOperation = async (operation, errorContext) => {
  try {
    loading.value = true
    error.value = null
    
    const result = await operation()
    return result
  } catch (err) {
    const apiError = handleApiError(err)
    error.value = `${errorContext}: ${apiError.message}`
    throw apiError
  } finally {
    loading.value = false
  }
}
```

### 2. **Loading State Management**
```javascript
// Granular loading states
const loadingStates = ref({
  drums: false,
  layers: false,
  preview: false
})

// Helper function
const withLoading = (key, asyncFn) => {
  return async (...args) => {
    loadingStates.value[key] = true
    try {
      return await asyncFn(...args)
    } finally {
      loadingStates.value[key] = false
    }
  }
}
```

### 3. **API Response Caching**
```javascript
// Simple cache implementation
const cache = new Map()
const CACHE_DURATION = 5 * 60 * 1000 // 5 minutes

const getCachedData = async (key, fetchFn) => {
  const cached = cache.get(key)
  
  if (cached && Date.now() - cached.timestamp < CACHE_DURATION) {
    return cached.data
  }
  
  const data = await fetchFn()
  cache.set(key, { data, timestamp: Date.now() })
  return data
}
```

## Key Takeaways

1. **Axios provides powerful HTTP client capabilities** with interceptors and configuration
2. **Service layer pattern** separates API logic from components
3. **Proper error handling** improves user experience and debugging
4. **Loading states and progress indicators** keep users informed
5. **WebSocket integration** enables real-time updates
6. **Caching strategies** can improve performance and reduce server load

## Next Steps

You've now completed the Vue.js Framework section! Part III will cover **Advanced Integration** topics including architecture patterns, performance optimization, and putting everything together in real-world applications.