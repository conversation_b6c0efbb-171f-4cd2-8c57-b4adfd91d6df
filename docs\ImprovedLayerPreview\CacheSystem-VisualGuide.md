# Cache System Visual Guide
## Understanding the APIRecoater CLI File Caching Architecture

```
╔══════════════════════════════════════════════════════════════════════════════════════╗
║                                CACHE SYSTEM OVERVIEW                                 ║
╚══════════════════════════════════════════════════════════════════════════════════════╝

                          ┌─────────────────────────────────────┐
                          │          Frontend Actions           │
                          └─────────────────────────────────────┘
                                           │
                              ┌────────────┴─────────────┐
                              │                          │
                              ▼                          ▼
                    ┌──────────────────┐      ┌─────────────────────┐
                    │  Preview Upload  │      │  Direct Drum Upload │
                    │ POST /cli/upload │      │POST /cli/upload/{id}│
                    └──────────────────┘      └─────────────────────┘
                              │                          │
                              ▼                          ▼
                    ┌──────────────────┐      ┌─────────────────────┐
                    │ Preview Cache ID │      │   Drum Cache Entry  │
                    │  (UUID string)   │      │  (drum_0, drum_1,   │
                    │                  │      │   drum_2)           │
                    └──────────────────┘      └─────────────────────┘

╔══════════════════════════════════════════════════════════════════════════════════════╗
║                                THREE CACHE LAYERS                                    ║
╚══════════════════════════════════════════════════════════════════════════════════════╝

┌─────────────────────────────────────────────────────────────────────────────────────┐
│                                 1. PREVIEW CACHE                                    │
├─────────────────────────────────────────────────────────────────────────────────────┤
│  Purpose: Preview, single layer, and layer range operations                         │
│  Location: MultiMaterialJobService.cli_cache                                        │
│  Type: Dict[file_id: str, CliCacheEntry]                                            │
│                                                                                     │
│  ┌───────────────────────────────────────────────────────────────────────────────┐  │
│  │ Preview Cache Structure:                                                      │  │
│  │                                                                               │  │
│  │ cli_cache = {                                                                 │  │
│  │   "a1b2c3d4ef56...": {                                                        │  │
│  │     'parsed_file': ParsedCliFile(                                             │  │
│  │       header_lines: ["$$UNITS/1", "$$HEADEREND"],                             │  │
│  │       layers: [Layer1, Layer2, ..., LayerN]                                   │  │
│  │     ),                                                                        │  │
│  │     'original_filename': "multi_layer_design.cli"                             │  │
│  │   },                                                                          │  │
│  │   "f7g8h9i0jk12...": { ... }                                                  │  │
│  │ }                                                                             │  │
│  └───────────────────────────────────────────────────────────────────────────────┘  │
│                                                                                     │
│  Operations:                                                                        │
│  • Preview individual layers: GET /cli/{file_id}/layer/{num}/preview                │
│  • Send single layer: POST /cli/{file_id}/layer/{num}/send/{drum_id}                │
│  • Send layer range: POST /cli/{file_id}/layers/send/{drum_id}                      │
└─────────────────────────────────────────────────────────────────────────────────────┘

┌─────────────────────────────────────────────────────────────────────────────────────┐
│                                  2. DRUM CACHE                                      │
├─────────────────────────────────────────────────────────────────────────────────────┤
│  Purpose: Production printing workflow - per-drum CLI storage                       │
│  Location: MultiMaterialJobService.drum_cli_cache                                   │
│  Type: Dict[drum_id: int, Optional[Dict[str, Any]]]                                 │
│                                                                                     │
│  ┌───────────────────────────────────────────────────────────────────────────────┐  │
│  │ Drum Cache Structure:                                                         │  │
│  │                                                                               │  │
│  │ drum_cli_cache = {                                                            │  │
│  │   0: {  # Drum 0                                                              │  │
│  │     'parsed_file': ParsedCliFile(...),                                        │  │
│  │     'filename': "support_structure.cli",                                      │  │
│  │     'layer_count': 150                                                        │  │
│  │   },                                                                          │  │
│  │   1: {  # Drum 1                                                              │  │
│  │     'parsed_file': ParsedCliFile(...),                                        │  │
│  │     'filename': "main_part.cli",                                              │  │
│  │     'layer_count': 200                                                        │  │
│  │   },                                                                          │  │
│  │   2: None  # Drum 2 (no file cached)                                          │  │
│  │ }                                                                             │  │
│  └───────────────────────────────────────────────────────────────────────────────┘  │
│                                                                                     │
│  Operations:                                                                        │
│  • Direct upload: POST /cli/upload/{drum_id}                                        │
│  • Multi-material jobs: Uses cached files for layer-by-layer printing               │
│  • Max layer calculation: max(drum0.layers, drum1.layers, drum2.layers)             │
└─────────────────────────────────────────────────────────────────────────────────────┘

┌─────────────────────────────────────────────────────────────────────────────────────┐
│                                  3. JOB CACHE                                       │
├─────────────────────────────────────────────────────────────────────────────────────┤
│  Purpose: Active job state management during printing                               │
│  Location: MultiMaterialJobService.current_job                                      │
│  Type: Optional[MultiMaterialJobState]                                              │
│                                                                                     │
│  ┌───────────────────────────────────────────────────────────────────────────────┐  │
│  │ Job Cache Structure:                                                          │  │
│  │                                                                               │  │
│  │ current_job = MultiMaterialJobState(                                          │  │
│  │   job_id: "job_20240918_150332",                                              │  │
│  │   file_ids: {                                                                 │  │
│  │     0: "drum_0_cache",                                                        │  │
│  │     1: "drum_1_cache",                                                        │  │
│  │     2: None                                                                   │  │
│  │   },                                                                          │  │
│  │   total_layers: 200,                                                          │  │
│  │   current_layer: 45,                                                          │  │
│  │   status: JobStatus.RUNNING,                                                  │  │
│  │   is_active: True,                                                            │  │
│  │   error_message: "",                                                          │  │
│  │   progress_percentage: 22.5                                                   │  │
│  │ )                                                                             │  │
│  └───────────────────────────────────────────────────────────────────────────────┘  │
│                                                                                     │
│  Operations:                                                                        │
│  • Job lifecycle: start → running → completed/cancelled/error                       │
│  • Progress tracking: current_layer / total_layers                                  │
│  • OPC UA coordination: Maps to OPC UA variables for PLC communication              │
└─────────────────────────────────────────────────────────────────────────────────────┘
```

## Cache Flow Diagrams

### Preview Workflow
```
User Uploads CLI
      │
      ▼
┌─────────────────┐    ┌──────────────────┐     ┌─────────────────────┐
│  Upload for     │───▶│  Generate UUID   │───▶│  Store in Preview   │
│  Preview        │    │  file_id         │     │  Cache              │
│  /cli/upload    │    │                  │     │  cli_cache[id]      │
└─────────────────┘    └──────────────────┘     └─────────────────────┘
                                                         │
                                 ┌───────────────────────┴───────────────────────┐
                                 │                                               │
                                 ▼                                               ▼
                    ┌──────────────────────┐                        ┌──────────────────────┐
                    │   Layer Preview      │                        │   Send to Drum       │
                    │   Operations         │                        │   Operations         │
                    │                      │                        │                      │
                    │ • View PNG           │                        │ • Single layer       │
                    │ • Inspect geometry   │                        │ • Layer range        │
                    │ • Validate before    │                        │ • Trimmed to drum    │
                    │   production         │                        │   cache              │
                    └──────────────────────┘                        └──────────────────────┘
```

### Production Workflow
```
User Uploads to Drum
      │
      ▼
┌─────────────────┐    ┌──────────────────┐    ┌─────────────────────┐
│  Direct Upload  │───▶│  Parse & Cache   │───▶│  Store in Drum      │
│  /cli/upload/   │    │  for Drum       │    │  Cache              │
│  {drum_id}      │    │                  │    │  drum_cli_cache[id] │
└─────────────────┘    └──────────────────┘    └─────────────────────┘
                                                         │
                        ┌────────────────────────────────┴─────────────────────────────────┐
                        │                                                                  │
                        ▼                                                                  ▼
           ┌──────────────────────┐                                         ┌──────────────────────┐
           │   Multi-Material     │                                         │   Layer Processing   │
           │   Job Start          │                                         │   Loop               │
           │                      │                                         │                      │
           │ • Validate all drums │                                         │ • Extract layer N    │
           │ • Calculate max      │────────────────────────────────────────▶│ • Upload to all      │
           │   layers             │                                         │   drums              │
           │ • Create job state   │                                         │ • Print & wait       │
           │                      │                                         │ • Repeat until done  │
           └──────────────────────┘                                         └──────────────────────┘
```

## Cache Management Operations

### Cache Cleanup Patterns
```
┌─────────────────────────────────────────────────────────────────────────────────────┐
│                               CLEANUP TRIGGERS                                     │
├─────────────────────────────────────────────────────────────────────────────────────┤
│                                                                                     │
│  Job Completion          Job Cancellation        Manual Clear         Error Reset  │
│        │                       │                      │                    │       │
│        ▼                       ▼                      ▼                    ▼       │
│  ┌──────────────┐       ┌──────────────┐       ┌──────────────┐   ┌──────────────┐ │
│  │ Clear Drum   │       │ Clear All    │       │ Clear Single │   │ Keep Caches  │ │
│  │ Caches       │       │ Caches       │       │ Drum Cache   │   │ Preserve     │ │
│  │              │       │              │       │              │   │ Job State    │ │
│  │ Keep Preview │       │ Keep Preview │       │ Keep Others  │   │              │ │
│  │ Cache        │       │ Cache        │       │              │   │              │ │
│  └──────────────┘       └──────────────┘       └──────────────┘   └──────────────┘ │
│                                                                                     │
└─────────────────────────────────────────────────────────────────────────────────────┘
```

### Memory Usage Visualization
```
Cache Type        Memory Usage      Lifetime           Cleanup Policy
─────────────────────────────────────────────────────────────────────────
Preview Cache     ████████████     Session-based      Manual/timeout
                  (Large files)    (Until restart)    

Drum Cache        █████████        Job-based          Auto on completion
                  (Per-drum)       (Until job ends)   

Job Cache         ██               Active job only    Auto on job end
                  (Metadata)       (Runtime state)    

OPC UA State      █                Persistent         External clear
                  (Variables)      (Until reset)      
─────────────────────────────────────────────────────────────────────────
```

## Integration Points

### Frontend ↔ Backend Cache Mapping
```
┌─────────────────────────────────────────────────────────────────────────────────────┐
│                            FRONTEND STATE MAPPING                                  │
├─────────────────────────────────────────────────────────────────────────────────────┤
│                                                                                     │
│  Frontend Store (printJobStore.js)              Backend Cache                      │
│  ┌─────────────────────────────────┐           ┌─────────────────────────────────┐  │
│  │ uploadedFiles: {                │           │ drum_cli_cache: {               │  │
│  │   0: {                          │  ◄────────┤   0: {                          │  │
│  │     fileId: "drum_0",           │           │     parsed_file: ...,           │  │
│  │     fileName: "part.cli",       │           │     filename: "part.cli",       │  │
│  │     layerCount: 150             │           │     layer_count: 150            │  │
│  │   },                            │           │   },                            │  │
│  │   1: null,                      │           │   1: null,                      │  │
│  │   2: null                       │           │   2: null                       │  │
│  │ }                               │           │ }                               │  │
│  └─────────────────────────────────┘           └─────────────────────────────────┘  │
│                                                                                     │
│  Frontend Preview State                         Backend Preview Cache              │
│  ┌─────────────────────────────────┐           ┌─────────────────────────────────┐  │
│  │ cliFileInfo: {                  │           │ cli_cache: {                    │  │
│  │   file_id: "a1b2c3d4...",       │  ◄────────┤   "a1b2c3d4...": {              │  │
│  │   total_layers: 200,            │           │     parsed_file: ...,           │  │
│  │   filename: "design.cli"        │           │     original_filename: "..."    │  │
│  │ }                               │           │   }                             │  │
│  │                                 │           │ }                               │  │
│  └─────────────────────────────────┘           └─────────────────────────────────┘  │
│                                                                                     │
└─────────────────────────────────────────────────────────────────────────────────────┘
```

## Common Cache Operations

### 1. Preview Upload and Layer Selection
```
┌─────────────────────────────────────────────────────────────────────────────────────┐
│ SCENARIO: User uploads 100-layer CLI file for preview and sends layers 20-30       │
│ to drum 1                                                                           │
├─────────────────────────────────────────────────────────────────────────────────────┤
│                                                                                     │
│ Step 1: Upload for Preview                                                          │
│ POST /cli/upload → file_id: "abc123..."                                            │
│                                                                                     │
│ cli_cache["abc123..."] = {                                                          │
│   parsed_file: ParsedCliFile(layers=[Layer1...Layer100]),                          │
│   original_filename: "complex_part.cli"                                            │
│ }                                                                                   │
│                                                                                     │
│ Step 2: Send Layer Range to Drum                                                   │
│ POST /cli/abc123.../layers/send/1 {start: 20, end: 30}                            │
│                                                                                     │
│ ┌─ Backend Processing ───────────────────────────────────────────────────────────┐ │
│ │ 1. Retrieve from preview cache: cli_cache["abc123..."]                        │ │
│ │ 2. Extract layers 20-30: selected_layers = layers[19:30]                      │ │
│ │ 3. Generate new CLI: trimmed_cli = generate_from_range(selected_layers)       │ │
│ │ 4. Upload to recoater: upload_drum_geometry(1, trimmed_cli)                   │ │
│ │ 5. Cache trimmed version for drum 1:                                          │ │
│ │    drum_cli_cache[1] = {                                                       │ │
│ │      parsed_file: ParsedCliFile(layers=[Layer20...Layer30]),                  │ │
│ │      filename: "layers_20-30_complex_part.cli",                               │ │
│ │      layer_count: 11                                                          │ │
│ │    }                                                                           │ │
│ └────────────────────────────────────────────────────────────────────────────────┘ │
│                                                                                     │
└─────────────────────────────────────────────────────────────────────────────────────┘
```

### 2. Multi-Material Job Execution
```
┌─────────────────────────────────────────────────────────────────────────────────────┐
│ SCENARIO: 3-drum job with different layer counts                                   │
├─────────────────────────────────────────────────────────────────────────────────────┤
│                                                                                     │
│ Setup:                                                                              │
│ drum_cli_cache = {                                                                  │
│   0: { filename: "support.cli", layer_count: 80 },                                 │
│   1: { filename: "main.cli", layer_count: 120 },                                   │
│   2: { filename: "detail.cli", layer_count: 100 }                                  │
│ }                                                                                   │
│                                                                                     │
│ Max Layers Calculation: max(80, 120, 100) = 120                                    │
│                                                                                     │
│ Layer Processing Loop:                                                              │
│ ┌─────────────────────────────────────────────────────────────────────────────────┐ │
│ │ For layer_index = 0 to 119:                                                    │ │
│ │                                                                                 │ │
│ │   Drum 0: layer_index < 80  ? use support.cli[layer_index]                     │ │
│ │                             : use empty_layer.cli                              │ │
│ │                                                                                 │ │
│ │   Drum 1: layer_index < 120 ? use main.cli[layer_index]                        │ │
│ │                             : use empty_layer.cli                              │ │
│ │                                                                                 │ │
│ │   Drum 2: layer_index < 100 ? use detail.cli[layer_index]                      │ │
│ │                             : use empty_layer.cli                              │ │
│ │                                                                                 │ │
│ │   Upload all 3 files to recoater, print layer, wait for completion            │ │
│ │                                                                                 │ │
│ └─────────────────────────────────────────────────────────────────────────────────┘ │
│                                                                                     │
└─────────────────────────────────────────────────────────────────────────────────────┘
```

## Cache Performance Considerations

### Memory Usage Patterns
```
Cache Growth Over Time:

Preview Cache (Cumulative)
Memory │ ████████████████████████████████████████████
Usage  │ ████████████████████████████████
       │ ████████████████████████
       │ ████████████████
       │ ████████
       │ ████
       └────────────────────────────────────────────── Time
         Upload Upload Upload Upload Upload Upload
         File1  File2  File3  File4  File5  File6

Drum Cache (Job-Scoped)
Memory │     ████████████████     ████████████████     
Usage  │     ████████████████     ████████████████     
       │     ████████████████     ████████████████     
       │ ████████████████████ ████████████████████████ 
       │ ████████████████████ ████████████████████████ 
       │ ████████████████████ ████████████████████████ 
       └────────────────────────────────────────────── Time
         Job1 Setup│  Job1   │Job2 Setup│  Job2   │
                   │Complete │          │Complete │
                   │Clear    │          │Clear    │
```

### Best Practices Summary

1. **Preview Cache**: Use for exploration, testing, and selective operations
2. **Drum Cache**: Use for production printing workflows
3. **Job Cache**: Maintains active job state and progress
4. **Cleanup**: Automatic on job completion, manual for preview cache
5. **Memory**: Monitor large CLI files in preview cache
6. **Performance**: Drum cache optimized for sequential layer access

This caching architecture provides flexibility for both development/testing workflows (preview cache) and production printing (drum cache) while maintaining clear separation of concerns and efficient memory usage.

## ⚠️ CRITICAL ARCHITECTURAL ISSUE IDENTIFIED ⚠️

### Layer Preview Card Disconnect

```
╔══════════════════════════════════════════════════════════════════════════════════════╗
║                              LAYER PREVIEW ISSUE                                    ║
╚══════════════════════════════════════════════════════════════════════════════════════╝

🔍 PROBLEM: The Layer Preview card is trying to preview geometry from drums BEFORE
             it's actually uploaded to the hardware!

Current Workflow (BROKEN):
┌─────────────────────────────────────────────────────────────────────────────────────┐
│                                                                                     │
│ 1. User uploads CLI → Backend Cache (MultiMaterialJobService.drum_cli_cache)       │
│      │                                                                              │
│      ▼                                                                              │
│ 2. User clicks "Drum X Geometry" preview                                           │
│      │                                                                              │
│      ▼                                                                              │
│ 3. Frontend calls: getDrumGeometryPreview(drumId)                                  │
│      │                                                                              │
│      ▼                                                                              │
│ 4. Backend calls: recoater_client.download_drum_geometry(drum_id)                  │
│      │                                                                              │
│      ▼                                                                              │
│ 5. ❌ HARDWARE HAS NO FILE! Files only uploaded during print job execution!        │
│                                                                                     │
└─────────────────────────────────────────────────────────────────────────────────────┘

API Flow Analysis:
┌─────────────────────────────────────────────────────────────────────────────────────┐
│ Layer Preview Options:                                                              │
│                                                                                     │
│ Option 1: "Current Layer Configuration"                                            │
│   → apiService.getLayerPreview()                                                   │
│   → GET /print/layer/preview                                                       │
│   → recoater_client.get_layer_preview()                                            │
│   ✅ WORKS: Gets layer configuration from hardware                                 │
│                                                                                     │
│ Option 2: "Drum X Geometry"                                                        │
│   → apiService.getDrumGeometryPreview(drumId)                                      │
│   → GET /print/drums/{drum_id}/geometry/preview                                    │
│   → recoater_client.download_drum_geometry(drum_id)                                │
│   ❌ FAILS: No geometry on hardware until print job starts!                       │
│                                                                                     │
└─────────────────────────────────────────────────────────────────────────────────────┘

Timing Issue:
┌─────────────────────────────────────────────────────────────────────────────────────┐
│                                                                                     │
│   Cache Upload          Preview Attempt          Actual Hardware Upload            │
│        │                       │                         │                        │
│        ▼                       ▼                         ▼                        │
│  ┌─────────────┐        ┌─────────────┐           ┌─────────────┐                 │
│  │ Files cached│        │ Try to load │           │ Files sent  │                 │
│  │ in backend  │        │ from drums  │           │ to hardware │                 │
│  │ memory only │        │   ❌ EMPTY   │           │ during job  │                 │
│  └─────────────┘        └─────────────┘           └─────────────┘                 │
│        │                       │                         │                        │
│        │←────── TIME GAP ──────│────── TIME GAP ────────│                        │
│                                                                                     │
└─────────────────────────────────────────────────────────────────────────────────────┘
```

### Root Cause Analysis

**The Issue:**
1. **Backend Cache ≠ Hardware Storage**: CLI files are cached in `MultiMaterialJobService.drum_cli_cache` but not uploaded to hardware
2. **Preview Expects Hardware Files**: `getDrumGeometryPreview()` tries to read from hardware drum storage
3. **Upload Happens During Job**: Files are only sent to hardware during `start_layer_by_layer_job()` execution
4. **Preview Returns Nothing**: Since hardware has no files, preview shows empty/error state

**Current Cache vs Hardware State:**
```
Backend Cache State:               Hardware Drum State:
┌─────────────────────┐           ┌─────────────────────┐
│ drum_cli_cache = {  │           │ Drum 0: EMPTY       │
│   0: {              │           │ Drum 1: EMPTY       │
│     filename: '...' │    ≠      │ Drum 2: EMPTY       │
│     parsed_file:... │           │                     │
│     layer_count:150 │           │ (Files uploaded     │
│   },                │           │  only during job    │
│   1: {...},         │           │  execution)         │
│   2: null           │           │                     │
│ }                   │           │                     │
└─────────────────────┘           └─────────────────────┘
```

### Proposed Solutions

#### Solution A: Preview from Cache (Recommended)
```
┌─────────────────────────────────────────────────────────────────────────────────────┐
│ SOLUTION A: Generate Preview from Backend Cache                                     │
├─────────────────────────────────────────────────────────────────────────────────────┤
│                                                                                     │
│ Modified Flow:                                                                      │
│ 1. User selects "Drum X Geometry"                                                  │
│ 2. Frontend calls: getDrumCachePreview(drumId) // NEW ENDPOINT                     │
│ 3. Backend checks: drum_cli_cache[drumId]                                          │
│ 4. If cached: Generate PNG from first layer of cached CLI                          │
│ 5. If empty: Return "No file cached" message                                       │
│                                                                                     │
│ Benefits:                                                                           │
│ • Shows actual cached content                                                       │
│ • Works immediately after upload                                                    │
│ • No hardware dependency                                                            │
│ • Consistent with cache-based workflow                                             │
│                                                                                     │
└─────────────────────────────────────────────────────────────────────────────────────┘
```

#### Solution B: Upload to Hardware Immediately
```
┌─────────────────────────────────────────────────────────────────────────────────────┐
│ SOLUTION B: Immediate Hardware Upload (Alternative)                                 │
├─────────────────────────────────────────────────────────────────────────────────────┤
│                                                                                     │
│ Modified Flow:                                                                      │
│ 1. User uploads CLI to drum cache                                                   │
│ 2. Backend ALSO uploads to hardware immediately                                     │
│ 3. getDrumGeometryPreview() now works as expected                                   │
│                                                                                     │
│ Drawbacks:                                                                          │
│ • Changes current architecture                                                       │
│ • Multiple hardware uploads (cache + job)                                          │
│ • Potential hardware state inconsistencies                                         │
│                                                                                     │
└─────────────────────────────────────────────────────────────────────────────────────┘
```

### Implementation Details for Solution A

**New Backend Endpoint:**
```python
# backend/app/api/print/drum.py
@router.get("/drums/{drum_id}/cache/preview")
async def get_drum_cache_preview(
    drum_id: int = Path(..., ge=0, le=2),
    job_manager: MultiMaterialJobService = Depends(get_multilayer_job_manager),
) -> Response:
    """Get geometry preview from drum cache (not hardware)."""
    cached_data = job_manager.get_cached_file_for_drum(drum_id)
    
    if not cached_data:
        # Return placeholder image with "No file cached" text
        return generate_placeholder_preview(f"No file cached for Drum {drum_id}")
    
    # Generate preview from first layer of cached CLI
    first_layer = cached_data['parsed_file'].layers[0]
    png_bytes = Editor().render_layer_to_png(first_layer, drum_id=drum_id)
    
    return Response(
        content=png_bytes,
        media_type="image/png",
        headers={"Content-Disposition": f"inline; filename=drum_{drum_id}_cache_preview.png"}
    )
```

**Updated Frontend API:**
```javascript
// frontend/src/services/api.js
getDrumCachePreview(drumId) {
  return apiClient.get(`/print/drums/${drumId}/cache/preview`, {
    responseType: 'blob'
  })
}
```

**Updated Preview Logic:**
```javascript
// frontend/src/views/PrintView.vue
if (previewSource.value.startsWith('drum-')) {
  const drumId = parseInt(previewSource.value.split('-')[1])
  // Use cache preview instead of hardware preview
  response = await apiService.getDrumCachePreview(drumId)
  successMessage = `Drum ${drumId} cached geometry preview loaded`
}
```

### Testing the Issue

To verify this issue:
1. Upload CLI files to drum caches via File Management
2. Try to preview "Drum X Geometry"
3. Observe empty/error result (because hardware has no files)
4. Start a print job → files get uploaded to hardware
5. Now "Drum X Geometry" preview would work (but job is running)

This explains why the Layer Preview card feels disconnected from the cache-based workflow!