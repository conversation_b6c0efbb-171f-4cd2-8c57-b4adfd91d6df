# Chapter 14: Performance Optimization
## Building High-Performance Vue.js Industrial Applications

```
╔══════════════════════════════════════════════════════════════════════════════════════╗
║                        CHAPTER 14: PERFORMANCE OPTIMIZATION                          ║
║                  Building Fast, Efficient Vue.js Applications                        ║
╚══════════════════════════════════════════════════════════════════════════════════════╝
```

## Chapter Objectives

By the end of this chapter, you will understand:
- Vue.js performance fundamentals and optimization techniques
- Bundle size optimization and code splitting strategies
- Reactivity optimization for large-scale applications
- Memory management and leak prevention
- Performance monitoring and profiling tools
- Industrial application performance considerations

## Understanding Performance in Vue.js

### Performance Metrics That Matter

```
┌─────────────────────────────────────────────────────────────────────────────────────┐
│                              PERFORMANCE METRICS                                    │
├─────────────────────────────────────────────────────────────────────────────────────┤
│                                                                                     │
│ User Experience Metrics          Technical Metrics          Business Metrics        │
│      │                                │                          │                 │
│      ▼                                ▼                          ▼                 │
│ ┌─────────────┐               ┌─────────────┐            ┌─────────────┐          │
│ │ First Paint │               │ Bundle Size │            │ User        │          │
│ │ (FP)        │               │             │            │ Retention   │          │
│ │             │               │ • JS Size   │            │             │          │
│ │ • 1.5s     │               │ • CSS Size  │            │ • Conversion │          │
│ │ target      │               │ • Images    │            │ • Engagement │          │
│ └─────────────┘               └─────────────┘            └─────────────┘          │
│      │                                │                          │                 │
│      ▼                                ▼                          ▼                 │
│ ┌─────────────┐               ┌─────────────┐            ┌─────────────┐          │
│ │ Time to    │               │ Load Time   │            │ Revenue     │          │
│ │ Interactive│               │             │            │ Impact      │          │
│ │ (TTI)      │               │ • Network   │            │             │          │
│ │             │               │ • Parse     │            │ • Cost      │          │
│ │ • 3.5s     │               │ • Execute   │            │ • Savings   │          │
│ │ target      │               └─────────────┘            └─────────────┘          │
│ └─────────────┘                                                             │
│      │                                                                        │
│      ▼                                                                        │
│ ┌─────────────┐                                                              │
│ │ Largest     │                                                              │
│ │ Contentful  │                                                              │
│ │ Paint (LCP) │                                                              │
│ │             │                                                              │
│ │ • 2.5s      │                                                              │
│ │ target      │                                                              │
│ └─────────────┘                                                              │
│                                                                                     │
└─────────────────────────────────────────────────────────────────────────────────────┘
```

### Vue.js Performance Principles

1. **Reactivity is Expensive**: Every reactive property creates watchers
2. **DOM Updates are Costly**: Minimize unnecessary re-renders
3. **Bundle Size Matters**: Every KB affects load time
4. **Memory Leaks Kill Apps**: Clean up resources properly
5. **Network is the Bottleneck**: Optimize data fetching

## Bundle Size Optimization

### Code Splitting Strategies

```javascript
// 1. Route-based code splitting (automatic with Vue Router)
const routes = [
  {
    path: '/dashboard',
    component: () => import('./views/Dashboard.vue'),
    children: [
      {
        path: 'analytics',
        component: () => import('./views/Analytics.vue')
      },
      {
        path: 'reports',
        component: () => import('./views/Reports.vue')
      }
    ]
  }
]

// 2. Component-based code splitting
const AsyncDrumControl = defineAsyncComponent({
  loader: () => import('./components/DrumControl.vue'),
  loadingComponent: LoadingSpinner,
  errorComponent: ErrorComponent,
  delay: 200,
  timeout: 3000
})

// 3. Library-based code splitting
const AsyncChart = defineAsyncComponent({
  loader: () => import('./components/HeavyChart.vue'),
  loadingComponent: LoadingSpinner
})

// Usage in template
<template>
  <div>
    <DrumControl />
    <AsyncDrumControl v-if="showAdvanced" />
    <AsyncChart v-if="showAnalytics" />
  </div>
</template>
```

### Bundle Analysis and Optimization

```javascript
// vite.config.js - Bundle analysis
import { defineConfig } from 'vite'
import { visualizer } from 'rollup-plugin-visualizer'

export default defineConfig({
  plugins: [
    vue(),
    visualizer({
      filename: 'dist/bundle-analysis.html',
      open: true,
      gzipSize: true,
      brotliSize: true
    })
  ],
  build: {
    rollupOptions: {
      output: {
        manualChunks: {
          // Vendor chunks
          'vue-vendor': ['vue', 'vue-router', 'pinia'],
          'ui-vendor': ['element-plus', '@headlessui/vue'],
          'chart-vendor': ['chart.js', 'd3'],
          // Feature chunks
          'drum-feature': ['./src/views/DrumView.vue', './src/components/DrumControl.vue'],
          'layer-feature': ['./src/views/LayerView.vue', './src/components/LayerPreview.vue']
        }
      }
    },
    chunkSizeWarningLimit: 1000
  }
})
```

### Tree Shaking and Dead Code Elimination

```javascript
// 1. Named exports enable tree shaking
export const useDrumStore = defineStore('drum', () => { /* ... */ })
export const useLayerStore = defineStore('layer', () => { /* ... */ })
export const formatTemperature = (temp) => { /* ... */ }

// 2. Avoid default exports for better tree shaking
// ❌ Bad - entire module imported
import utils from './utils'

// ✅ Good - only used functions imported
import { formatTemperature, validateLayer } from './utils'

// 3. Dynamic imports for conditional loading
const loadHeavyLibrary = async () => {
  if (condition) {
    const { heavyFunction } = await import('./heavyLibrary')
    return heavyFunction()
  }
}
```

## Reactivity Optimization

### Computed Properties vs Methods

```javascript
// ❌ Bad - method called on every render
<template>
  <div>
    <p>Total layers: {{ getTotalLayers() }}</p>
    <p>Active layers: {{ getActiveLayers() }}</p>
    <p>Completed layers: {{ getCompletedLayers() }}</p>
  </div>
</template>

<script>
export default {
  methods: {
    getTotalLayers() {
      console.log('Computing total layers...') // Called every render!
      return this.layers.length
    },
    getActiveLayers() {
      console.log('Computing active layers...') // Called every render!
      return this.layers.filter(l => l.status === 'active').length
    }
  }
}
</script>

// ✅ Good - computed properties cached
<template>
  <div>
    <p>Total layers: {{ totalLayers }}</p>
    <p>Active layers: {{ activeLayers }}</p>
    <p>Completed layers: {{ completedLayers }}</p>
  </div>
</template>

<script>
export default {
  computed: {
    totalLayers() {
      console.log('Computing total layers...') // Called only when layers change
      return this.layers.length
    },
    activeLayers() {
      console.log('Computing active layers...') // Called only when layers change
      return this.layers.filter(l => l.status === 'active').length
    },
    completedLayers() {
      console.log('Computing completed layers...') // Called only when layers change
      return this.layers.filter(l => l.status === 'completed').length
    }
  }
}
</script>
```

### Watcher Optimization

```javascript
// 1. Use immediate: false for non-immediate watchers
watch(selectedDrum, (newDrum) => {
  // Only runs when selectedDrum actually changes
  loadDrumData(newDrum)
}, { immediate: false })

// 2. Debounce rapid changes
import { debounce } from 'lodash-es'

const debouncedSearch = debounce((query) => {
  searchLayers(query)
}, 300)

watch(searchQuery, (newQuery) => {
  debouncedSearch(newQuery)
})

// 3. Use watchEffect for automatic dependency tracking
watchEffect(() => {
  // Automatically tracks dependencies
  const drum = selectedDrum.value
  const layer = selectedLayer.value
  
  if (drum && layer) {
    loadPreview(drum, layer)
  }
})
```

### Large List Optimization

```vue
<!-- VirtualList.vue - Only render visible items -->
<template>
  <div 
    ref="containerRef"
    class="virtual-list" 
    @scroll="handleScroll"
    :style="{ height: containerHeight + 'px' }"
  >
    <div :style="{ height: totalHeight + 'px', position: 'relative' }">
      <div
        v-for="item in visibleItems"
        :key="item.index"
        :style="{
          position: 'absolute',
          top: item.top + 'px',
          height: itemHeight + 'px',
          width: '100%'
        }"
      >
        <slot :item="item.data" :index="item.index" />
      </div>
    </div>
  </div>
</template>

<script>
export default {
  name: 'VirtualList',
  props: {
    items: { type: Array, required: true },
    itemHeight: { type: Number, default: 50 },
    containerHeight: { type: Number, default: 400 },
    overscan: { type: Number, default: 5 }
  },
  setup(props) {
    const containerRef = ref(null)
    const scrollTop = ref(0)
    
    const totalHeight = computed(() => props.items.length * props.itemHeight)
    
    const visibleStartIndex = computed(() => {
      return Math.max(0, Math.floor(scrollTop.value / props.itemHeight) - props.overscan)
    })
    
    const visibleEndIndex = computed(() => {
      const endIndex = Math.min(
        props.items.length - 1,
        Math.ceil((scrollTop.value + props.containerHeight) / props.itemHeight) + props.overscan
      )
      return endIndex
    })
    
    const visibleItems = computed(() => {
      const items = []
      for (let i = visibleStartIndex.value; i <= visibleEndIndex.value; i++) {
        items.push({
          index: i,
          data: props.items[i],
          top: i * props.itemHeight
        })
      }
      return items
    })
    
    const handleScroll = (event) => {
      scrollTop.value = event.target.scrollTop
    }
    
    return {
      containerRef,
      totalHeight,
      visibleItems,
      handleScroll
    }
  }
}
</script>

<!-- Usage -->
<template>
  <VirtualList 
    :items="layers" 
    :item-height="60" 
    :container-height="400"
  >
    <template #default="{ item, index }">
      <LayerItem :layer="item" :index="index" />
    </template>
  </VirtualList>
</template>
```

## Memory Management

### Preventing Memory Leaks

```javascript
// 1. Clean up event listeners
export default {
  setup() {
    const cleanup = () => {
      // Clean up logic
    }
    
    onMounted(() => {
      // Setup logic
      window.addEventListener('resize', handleResize)
    })
    
    onBeforeUnmount(() => {
      // Clean up
      window.removeEventListener('resize', handleResize)
      cleanup()
    })
  }
}

// 2. Clean up timers and intervals
export default {
  setup() {
    const intervalId = ref(null)
    const timeoutId = ref(null)
    
    onMounted(() => {
      intervalId.value = setInterval(() => {
        // Periodic task
      }, 1000)
      
      timeoutId.value = setTimeout(() => {
        // Delayed task
      }, 5000)
    })
    
    onBeforeUnmount(() => {
      if (intervalId.value) {
        clearInterval(intervalId.value)
      }
      if (timeoutId.value) {
        clearTimeout(timeoutId.value)
      }
    })
  }
}

// 3. Clean up object URLs
export default {
  setup() {
    const imageUrl = ref(null)
    
    const loadImage = async () => {
      const response = await fetch('/api/preview')
      const blob = await response.blob()
      
      // Clean up previous URL
      if (imageUrl.value) {
        URL.revokeObjectURL(imageUrl.value)
      }
      
      imageUrl.value = URL.createObjectURL(blob)
    }
    
    onBeforeUnmount(() => {
      if (imageUrl.value) {
        URL.revokeObjectURL(imageUrl.value)
      }
    })
    
    return { imageUrl, loadImage }
  }
}
```

### Efficient Data Structures

```javascript
// 1. Use Map for frequent lookups
const drumMap = new Map()
const addDrum = (drum) => {
  drumMap.set(drum.id, drum) // O(1) lookup
}
const getDrum = (id) => drumMap.get(id) // O(1) lookup

// 2. Use Set for unique values
const activeDrumIds = new Set()
const toggleDrum = (drumId) => {
  if (activeDrumIds.has(drumId)) {
    activeDrumIds.delete(drumId)
  } else {
    activeDrumIds.add(drumId)
  }
}

// 3. Avoid deep watching large objects
const largeData = ref({ /* 1000+ properties */ })

// ❌ Bad - watches entire object
watch(largeData, () => { /* ... */ })

// ✅ Good - watch specific properties
watch(() => largeData.value.specificProperty, () => { /* ... */ })

// ✅ Better - use shallowRef for large objects
const largeData = shallowRef({ /* large object */ })
```

## Network Optimization

### API Call Optimization

```javascript
// 1. Request deduplication
const pendingRequests = new Map()

const dedupedFetch = async (url) => {
  if (pendingRequests.has(url)) {
    return pendingRequests.get(url)
  }
  
  const promise = fetch(url).then(res => res.json())
  pendingRequests.set(url, promise)
  
  try {
    const result = await promise
    return result
  } finally {
    pendingRequests.delete(url)
  }
}

// 2. Intelligent caching
const cache = new Map()
const CACHE_DURATION = 5 * 60 * 1000 // 5 minutes

const cachedFetch = async (url) => {
  const cached = cache.get(url)
  
  if (cached && Date.now() - cached.timestamp < CACHE_DURATION) {
    return cached.data
  }
  
  const data = await fetch(url).then(res => res.json())
  cache.set(url, { data, timestamp: Date.now() })
  
  return data
}

// 3. Progressive loading
const loadDrumData = async (drumId) => {
  // Load basic info first
  const basicInfo = await fetch(`/api/drums/${drumId}/basic`)
  
  // Then load detailed data
  const detailedData = await fetch(`/api/drums/${drumId}/detailed`)
  
  return { ...basicInfo, ...detailedData }
}
```

### Image Optimization

```vue
<!-- LazyImage.vue - Lazy loading with optimization -->
<template>
  <div class="lazy-image" :style="{ width: width + 'px', height: height + 'px' }">
    <img
      v-if="isLoaded"
      :src="src"
      :alt="alt"
      :style="{ width: width + 'px', height: height + 'px' }"
      class="image"
      @load="onLoad"
      @error="onError"
    >
    <div v-else class="placeholder">
      <div class="loading-spinner"></div>
    </div>
  </div>
</template>

<script>
export default {
  name: 'LazyImage',
  props: {
    src: { type: String, required: true },
    alt: { type: String, default: '' },
    width: { type: Number, default: 300 },
    height: { type: Number, default: 200 }
  },
  setup(props) {
    const isLoaded = ref(false)
    const hasError = ref(false)
    const imageRef = ref(null)
    
    const loadImage = () => {
      const img = new Image()
      img.onload = () => {
        isLoaded.value = true
      }
      img.onerror = () => {
        hasError.value = true
      }
      img.src = props.src
    }
    
    // Intersection Observer for lazy loading
    let observer = null
    
    onMounted(() => {
      observer = new IntersectionObserver((entries) => {
        entries.forEach(entry => {
          if (entry.isIntersecting) {
            loadImage()
            observer.unobserve(entry.target)
          }
        })
      })
      
      if (imageRef.value) {
        observer.observe(imageRef.value)
      }
    })
    
    onBeforeUnmount(() => {
      if (observer) {
        observer.disconnect()
      }
    })
    
    return {
      isLoaded,
      hasError,
      imageRef
    }
  }
}
</script>
```

## Performance Monitoring

### Vue DevTools Integration

```javascript
// Performance monitoring composable
export function usePerformanceMonitor() {
  const metrics = reactive({
    componentRenderTime: 0,
    apiResponseTime: 0,
    memoryUsage: 0,
    fps: 60
  })
  
  const startTimer = () => performance.now()
  const endTimer = (startTime) => performance.now() - startTime
  
  const measureRenderTime = (componentName) => {
    return (target) => {
      const startTime = startTimer()
      const result = target()
      const renderTime = endTimer(startTime)
      
      console.log(`${componentName} render time: ${renderTime}ms`)
      metrics.componentRenderTime = renderTime
      
      return result
    }
  }
  
  const measureApiCall = async (apiCall, callName) => {
    const startTime = startTimer()
    try {
      const result = await apiCall()
      const responseTime = endTimer(startTime)
      
      console.log(`${callName} response time: ${responseTime}ms`)
      metrics.apiResponseTime = responseTime
      
      return result
    } catch (error) {
      const errorTime = endTimer(startTime)
      console.error(`${callName} failed after ${errorTime}ms:`, error)
      throw error
    }
  }
  
  const measureMemoryUsage = () => {
    if (performance.memory) {
      metrics.memoryUsage = performance.memory.usedJSHeapSize / 1024 / 1024 // MB
    }
  }
  
  const measureFPS = () => {
    let lastTime = performance.now()
    let frames = 0
    
    const measure = () => {
      frames++
      const currentTime = performance.now()
      
      if (currentTime - lastTime >= 1000) {
        metrics.fps = Math.round((frames * 1000) / (currentTime - lastTime))
        frames = 0
        lastTime = currentTime
      }
      
      requestAnimationFrame(measure)
    }
    
    requestAnimationFrame(measure)
  }
  
  return {
    metrics,
    measureRenderTime,
    measureApiCall,
    measureMemoryUsage,
    measureFPS
  }
}
```

### Production Performance Monitoring

```javascript
// Performance tracking in production
export function useProductionMonitoring() {
  const trackPerformance = (metric, value, tags = {}) => {
    // Send to monitoring service (e.g., DataDog, New Relic)
    if (process.env.NODE_ENV === 'production') {
      // analytics.track('performance', { metric, value, ...tags })
      console.log(`Performance: ${metric} = ${value}`, tags)
    }
  }
  
  const trackComponentRender = (componentName, renderTime) => {
    trackPerformance('component_render_time', renderTime, {
      component: componentName,
      threshold: renderTime > 16 ? 'slow' : 'fast' // 16ms = 60fps
    })
  }
  
  const trackApiCall = (endpoint, responseTime, status) => {
    trackPerformance('api_response_time', responseTime, {
      endpoint,
      status,
      threshold: responseTime > 1000 ? 'slow' : 'fast'
    })
  }
  
  const trackBundleSize = () => {
    // Track bundle size on app load
    if (window.performance && window.performance.getEntriesByType) {
      const resources = window.performance.getEntriesByType('resource')
      const jsResources = resources.filter(r => r.name.endsWith('.js'))
      
      jsResources.forEach(resource => {
        trackPerformance('bundle_size', resource.transferSize, {
          file: resource.name,
          type: 'javascript'
        })
      })
    }
  }
  
  return {
    trackPerformance,
    trackComponentRender,
    trackApiCall,
    trackBundleSize
  }
}
```

## Industrial Application Performance Patterns

### Real-time Data Optimization for RecoaterHMI

```javascript
// optimized-drum-monitoring.js - RecoaterHMI specific patterns
export function useOptimizedDrumMonitoring() {
  const drumStates = shallowRef(new Map()) // Use shallowRef for large data
  const updateQueue = ref([])
  const isProcessing = ref(false)
  const performanceMetrics = reactive({
    updateCount: 0,
    averageUpdateTime: 0,
    lastUpdateTime: null
  })
  
  // Batch updates for performance - critical for real-time industrial data
  const processUpdates = async () => {
    if (isProcessing.value || updateQueue.value.length === 0) return
    
    const startTime = performance.now()
    isProcessing.value = true
    
    try {
      // Process updates in batches of 10 to prevent UI blocking
      const batch = updateQueue.value.splice(0, 10)
      
      // Use Map operations for O(1) updates
      batch.forEach(update => {
        drumStates.value.set(update.drumId, {
          ...drumStates.value.get(update.drumId),
          ...update.data,
          lastUpdate: Date.now()
        })
      })
      
      // Allow UI to update using Vue's scheduler
      await nextTick()
      
      // Update performance metrics
      const updateTime = performance.now() - startTime
      performanceMetrics.updateCount++
      performanceMetrics.averageUpdateTime = 
        (performanceMetrics.averageUpdateTime * (performanceMetrics.updateCount - 1) + updateTime) / 
        performanceMetrics.updateCount
      performanceMetrics.lastUpdateTime = new Date().toISOString()
      
    } finally {
      isProcessing.value = false
      
      // Continue processing remaining updates
      if (updateQueue.value.length > 0) {
        // Use RAF for 60fps updates
        requestAnimationFrame(() => processUpdates())
      }
    }
  }
  
  const updateDrumState = (drumId, data) => {
    // Prevent duplicate updates in queue
    const existingIndex = updateQueue.value.findIndex(u => u.drumId === drumId)
    if (existingIndex >= 0) {
      // Merge with existing update
      Object.assign(updateQueue.value[existingIndex].data, data)
    } else {
      updateQueue.value.push({ drumId, data })
    }
    
    processUpdates()
  }
  
  // Optimized WebSocket with exponential backoff and performance monitoring
  const connectWebSocket = () => {
    let reconnectDelay = 1000
    const maxDelay = 30000
    let connectionStartTime = null
    
    const connect = () => {
      connectionStartTime = performance.now()
      const ws = new WebSocket(`${window.location.protocol === 'https:' ? 'wss:' : 'ws:'}//${window.location.host}/ws/drums`)
      
      ws.onopen = () => {
        const connectionTime = performance.now() - connectionStartTime
        console.log(`WebSocket connected in ${connectionTime}ms`)
        reconnectDelay = 1000 // Reset delay on successful connection
      }
      
      ws.onmessage = (event) => {
        try {
          const data = JSON.parse(event.data)
          
          // Handle different message types efficiently
          switch (data.type) {
            case 'drum_status':
              updateDrumState(data.drumId, { status: data.status })
              break
            case 'drum_temperature':
              updateDrumState(data.drumId, { temperature: data.temperature })
              break
            case 'drum_batch_update':
              // Handle multiple drum updates in one message
              data.updates.forEach(update => {
                updateDrumState(update.drumId, update.data)
              })
              break
            default:
              console.warn('Unknown message type:', data.type)
          }
        } catch (error) {
          console.error('Failed to parse WebSocket message:', error)
        }
      }
      
      ws.onclose = () => {
        console.log('WebSocket disconnected, attempting reconnect...')
        setTimeout(() => {
          reconnectDelay = Math.min(reconnectDelay * 1.5, maxDelay)
          connect()
        }, reconnectDelay)
      }
      
      ws.onerror = (error) => {
        console.error('WebSocket error:', error)
        ws.close()
      }
    }
    
    connect()
  }
  
  return {
    drumStates: readonly(drumStates),
    performanceMetrics: readonly(performanceMetrics),
    updateDrumState,
    connectWebSocket
  }
}
```

### Memory-Efficient Layer Preview Caching

```javascript
// layer-preview-cache.js - Optimized for RecoaterHMI layer previews
export function useLayerPreviewCache() {
  const cache = new Map()
  const maxCacheSize = 50 // Limit cache size to prevent memory issues
  const cacheMetrics = reactive({
    hits: 0,
    misses: 0,
    totalRequests: 0,
    memoryUsage: 0
  })
  
  // LRU (Least Recently Used) cache implementation
  class LRUCache {
    constructor(maxSize) {
      this.maxSize = maxSize
      this.cache = new Map()
    }
    
    get(key) {
      if (this.cache.has(key)) {
        // Move to end (most recently used)
        const value = this.cache.get(key)
        this.cache.delete(key)
        this.cache.set(key, value)
        return value
      }
      return null
    }
    
    set(key, value) {
      if (this.cache.has(key)) {
        this.cache.delete(key)
      } else if (this.cache.size >= this.maxSize) {
        // Remove least recently used (first item)
        const firstKey = this.cache.keys().next().value
        const oldValue = this.cache.get(firstKey)
        this.cache.delete(firstKey)
        
        // Clean up blob URLs to prevent memory leaks
        if (oldValue?.blobUrl) {
          URL.revokeObjectURL(oldValue.blobUrl)
        }
      }
      
      this.cache.set(key, value)
    }
    
    clear() {
      // Clean up all blob URLs
      this.cache.forEach(value => {
        if (value?.blobUrl) {
          URL.revokeObjectURL(value.blobUrl)
        }
      })
      this.cache.clear()
    }
    
    size() {
      return this.cache.size
    }
  }
  
  const lruCache = new LRUCache(maxCacheSize)
  
  const getCacheKey = (drumId, layerNumber) => `${drumId}-${layerNumber}`
  
  const getPreview = async (drumId, layerNumber) => {
    const key = getCacheKey(drumId, layerNumber)
    cacheMetrics.totalRequests++
    
    // Check cache first
    const cached = lruCache.get(key)
    if (cached) {
      cacheMetrics.hits++
      return cached
    }
    
    // Cache miss - fetch from API
    cacheMetrics.misses++
    
    try {
      const response = await apiService.getDrumGeometryPreview(drumId, layerNumber)
      const blob = response.data
      const blobUrl = URL.createObjectURL(blob)
      
      const previewData = {
        blobUrl,
        timestamp: Date.now(),
        size: blob.size,
        drumId,
        layerNumber
      }
      
      // Store in cache
      lruCache.set(key, previewData)
      
      // Update memory usage estimate
      updateMemoryUsage()
      
      return previewData
    } catch (error) {
      console.error('Failed to fetch preview:', error)
      throw error
    }
  }
  
  const preloadAdjacent = async (drumId, currentLayer, maxLayers) => {
    // Preload adjacent layers for smoother navigation
    const preloadTasks = []
    
    // Preload previous layer
    if (currentLayer > 1) {
      preloadTasks.push(getPreview(drumId, currentLayer - 1))
    }
    
    // Preload next layer
    if (currentLayer < maxLayers) {
      preloadTasks.push(getPreview(drumId, currentLayer + 1))
    }
    
    // Execute preloads in background (don't wait for them)
    Promise.allSettled(preloadTasks).then(results => {
      console.log('Preloaded adjacent layers:', results.length)
    })
  }
  
  const updateMemoryUsage = () => {
    let totalSize = 0
    lruCache.cache.forEach(value => {
      totalSize += value.size || 0
    })
    cacheMetrics.memoryUsage = totalSize
  }
  
  const clearCache = () => {
    lruCache.clear()
    cacheMetrics.memoryUsage = 0
  }
  
  // Cleanup on page unload
  window.addEventListener('beforeunload', clearCache)
  
  return {
    getPreview,
    preloadAdjacent,
    clearCache,
    cacheMetrics: readonly(cacheMetrics)
  }
}
```

### Performance Monitoring Dashboard

```vue
<!-- PerformanceMonitor.vue - Debug component for development -->
<template>
  <div v-if="showMonitor" class="performance-monitor">
    <div class="monitor-header">
      <h4>Performance Monitor</h4>
      <button @click="toggleMonitor">{{ expanded ? 'Collapse' : 'Expand' }}</button>
    </div>
    
    <div v-if="expanded" class="monitor-content">
      <!-- Memory Usage -->
      <div class="metric-section">
        <h5>Memory Usage</h5>
        <div class="metric-grid">
          <div class="metric">
            <span class="label">Heap Used:</span>
            <span class="value">{{ formatBytes(memoryMetrics.usedJSHeapSize) }}</span>
          </div>
          <div class="metric">
            <span class="label">Heap Total:</span>
            <span class="value">{{ formatBytes(memoryMetrics.totalJSHeapSize) }}</span>
          </div>
          <div class="metric">
            <span class="label">Heap Limit:</span>
            <span class="value">{{ formatBytes(memoryMetrics.jsHeapSizeLimit) }}</span>
          </div>
        </div>
      </div>
      
      <!-- Performance Metrics -->
      <div class="metric-section">
        <h5>Performance</h5>
        <div class="metric-grid">
          <div class="metric">
            <span class="label">FPS:</span>
            <span class="value" :class="{ warning: fps < 30, error: fps < 15 }">
              {{ Math.round(fps) }}
            </span>
          </div>
          <div class="metric">
            <span class="label">Render Time:</span>
            <span class="value">{{ renderTime }}ms</span>
          </div>
          <div class="metric">
            <span class="label">Component Count:</span>
            <span class="value">{{ componentCount }}</span>
          </div>
        </div>
      </div>
      
      <!-- API Performance -->
      <div class="metric-section">
        <h5>API Performance</h5>
        <div class="metric-grid">
          <div class="metric">
            <span class="label">Avg Response:</span>
            <span class="value">{{ apiMetrics.averageResponseTime }}ms</span>
          </div>
          <div class="metric">
            <span class="label">Requests/sec:</span>
            <span class="value">{{ apiMetrics.requestsPerSecond }}</span>
          </div>
          <div class="metric">
            <span class="label">Error Rate:</span>
            <span class="value" :class="{ warning: apiMetrics.errorRate > 5 }">
              {{ apiMetrics.errorRate }}%
            </span>
          </div>
        </div>
      </div>
      
      <!-- Cache Performance -->
      <div class="metric-section">
        <h5>Cache Performance</h5>
        <div class="metric-grid">
          <div class="metric">
            <span class="label">Hit Rate:</span>
            <span class="value">{{ cacheHitRate }}%</span>
          </div>
          <div class="metric">
            <span class="label">Cache Size:</span>
            <span class="value">{{ formatBytes(cacheSize) }}</span>
          </div>
          <div class="metric">
            <span class="label">Cache Entries:</span>
            <span class="value">{{ cacheEntries }}</span>
          </div>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup>
import { ref, reactive, computed, onMounted, onBeforeUnmount } from 'vue'

const showMonitor = ref(process.env.NODE_ENV === 'development')
const expanded = ref(false)

const memoryMetrics = reactive({
  usedJSHeapSize: 0,
  totalJSHeapSize: 0,
  jsHeapSizeLimit: 0
})

const fps = ref(60)
const renderTime = ref(0)
const componentCount = ref(0)
const apiMetrics = reactive({
  averageResponseTime: 0,
  requestsPerSecond: 0,
  errorRate: 0
})

const cacheMetrics = reactive({
  hits: 0,
  misses: 0,
  totalSize: 0,
  entries: 0
})

const cacheHitRate = computed(() => {
  const total = cacheMetrics.hits + cacheMetrics.misses
  return total > 0 ? Math.round((cacheMetrics.hits / total) * 100) : 0
})

const cacheSize = computed(() => cacheMetrics.totalSize)
const cacheEntries = computed(() => cacheMetrics.entries)

let performanceObserver = null
let fpsCounter = null

const updateMemoryMetrics = () => {
  if (performance.memory) {
    Object.assign(memoryMetrics, performance.memory)
  }
}

const measureFPS = () => {
  let frames = 0
  let lastTime = performance.now()
  
  const measure = () => {
    frames++
    const currentTime = performance.now()
    
    if (currentTime - lastTime >= 1000) {
      fps.value = Math.round((frames * 1000) / (currentTime - lastTime))
      frames = 0
      lastTime = currentTime
    }
    
    fpsCounter = requestAnimationFrame(measure)
  }
  
  fpsCounter = requestAnimationFrame(measure)
}

const formatBytes = (bytes) => {
  if (bytes === 0) return '0 B'
  const k = 1024
  const sizes = ['B', 'KB', 'MB', 'GB']
  const i = Math.floor(Math.log(bytes) / Math.log(k))
  return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i]
}

const toggleMonitor = () => {
  expanded.value = !expanded.value
}

onMounted(() => {
  // Update memory metrics every second
  const memoryInterval = setInterval(updateMemoryMetrics, 1000)
  
  // Start FPS measurement
  measureFPS()
  
  // Performance Observer for render timing
  if (window.PerformanceObserver) {
    performanceObserver = new PerformanceObserver((list) => {
      const entries = list.getEntries()
      entries.forEach(entry => {
        if (entry.entryType === 'measure') {
          renderTime.value = Math.round(entry.duration)
        }
      })
    })
    
    performanceObserver.observe({ entryTypes: ['measure'] })
  }
  
  onBeforeUnmount(() => {
    clearInterval(memoryInterval)
    if (fpsCounter) {
      cancelAnimationFrame(fpsCounter)
    }
    if (performanceObserver) {
      performanceObserver.disconnect()
    }
  })
})
</script>

<style scoped>
.performance-monitor {
  position: fixed;
  top: 10px;
  right: 10px;
  background: rgba(0, 0, 0, 0.9);
  color: white;
  padding: 1rem;
  border-radius: 8px;
  font-size: 12px;
  max-width: 300px;
  z-index: 9999;
  font-family: 'Courier New', monospace;
}

.monitor-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 0.5rem;
}

.monitor-header button {
  background: transparent;
  border: 1px solid #666;
  color: white;
  padding: 2px 8px;
  border-radius: 4px;
  cursor: pointer;
  font-size: 10px;
}

.metric-section {
  margin-bottom: 1rem;
}

.metric-section h5 {
  margin: 0 0 0.5rem 0;
  color: #00ff00;
  font-size: 11px;
}

.metric-grid {
  display: grid;
  grid-template-columns: 1fr;
  gap: 0.25rem;
}

.metric {
  display: flex;
  justify-content: space-between;
}

.label {
  color: #ccc;
}

.value {
  color: #00ff00;
  font-weight: bold;
}

.value.warning {
  color: #ffaa00;
}

.value.error {
  color: #ff4444;
}
</style>
```

### Critical Path Optimization for RecoaterHMI

```javascript
// critical-path-optimization.js
export function useCriticalPathOptimization() {
  const criticalFeatures = new Set(['connection-status', 'drum-control', 'emergency-stop'])
  const nonCriticalFeatures = new Set(['settings', 'analytics', 'reports', 'help'])
  
  const isCriticalLoaded = ref(false)
  const loadingProgress = ref(0)
  const criticalComponents = shallowRef(new Map())
  const nonCriticalComponents = shallowRef(new Map())
  
  // Load critical features first for safety-critical operations
  const loadCriticalFeatures = async () => {
    console.log('Loading critical features for industrial safety...')
    
    const criticalImports = [
      import('./components/ConnectionStatus.vue'),
      import('./components/DrumControl.vue'),
      import('./components/EmergencyStop.vue'),
      import('./stores/statusStore.js'),
      import('./stores/safetyStore.js')
    ]
    
    const results = await Promise.allSettled(criticalImports)
    
    results.forEach((result, index) => {
      if (result.status === 'fulfilled') {
        const componentNames = ['ConnectionStatus', 'DrumControl', 'EmergencyStop', 'statusStore', 'safetyStore']
        criticalComponents.value.set(componentNames[index], result.value.default || result.value)
      } else {
        console.error(`Failed to load critical component ${index}:`, result.reason)
      }
    })
    
    isCriticalLoaded.value = true
    loadingProgress.value = 50
    
    console.log('Critical features loaded successfully')
  }
  
  // Lazy load non-critical features after critical ones are ready
  const loadNonCriticalFeatures = async () => {
    if (!isCriticalLoaded.value) {
      await loadCriticalFeatures()
    }
    
    console.log('Loading non-critical features...')
    
    // Load non-critical features with lower priority
    const nonCriticalImports = [
      () => import('./components/SettingsPanel.vue'),
      () => import('./components/AnalyticsDashboard.vue'),
      () => import('./components/ReportsView.vue'),
      () => import('./components/HelpDocumentation.vue')
    ]
    
    // Load one at a time to avoid blocking main thread
    for (const [index, importFn] of nonCriticalImports.entries()) {
      try {
        await new Promise(resolve => setTimeout(resolve, 100)) // Small delay
        const component = await importFn()
        const componentNames = ['SettingsPanel', 'AnalyticsDashboard', 'ReportsView', 'HelpDocumentation']
        nonCriticalComponents.value.set(componentNames[index], component.default)
        
        loadingProgress.value = 50 + ((index + 1) / nonCriticalImports.length) * 50
      } catch (error) {
        console.warn(`Failed to load non-critical component ${index}:`, error)
      }
    }
    
    console.log('All features loaded')
  }
  
  // Preload features during idle time
  const preloadDuringIdle = () => {
    if ('requestIdleCallback' in window) {
      requestIdleCallback(() => {
        loadNonCriticalFeatures()
      }, { timeout: 5000 })
    } else {
      // Fallback for browsers without requestIdleCallback
      setTimeout(loadNonCriticalFeatures, 2000)
    }
  }
  
  // Progressive enhancement based on network conditions
  const adaptToNetworkConditions = () => {
    if ('connection' in navigator) {
      const connection = navigator.connection
      
      if (connection.effectiveType === '4g' && !connection.saveData) {
        // Fast connection - load everything
        loadNonCriticalFeatures()
      } else if (connection.effectiveType === '3g') {
        // Slower connection - delay non-critical features
        setTimeout(loadNonCriticalFeatures, 5000)
      } else {
        // Very slow connection - only load on demand
        console.log('Slow connection detected - loading only critical features')
      }
    } else {
      // Unknown connection - use conservative approach
      preloadDuringIdle()
    }
  }
  
  return {
    isCriticalLoaded,
    loadingProgress: readonly(loadingProgress),
    criticalComponents: readonly(criticalComponents),
    nonCriticalComponents: readonly(nonCriticalComponents),
    loadCriticalFeatures,
    loadNonCriticalFeatures,
    preloadDuringIdle,
    adaptToNetworkConditions
  }
}
```

*Performance optimization is especially critical in industrial applications where system responsiveness can impact safety and productivity. These patterns ensure your RecoaterHMI application performs optimally under real-world conditions.*

## Performance Profiling Tools

### Vue DevTools Performance Tab

```javascript
// Enable performance tracking in development
if (process.env.NODE_ENV === 'development') {
  // Track component updates
  const trackUpdates = (component) => {
    let updateCount = 0
    const originalUpdate = component._update
    
    component._update = function() {
      updateCount++
      console.log(`${component.$?.options?.name || 'Anonymous'} updated ${updateCount} times`)
      return originalUpdate.apply(this, arguments)
    }
  }
  
  // Track watcher performance
  const trackWatchers = (component) => {
    component.$watchers?.forEach((watcher, index) => {
      const originalRun = watcher.run
      watcher.run = function() {
        const start = performance.now()
        const result = originalRun.apply(this, arguments)
        const duration = performance.now() - start
        
        if (duration > 10) { // Log slow watchers
          console.warn(`Slow watcher ${index}: ${duration}ms`)
        }
        
        return result
      }
    })
  }
}
```

### Lighthouse Performance Audit

```javascript
// Automated performance testing
export function useLighthouseAudit() {
  const runAudit = async () => {
    if ('chrome' in window) {
      // Use Lighthouse programmatically
      const { default: lighthouse } = await import('lighthouse')
      
      const runnerResult = await lighthouse('http://localhost:3000', {
        logLevel: 'info',
        output: 'json',
        onlyCategories: ['performance']
      })
      
      const report = runnerResult.report
      console.log('Lighthouse Performance Score:', report.categories.performance.score)
      
      return report
    }
  }
  
  const auditComponent = (component) => {
    const metrics = {
      renderCount: 0,
      totalRenderTime: 0,
      averageRenderTime: 0
    }
    
    const originalRender = component.render
    component.render = function() {
      const start = performance.now()
      const result = originalRender.apply(this, arguments)
      const duration = performance.now() - start
      
      metrics.renderCount++
      metrics.totalRenderTime += duration
      metrics.averageRenderTime = metrics.totalRenderTime / metrics.renderCount
      
      return result
    }
    
    return metrics
  }
  
  return {
    runAudit,
    auditComponent
  }
}
```

## Performance Checklist

### Bundle Optimization
- [ ] Analyze bundle size with `vite-bundle-analyzer`
- [ ] Implement code splitting for routes
- [ ] Use dynamic imports for heavy components
- [ ] Enable tree shaking
- [ ] Optimize images and assets

### Reactivity Optimization
- [ ] Use computed properties instead of methods
- [ ] Implement virtual scrolling for large lists
- [ ] Use shallowRef for large objects
- [ ] Debounce rapid user input
- [ ] Avoid deep watching when possible

### Memory Management
- [ ] Clean up event listeners on unmount
- [ ] Clear timers and intervals
- [ ] Revoke object URLs
- [ ] Use WeakMap for caches
- [ ] Monitor memory usage in production

### Network Optimization
- [ ] Implement request deduplication
- [ ] Use intelligent caching strategies
- [ ] Lazy load images
- [ ] Compress responses
- [ ] Use CDN for static assets

### Monitoring and Profiling
- [ ] Set up performance monitoring
- [ ] Use Vue DevTools for debugging
- [ ] Implement error tracking
- [ ] Monitor Core Web Vitals
- [ ] Profile memory usage

## Key Takeaways

1. **Performance is User Experience**: Fast applications = happy users
2. **Measure Before Optimizing**: Use tools to identify bottlenecks
3. **Bundle Size Matters**: Every KB affects load time
4. **Reactivity Has Cost**: Optimize watchers and computed properties
5. **Memory Leaks Kill Apps**: Clean up resources properly
6. **Network is Slow**: Optimize data fetching and caching
7. **Monitor in Production**: Track performance metrics continuously

## Performance Best Practices Summary

### Development Phase
- Use performance budgets in build tools
- Implement lazy loading for non-critical features
- Profile components during development
- Use efficient data structures

### Production Phase
- Enable compression and caching
- Use CDN for static assets
- Monitor performance metrics
- Implement error boundaries
- Use performance monitoring tools

### Maintenance Phase
- Regularly audit bundle size
- Update dependencies for performance improvements
- Monitor Core Web Vitals
- Optimize based on real user data

*Performance optimization is an ongoing process. Focus on the metrics that matter most to your users and continuously improve your application's speed and efficiency.*</content>
<parameter name="filePath">c:\Users\<USER>\Downloads\SIMTech_Internship\RecoaterSearch\APIRecoater_Ethernet\docs\FrontendTextbook\Part-III-Integration\14-performance-optimization.md