# Chapter 8: Vue.js Reactivity System

## Learning Objectives
By the end of this chapter, you will understand:
- How Vue.js reactivity works under the hood
- The difference between shallow and deep reactivity
- Performance implications of reactive data
- Common reactivity pitfalls and how to avoid them
- Advanced reactivity patterns for industrial applications

## Understanding Vue.js Reactivity

Vue.js reactivity is like having a **smart notification system** that automatically updates the UI when data changes. Let's understand how this magic works.

### The Reactivity Concept

```
Traditional Programming:
Data Changes → Manual DOM Update → UI Update
     ↓              ↓                ↓
  Update Value   Remember to Update  Hope You Didn't Miss Anything

Vue.js Reactivity:
Data Changes → Automatic Detection → Automatic UI Update
     ↓              ↓                    ↓
  Update Value   Vue Tracks Changes   UI Always Synchronized
```

### Real-World Analogy

Think of Vue.js reactivity like a **smart home automation system**:

**Without Reactivity (Manual System):**
- Temperature changes
- You manually check thermometer
- You manually adjust heating/cooling
- You manually update display
- Easy to miss changes or forget steps

**With Reactivity (Smart System):**
- Temperature changes
- Sensors automatically detect change
- System automatically adjusts heating/cooling
- Display automatically updates
- Everything stays synchronized

## How Reactivity Works Under the Hood

### 1. **Proxy-Based Reactivity (Vue 3)**

Vue 3 uses JavaScript Proxies to intercept property access:

```javascript
// Simplified version of how Vue creates reactive objects
function createReactive(target) {
  return new Proxy(target, {
    get(target, key) {
      // Track: Record that this property was accessed
      track(target, key)
      return target[key]
    },
    
    set(target, key, value) {
      // Update the value
      target[key] = value
      // Trigger: Notify all watchers that this property changed
      trigger(target, key)
      return true
    }
  })
}

// When you use reactive():
const drumConfig = reactive({
  temperature: 200,
  speed: 50
})

// Behind the scenes:
// drumConfig.temperature → track() is called
// drumConfig.temperature = 220 → trigger() is called
```

### 2. **Dependency Tracking**

Vue automatically tracks which properties your component uses:

```javascript
// When this computed property runs:
const temperatureStatus = computed(() => {
  if (drumConfig.temperature < 150) return 'Too Cold'    // track(drumConfig, 'temperature')
  if (drumConfig.temperature > 250) return 'Too Hot'     // track(drumConfig, 'temperature')
  return 'Optimal'
})

// Vue knows: temperatureStatus depends on drumConfig.temperature
// When drumConfig.temperature changes → temperatureStatus recalculates
```

### 3. **Effect System**

```
Dependency Graph:
┌─────────────────┐
│ drumConfig.temp │ ← Reactive Source
└─────────────────┘
         │
         ▼
┌─────────────────┐
│ temperatureStatus│ ← Computed Property
└─────────────────┘
         │
         ▼
┌─────────────────┐
│ Template        │ ← UI Component
└─────────────────┘

When drumConfig.temp changes:
1. Vue detects the change
2. Recalculates temperatureStatus
3. Updates the template automatically
```

## Types of Reactivity

### 1. **ref() - Reactive Primitives**

```javascript
import { ref } from 'vue'

const count = ref(0)

// Behind the scenes, Vue creates:
{
  value: 0,
  _isRef: true,
  // Getter/setter for .value that triggers reactivity
}

// When you access count.value:
// - Vue tracks the dependency
// When you set count.value = 1:
// - Vue triggers all dependent effects
```

### 2. **reactive() - Reactive Objects**

```javascript
import { reactive } from 'vue'

const drumState = reactive({
  id: 0,
  temperature: 200,
  isActive: false,
  settings: {
    speed: 50,
    material: 'PLA'
  }
})

// Behind the scenes, Vue creates proxies for:
// - The main object
// - Nested objects (deep reactivity)
// - Arrays and their methods
```

### 3. **Deep vs Shallow Reactivity**

```javascript
// Deep reactivity (default)
const deepReactive = reactive({
  level1: {
    level2: {
      level3: {
        value: 'deeply nested'
      }
    }
  }
})

// Changing deeply nested values triggers reactivity
deepReactive.level1.level2.level3.value = 'changed'  // ✅ Reactive

// Shallow reactivity (performance optimization)
import { shallowReactive } from 'vue'

const shallowReactive = shallowReactive({
  level1: {
    level2: {
      value: 'shallow'
    }
  }
})

// Only first level is reactive
shallowReactive.level1 = { newObject: true }  // ✅ Reactive
shallowReactive.level1.level2.value = 'changed'  // ❌ NOT reactive
```

## Performance Implications

### 1. **Reactivity Overhead**

```javascript
// High overhead - deep reactivity for large objects
const hugeDrumData = reactive({
  layers: new Array(10000).fill(null).map((_, i) => ({
    id: i,
    thickness: 0.1,
    material: 'PLA',
    temperature: 200,
    coordinates: new Array(1000).fill(null).map(() => ({
      x: Math.random(),
      y: Math.random()
    }))
  }))
})

// Better - shallow reactivity for large, rarely-changing data
const hugeDrumData = shallowReactive({
  layers: layersArray,  // Array reference is reactive
  metadata: metadataObject
})
```

### 2. **Computed Property Caching**

```javascript
// Expensive computation
const processedLayers = computed(() => {
  console.log('Computing processed layers...')  // Only runs when dependencies change
  
  return hugeDrumData.layers
    .filter(layer => layer.isActive)
    .map(layer => ({
      ...layer,
      processedData: expensiveProcessing(layer)
    }))
})

// Called multiple times, but only computes once:
console.log(processedLayers.value)  // Runs computation
console.log(processedLayers.value)  // Uses cached result
console.log(processedLayers.value)  // Uses cached result

// Changes dependency, computation runs again:
hugeDrumData.layers.push(newLayer)  // Triggers recomputation
```

### 3. **Performance Best Practices**

```javascript
// ✅ GOOD: Use appropriate reactivity level
const drumConfig = reactive({
  id: 0,
  name: 'Drum 1',
  settings: {
    temperature: 200,
    speed: 50
  }
})

// ✅ GOOD: Use shallowRef for large arrays that get replaced
const largeDataSet = shallowRef([])
largeDataSet.value = newDataArray  // Triggers reactivity

// ❌ BAD: Making everything reactive unnecessarily
const constantData = reactive({
  PI: 3.14159,
  DRUM_COUNT: 3,
  API_ENDPOINTS: {
    DRUM: '/api/drums',
    LAYER: '/api/layers'
  }
})

// ✅ GOOD: Keep constants as regular objects
const CONSTANTS = {
  PI: 3.14159,
  DRUM_COUNT: 3,
  API_ENDPOINTS: {
    DRUM: '/api/drums',
    LAYER: '/api/layers'
  }
}
```

## Common Reactivity Pitfalls

### 1. **Destructuring Reactive Objects**

```javascript
const drumState = reactive({
  id: 0,
  temperature: 200,
  isActive: false
})

// ❌ BAD: Loses reactivity
const { temperature, isActive } = drumState
temperature = 220  // Won't trigger reactivity!

// ✅ GOOD: Keep object reference
const updateTemperature = (newTemp) => {
  drumState.temperature = newTemp  // ✅ Reactive
}

// ✅ GOOD: Use toRefs for destructuring
import { toRefs } from 'vue'
const { temperature, isActive } = toRefs(drumState)
temperature.value = 220  // ✅ Reactive
```

### 2. **Array Index Assignment**

```javascript
const layers = reactive([
  { id: 1, name: 'Base' },
  { id: 2, name: 'Support' }
])

// ✅ GOOD: These trigger reactivity
layers.push({ id: 3, name: 'Top' })
layers.splice(1, 1)
layers[0] = { id: 1, name: 'New Base' }

// ✅ GOOD: Array methods work
layers.sort((a, b) => a.id - b.id)
layers.reverse()
```

### 3. **Async Function Pitfalls**

```javascript
// ❌ PROBLEM: Reactivity might be lost in async contexts
const loadDrumData = async () => {
  const response = await fetch('/api/drums')
  const data = await response.json()
  
  // This might not be reactive if data comes from outside Vue
  drumState.value = data  
}

// ✅ SOLUTION: Explicitly assign reactive properties
const loadDrumData = async () => {
  const response = await fetch('/api/drums')
  const data = await response.json()
  
  // Assign to reactive properties explicitly
  Object.assign(drumState, data)
  // Or:
  drumState.temperature = data.temperature
  drumState.speed = data.speed
  drumState.isActive = data.isActive
}
```

### 4. **Computed Property Mutations**

```javascript
const drumConfig = reactive({
  temperature: 200,
  speed: 50
})

// ❌ BAD: Don't mutate computed properties
const drumSettings = computed(() => {
  const settings = drumConfig.settings
  settings.temperature += 10  // DON'T DO THIS!
  return settings
})

// ✅ GOOD: Computed properties should be pure
const adjustedTemperature = computed(() => {
  return drumConfig.temperature + 10  // Pure calculation
})

// ✅ GOOD: Use methods for mutations
const increaseDrumTemperature = () => {
  drumConfig.temperature += 10
}
```

## Advanced Reactivity Patterns

### 1. **Custom Ref with Validation**

```javascript
import { customRef } from 'vue'

function useValidatedRef(initialValue, validator) {
  return customRef((track, trigger) => {
    let value = initialValue
    
    return {
      get() {
        track()  // Track dependency
        return value
      },
      
      set(newValue) {
        if (validator(newValue)) {
          value = newValue
          trigger()  // Trigger reactivity
        } else {
          console.warn('Invalid value:', newValue)
        }
      }
    }
  })
}

// Usage:
const drumTemperature = useValidatedRef(200, (temp) => {
  return temp >= 100 && temp <= 300
})

drumTemperature.value = 250  // ✅ Valid, updates
drumTemperature.value = 400  // ❌ Invalid, ignored with warning
```

### 2. **Debounced Reactive Values**

```javascript
import { customRef } from 'vue'

function useDebouncedRef(value, delay = 300) {
  let timeout
  
  return customRef((track, trigger) => {
    return {
      get() {
        track()
        return value
      },
      
      set(newValue) {
        clearTimeout(timeout)
        timeout = setTimeout(() => {
          value = newValue
          trigger()
        }, delay)
      }
    }
  })
}

// Usage: Only updates after user stops typing for 300ms
const searchQuery = useDebouncedRef('')
```

### 3. **Reactive State Machine**

```javascript
import { reactive, computed } from 'vue'

function useDrumStateMachine() {
  const state = reactive({
    current: 'idle',
    context: {
      temperature: 0,
      errorMessage: null
    }
  })
  
  const canStart = computed(() => {
    return state.current === 'idle' && state.context.temperature > 150
  })
  
  const canStop = computed(() => {
    return ['heating', 'printing'].includes(state.current)
  })
  
  const transitions = {
    idle: {
      START: 'heating'
    },
    heating: {
      READY: 'ready',
      ERROR: 'error'
    },
    ready: {
      PRINT: 'printing',
      STOP: 'idle'
    },
    printing: {
      COMPLETE: 'idle',
      ERROR: 'error',
      STOP: 'idle'
    },
    error: {
      RESET: 'idle'
    }
  }
  
  const send = (event, payload = {}) => {
    const currentState = state.current
    const nextState = transitions[currentState]?.[event]
    
    if (nextState) {
      state.current = nextState
      
      // Update context based on transition
      if (event === 'ERROR') {
        state.context.errorMessage = payload.message
      } else if (event === 'RESET') {
        state.context.errorMessage = null
      }
    }
  }
  
  return {
    state: readonly(state),
    canStart,
    canStop,
    send
  }
}
```

## Debugging Reactivity

### 1. **Using watchEffect for Debugging**

```javascript
import { watchEffect } from 'vue'

// Debug what properties are being tracked
watchEffect(() => {
  console.log('Dependencies accessed:', {
    drumId: selectedDrum.value,
    layer: currentLayer.value,
    loading: previewLoading.value
  })
})

// Debug when computed properties recalculate
const debuggedComputed = computed(() => {
  console.log('Recomputing drum display name')
  return `Drum ${selectedDrum.value + 1}`
})
```

### 2. **Reactivity Transform (Experimental)**

```javascript
// Instead of .value everywhere:
let count = $ref(0)
let doubled = $computed(() => count * 2)

// Compiles to:
let count = ref(0)
let doubled = computed(() => count.value * 2)
```

### 3. **Vue DevTools Integration**

The Vue DevTools show:
- Component reactivity graph
- Computed property dependencies
- Performance timeline
- State changes over time

## Industrial Application Patterns

### 1. **Real-time Status Updates**

```javascript
// Reactive system status
const systemStatus = reactive({
  drums: [
    { id: 0, temperature: 200, status: 'idle' },
    { id: 1, temperature: 210, status: 'printing' },
    { id: 2, temperature: 190, status: 'heating' }
  ],
  overall: 'operational',
  lastUpdate: new Date()
})

// Computed system health
const systemHealth = computed(() => {
  const activeDrums = systemStatus.drums.filter(d => d.status !== 'error')
  const healthPercentage = (activeDrums.length / systemStatus.drums.length) * 100
  
  if (healthPercentage === 100) return 'excellent'
  if (healthPercentage >= 75) return 'good'
  if (healthPercentage >= 50) return 'warning'
  return 'critical'
})

// Auto-update from WebSocket
const updateFromWebSocket = (data) => {
  // Direct assignment triggers reactivity
  Object.assign(systemStatus, data)
  systemStatus.lastUpdate = new Date()
}
```

### 2. **Form Validation with Reactivity**

```javascript
const formData = reactive({
  drumId: null,
  layerNumber: 1,
  temperature: 200,
  material: 'PLA'
})

const validation = computed(() => {
  const errors = {}
  
  if (formData.drumId === null) {
    errors.drumId = 'Please select a drum'
  }
  
  if (formData.layerNumber < 1 || formData.layerNumber > 1000) {
    errors.layerNumber = 'Layer number must be between 1 and 1000'
  }
  
  if (formData.temperature < 100 || formData.temperature > 300) {
    errors.temperature = 'Temperature must be between 100°C and 300°C'
  }
  
  return {
    errors,
    isValid: Object.keys(errors).length === 0
  }
})

// Reactive form state
const formState = computed(() => {
  if (validation.value.isValid) return 'valid'
  if (Object.keys(formData).every(key => formData[key] !== null)) return 'invalid'
  return 'incomplete'
})
```

### 3. **Performance Monitoring**

```javascript
const performanceMetrics = reactive({
  layerProcessingTime: 0,
  memoryUsage: 0,
  apiResponseTime: 0,
  errorRate: 0
})

// Computed performance indicators
const performanceStatus = computed(() => {
  const { layerProcessingTime, memoryUsage, apiResponseTime, errorRate } = performanceMetrics
  
  const score = (
    (layerProcessingTime < 1000 ? 25 : 0) +  // Under 1 second
    (memoryUsage < 80 ? 25 : 0) +            // Under 80% memory
    (apiResponseTime < 500 ? 25 : 0) +       // Under 500ms API
    (errorRate < 0.01 ? 25 : 0)              // Under 1% error rate
  )
  
  if (score >= 75) return 'excellent'
  if (score >= 50) return 'good'
  if (score >= 25) return 'poor'
  return 'critical'
})
```

## Key Takeaways

1. **Vue.js reactivity** is based on Proxy objects that intercept property access
2. **Dependency tracking** automatically connects data changes to UI updates
3. **Performance matters** - use appropriate reactivity levels for your data
4. **Common pitfalls** can be avoided with proper understanding
5. **Advanced patterns** enable sophisticated reactive applications
6. **Debugging tools** help understand and optimize reactive code

## Best Practices Summary

```javascript
// ✅ DO:
const state = reactive({})          // Use reactive for objects
const count = ref(0)                // Use ref for primitives
const computed = computed(() => {})  // Use computed for derived values
state.property = newValue           // Direct assignment

// ❌ DON'T:
const { prop } = reactive({})       // Destructure without toRefs
const computed = computed(() => {    // Mutate in computed
  state.prop = newValue
})
```

## Next Steps

In the next chapter, we'll explore **Vue.js Component Templates** and learn how to:
- Build dynamic templates with directives
- Handle events and user interactions
- Create conditional and list rendering
- Optimize template performance

Understanding reactivity is the foundation - now let's see how it powers dynamic templates!

---

## 🚀 Mini Project: Advanced Reactivity Patterns

### Exercise 8.1: Create Performance Monitoring System

Build a sophisticated reactivity monitoring system to understand how Vue's reactivity works in practice. Create `src/composables/useReactivityMonitoring.js`:

```javascript
import { ref, reactive, computed, watch, watchEffect, onMounted, onUnmounted } from 'vue'

export function useReactivityMonitoring(componentName = 'Component') {
  // Reactive state for monitoring
  const metrics = reactive({
    renderCount: 0,
    computedEvaluations: 0,
    watcherTriggers: 0,
    effectTriggers: 0,
    dataChanges: 0,
    averageRenderTime: 0,
    lastRenderTime: 0,
    dependencies: new Set(),
    performanceHistory: []
  })

  const isRecording = ref(false)
  const startTime = ref(0)
  const monitoring = reactive({
    trackRenders: true,
    trackComputed: true,
    trackWatchers: true,
    trackEffects: true,
    trackPerformance: true,
    maxHistorySize: 100
  })

  // Performance tracking
  const performanceData = ref([])
  const memoryUsage = ref(0)
  const reactivityScore = ref(0)

  // Computed properties for analysis
  const totalOperations = computed(() => {
    return metrics.renderCount + 
           metrics.computedEvaluations + 
           metrics.watcherTriggers + 
           metrics.effectTriggers
  })

  const operationsPerSecond = computed(() => {
    if (metrics.performanceHistory.length < 2) return 0
    
    const timeSpan = metrics.performanceHistory[metrics.performanceHistory.length - 1].timestamp - 
                     metrics.performanceHistory[0].timestamp
    
    return timeSpan > 0 ? Math.round((totalOperations.value / timeSpan) * 1000) : 0
  })

  const reactivityHealth = computed(() => {
    const maxRenderTime = 16 // 60fps threshold
    const maxComputedRatio = 0.3 // 30% of total operations
    const maxWatcherRatio = 0.2 // 20% of total operations
    
    let score = 100
    
    // Penalize slow renders
    if (metrics.averageRenderTime > maxRenderTime) {
      score -= Math.min(30, (metrics.averageRenderTime - maxRenderTime) / maxRenderTime * 30)
    }
    
    // Penalize excessive computed evaluations
    if (totalOperations.value > 0) {
      const computedRatio = metrics.computedEvaluations / totalOperations.value
      if (computedRatio > maxComputedRatio) {
        score -= Math.min(25, (computedRatio - maxComputedRatio) / maxComputedRatio * 25)
      }
      
      const watcherRatio = metrics.watcherTriggers / totalOperations.value
      if (watcherRatio > maxWatcherRatio) {
        score -= Math.min(25, (watcherRatio - maxWatcherRatio) / maxWatcherRatio * 25)
      }
    }
    
    return Math.max(0, Math.round(score))
  })

  const dependencyGraph = computed(() => {
    return Array.from(metrics.dependencies).map(dep => ({
      name: dep,
      type: 'dependency',
      connections: 1 // Simplified for this example
    }))
  })

  // Monitoring functions
  const startMonitoring = () => {
    isRecording.value = true
    resetMetrics()
    console.log(`🔍 Reactivity monitoring started for ${componentName}`)
  }

  const stopMonitoring = () => {
    isRecording.value = false
    console.log(`⏹️ Reactivity monitoring stopped for ${componentName}`)
    logSummaryReport()
  }

  const resetMetrics = () => {
    Object.assign(metrics, {
      renderCount: 0,
      computedEvaluations: 0,
      watcherTriggers: 0,
      effectTriggers: 0,
      dataChanges: 0,
      averageRenderTime: 0,
      lastRenderTime: 0,
      dependencies: new Set(),
      performanceHistory: []
    })
    performanceData.value = []
  }

  // Tracking functions
  const trackRender = (renderTime = 0) => {
    if (!isRecording.value || !monitoring.trackRenders) return
    
    metrics.renderCount++
    metrics.lastRenderTime = renderTime
    
    // Update average render time
    if (renderTime > 0) {
      const totalTime = metrics.averageRenderTime * (metrics.renderCount - 1) + renderTime
      metrics.averageRenderTime = totalTime / metrics.renderCount
    }
    
    recordPerformanceData('render', renderTime)
  }

  const trackComputed = (computedName = 'unknown', evaluationTime = 0) => {
    if (!isRecording.value || !monitoring.trackComputed) return
    
    metrics.computedEvaluations++
    metrics.dependencies.add(`computed:${computedName}`)
    recordPerformanceData('computed', evaluationTime)
  }

  const trackWatcher = (watcherName = 'unknown', executionTime = 0) => {
    if (!isRecording.value || !monitoring.trackWatchers) return
    
    metrics.watcherTriggers++
    metrics.dependencies.add(`watcher:${watcherName}`)
    recordPerformanceData('watcher', executionTime)
  }

  const trackEffect = (effectName = 'unknown', executionTime = 0) => {
    if (!isRecording.value || !monitoring.trackEffects) return
    
    metrics.effectTriggers++
    metrics.dependencies.add(`effect:${effectName}`)
    recordPerformanceData('effect', executionTime)
  }

  const trackDataChange = (propertyPath = 'unknown') => {
    if (!isRecording.value) return
    
    metrics.dataChanges++
    metrics.dependencies.add(`data:${propertyPath}`)
  }

  const recordPerformanceData = (type, duration) => {
    const dataPoint = {
      timestamp: Date.now(),
      type,
      duration,
      memoryUsage: getMemoryUsage()
    }
    
    performanceData.value.push(dataPoint)
    metrics.performanceHistory.push(dataPoint)
    
    // Limit history size
    if (metrics.performanceHistory.length > monitoring.maxHistorySize) {
      metrics.performanceHistory.shift()
    }
    
    if (performanceData.value.length > monitoring.maxHistorySize) {
      performanceData.value.shift()
    }
  }

  const getMemoryUsage = () => {
    try {
      if (performance.memory) {
        return {
          used: Math.round(performance.memory.usedJSHeapSize / 1024 / 1024),
          total: Math.round(performance.memory.totalJSHeapSize / 1024 / 1024),
          limit: Math.round(performance.memory.jsHeapSizeLimit / 1024 / 1024)
        }
      }
    } catch (error) {
      console.warn('Memory monitoring not available')
    }
    return { used: 0, total: 0, limit: 0 }
  }

  // Analysis functions
  const getPerformanceReport = () => {
    const slowOperations = performanceData.value.filter(op => op.duration > 16)
    const operationsByType = performanceData.value.reduce((acc, op) => {
      acc[op.type] = (acc[op.type] || 0) + 1
      return acc
    }, {})

    return {
      summary: {
        totalOperations: totalOperations.value,
        operationsPerSecond: operationsPerSecond.value,
        averageRenderTime: Math.round(metrics.averageRenderTime * 100) / 100,
        reactivityHealth: reactivityHealth.value
      },
      breakdown: operationsByType,
      performance: {
        slowOperations: slowOperations.length,
        slowOperationRatio: slowOperations.length / Math.max(1, performanceData.value.length),
        worstPerformance: Math.max(...performanceData.value.map(op => op.duration), 0)
      },
      dependencies: Array.from(metrics.dependencies),
      memoryUsage: getMemoryUsage()
    }
  }

  const logSummaryReport = () => {
    const report = getPerformanceReport()
    
    console.group(`📊 Reactivity Report: ${componentName}`)
    console.table(report.summary)
    console.log('Operation Breakdown:', report.breakdown)
    console.log('Performance Issues:', report.performance)
    console.log('Dependencies:', report.dependencies)
    console.log('Memory Usage:', report.memoryUsage)
    console.groupEnd()
  }

  const getOptimizationSuggestions = () => {
    const suggestions = []
    const report = getPerformanceReport()
    
    if (report.summary.averageRenderTime > 16) {
      suggestions.push({
        type: 'performance',
        priority: 'high',
        message: `Average render time (${report.summary.averageRenderTime}ms) exceeds 16ms target. Consider reducing template complexity or using v-memo.`
      })
    }
    
    if (report.performance.slowOperationRatio > 0.1) {
      suggestions.push({
        type: 'performance',
        priority: 'medium',
        message: `${Math.round(report.performance.slowOperationRatio * 100)}% of operations are slow. Review computed properties and watchers.`
      })
    }
    
    if (metrics.computedEvaluations / Math.max(1, totalOperations.value) > 0.5) {
      suggestions.push({
        type: 'reactivity',
        priority: 'medium',
        message: 'High computed property evaluation ratio. Ensure computed properties are efficient and necessary.'
      })
    }
    
    if (metrics.dependencies.size > 20) {
      suggestions.push({
        type: 'complexity',
        priority: 'low',
        message: `Component has ${metrics.dependencies.size} reactive dependencies. Consider breaking into smaller components.`
      })
    }
    
    const memory = getMemoryUsage()
    if (memory.used > memory.limit * 0.8) {
      suggestions.push({
        type: 'memory',
        priority: 'high',
        message: 'Memory usage is high. Check for memory leaks in watchers and effects.'
      })
    }
    
    return suggestions
  }

  // Utility functions for tracking
  const measureAsync = async (name, fn) => {
    if (!isRecording.value) return await fn()
    
    const start = performance.now()
    try {
      const result = await fn()
      const duration = performance.now() - start
      recordPerformanceData(`async:${name}`, duration)
      return result
    } catch (error) {
      const duration = performance.now() - start
      recordPerformanceData(`error:${name}`, duration)
      throw error
    }
  }

  const measureSync = (name, fn) => {
    if (!isRecording.value) return fn()
    
    const start = performance.now()
    try {
      const result = fn()
      const duration = performance.now() - start
      recordPerformanceData(`sync:${name}`, duration)
      return result
    } catch (error) {
      const duration = performance.now() - start
      recordPerformanceData(`error:${name}`, duration)
      throw error
    }
  }

  // Auto-monitoring for development
  if (process.env.NODE_ENV === 'development') {
    onMounted(() => {
      console.log(`🔧 Development mode: Auto-starting reactivity monitoring for ${componentName}`)
      startMonitoring()
    })

    onUnmounted(() => {
      if (isRecording.value) {
        stopMonitoring()
      }
    })
  }

  return {
    // State
    metrics,
    isRecording,
    monitoring,
    performanceData,
    
    // Computed
    totalOperations,
    operationsPerSecond,
    reactivityHealth,
    dependencyGraph,
    
    // Controls
    startMonitoring,
    stopMonitoring,
    resetMetrics,
    
    // Tracking
    trackRender,
    trackComputed,
    trackWatcher,
    trackEffect,
    trackDataChange,
    
    // Analysis
    getPerformanceReport,
    logSummaryReport,
    getOptimizationSuggestions,
    
    // Utilities
    measureAsync,
    measureSync
  }
}
```

### Exercise 8.2: Create Reactivity Debugger Component

Create `src/components/ReactivityDebugger.vue` to visualize reactivity in real-time:

```vue
<template>
  <div class="reactivity-debugger" v-if="showDebugger">
    <header class="debugger-header">
      <h3 class="debugger-title">🔍 Reactivity Monitor</h3>
      <div class="debugger-controls">
        <button 
          @click="toggleMonitoring" 
          class="btn"
          :class="isRecording ? 'btn-danger' : 'btn-primary'"
        >
          {{ isRecording ? '⏹️ Stop' : '▶️ Start' }}
        </button>
        <button @click="resetMetrics" class="btn btn-secondary">🔄 Reset</button>
        <button @click="showDebugger = false" class="btn btn-close">✕</button>
      </div>
    </header>

    <div class="debugger-content">
      <!-- Metrics Overview -->
      <section class="metrics-section">
        <h4>📊 Performance Metrics</h4>
        <div class="metrics-grid">
          <div class="metric-card">
            <span class="metric-label">Renders</span>
            <span class="metric-value">{{ metrics.renderCount }}</span>
          </div>
          <div class="metric-card">
            <span class="metric-label">Computed</span>
            <span class="metric-value">{{ metrics.computedEvaluations }}</span>
          </div>
          <div class="metric-card">
            <span class="metric-label">Watchers</span>
            <span class="metric-value">{{ metrics.watcherTriggers }}</span>
          </div>
          <div class="metric-card">
            <span class="metric-label">Effects</span>
            <span class="metric-value">{{ metrics.effectTriggers }}</span>
          </div>
        </div>
      </section>

      <!-- Health Score -->
      <section class="health-section">
        <h4>💊 Reactivity Health</h4>
        <div class="health-score" :class="getHealthClass(reactivityHealth)">
          <div class="health-circle">
            <span class="health-number">{{ reactivityHealth }}</span>
          </div>
          <div class="health-info">
            <div class="health-status">{{ getHealthStatus(reactivityHealth) }}</div>
            <div class="health-details">
              Avg Render: {{ Math.round(metrics.averageRenderTime * 100) / 100 }}ms
            </div>
          </div>
        </div>
      </section>

      <!-- Performance Chart -->
      <section class="chart-section">
        <h4>📈 Performance Timeline</h4>
        <div class="performance-chart">
          <div class="chart-container">
            <svg class="chart-svg" viewBox="0 0 400 100">
              <!-- Chart background -->
              <rect width="400" height="100" fill="var(--color-background-soft)" />
              
              <!-- Performance line -->
              <polyline
                :points="chartPoints"
                fill="none"
                stroke="var(--color-primary)"
                stroke-width="2"
              />
              
              <!-- Data points -->
              <circle
                v-for="(point, index) in chartData"
                :key="index"
                :cx="point.x"
                :cy="point.y"
                r="2"
                :fill="point.duration > 16 ? 'var(--color-danger)' : 'var(--color-primary)'"
              />
              
              <!-- Threshold line (16ms for 60fps) -->
              <line
                x1="0"
                y1="80"
                x2="400"
                y2="80"
                stroke="var(--color-danger)"
                stroke-width="1"
                stroke-dasharray="5,5"
                opacity="0.5"
              />
            </svg>
            <div class="chart-label">16ms threshold (60fps)</div>
          </div>
        </div>
      </section>

      <!-- Dependencies -->
      <section class="dependencies-section">
        <h4>🔗 Reactive Dependencies</h4>
        <div class="dependencies-list">
          <div
            v-for="dependency in Array.from(metrics.dependencies)"
            :key="dependency"
            class="dependency-item"
            :class="getDependencyClass(dependency)"
          >
            <span class="dependency-icon">{{ getDependencyIcon(dependency) }}</span>
            <span class="dependency-name">{{ dependency }}</span>
          </div>
        </div>
      </section>

      <!-- Optimization Suggestions -->
      <section class="suggestions-section" v-if="suggestions.length > 0">
        <h4>💡 Optimization Suggestions</h4>
        <div class="suggestions-list">
          <div
            v-for="(suggestion, index) in suggestions"
            :key="index"
            class="suggestion-item"
            :class="`priority-${suggestion.priority}`"
          >
            <span class="suggestion-icon">{{ getSuggestionIcon(suggestion.priority) }}</span>
            <span class="suggestion-message">{{ suggestion.message }}</span>
          </div>
        </div>
      </section>

      <!-- Configuration -->
      <section class="config-section">
        <h4>⚙️ Monitor Settings</h4>
        <div class="config-options">
          <label class="config-option">
            <input type="checkbox" v-model="monitoring.trackRenders">
            Track Renders
          </label>
          <label class="config-option">
            <input type="checkbox" v-model="monitoring.trackComputed">
            Track Computed
          </label>
          <label class="config-option">
            <input type="checkbox" v-model="monitoring.trackWatchers">
            Track Watchers
          </label>
          <label class="config-option">
            <input type="checkbox" v-model="monitoring.trackEffects">
            Track Effects
          </label>
        </div>
      </section>
    </div>
  </div>

  <!-- Toggle button when debugger is hidden -->
  <button 
    v-else 
    @click="showDebugger = true" 
    class="debugger-toggle"
    title="Show Reactivity Debugger"
  >
    🔍
  </button>
</template>

<script setup>
import { ref, computed, watch } from 'vue'
import { useReactivityMonitoring } from '../composables/useReactivityMonitoring'

const props = defineProps({
  componentName: {
    type: String,
    default: 'Unknown Component'
  }
})

// Reactivity monitoring
const {
  metrics,
  isRecording,
  monitoring,
  performanceData,
  reactivityHealth,
  startMonitoring,
  stopMonitoring,
  resetMetrics,
  getOptimizationSuggestions
} = useReactivityMonitoring(props.componentName)

// Local state
const showDebugger = ref(false)

// Computed properties
const suggestions = computed(() => getOptimizationSuggestions())

const chartData = computed(() => {
  const data = performanceData.value.slice(-20) // Last 20 data points
  const maxDuration = Math.max(...data.map(d => d.duration), 16)
  
  return data.map((point, index) => ({
    x: (index / Math.max(1, data.length - 1)) * 400,
    y: 100 - (point.duration / maxDuration) * 100,
    duration: point.duration
  }))
})

const chartPoints = computed(() => {
  return chartData.value.map(point => `${point.x},${point.y}`).join(' ')
})

// Methods
const toggleMonitoring = () => {
  if (isRecording.value) {
    stopMonitoring()
  } else {
    startMonitoring()
  }
}

const getHealthClass = (health) => {
  if (health >= 80) return 'health-excellent'
  if (health >= 60) return 'health-good'
  if (health >= 40) return 'health-fair'
  return 'health-poor'
}

const getHealthStatus = (health) => {
  if (health >= 80) return 'Excellent'
  if (health >= 60) return 'Good'
  if (health >= 40) return 'Fair'
  return 'Poor'
}

const getDependencyClass = (dependency) => {
  if (dependency.startsWith('computed:')) return 'dep-computed'
  if (dependency.startsWith('watcher:')) return 'dep-watcher'
  if (dependency.startsWith('effect:')) return 'dep-effect'
  if (dependency.startsWith('data:')) return 'dep-data'
  return 'dep-unknown'
}

const getDependencyIcon = (dependency) => {
  if (dependency.startsWith('computed:')) return '🧮'
  if (dependency.startsWith('watcher:')) return '👁️'
  if (dependency.startsWith('effect:')) return '⚡'
  if (dependency.startsWith('data:')) return '📦'
  return '❓'
}

const getSuggestionIcon = (priority) => {
  switch (priority) {
    case 'high': return '🚨'
    case 'medium': return '⚠️'
    case 'low': return '💡'
    default: return 'ℹ️'
  }
}
</script>

<style scoped>
.reactivity-debugger {
  position: fixed;
  top: 20px;
  right: 20px;
  width: 400px;
  max-height: 80vh;
  background: var(--color-background);
  border: 1px solid var(--color-border);
  border-radius: 8px;
  box-shadow: 0 4px 20px rgba(0,0,0,0.1);
  z-index: 1000;
  overflow: hidden;
  font-size: 0.875rem;
}

.debugger-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 1rem;
  background: var(--color-background-soft);
  border-bottom: 1px solid var(--color-border);
}

.debugger-title {
  margin: 0;
  font-size: 1rem;
  font-weight: 600;
}

.debugger-controls {
  display: flex;
  gap: 0.5rem;
}

.btn {
  padding: 0.25rem 0.5rem;
  border: none;
  border-radius: 4px;
  cursor: pointer;
  font-size: 0.75rem;
  transition: all 0.2s ease;
}

.btn-primary { background: var(--color-primary); color: white; }
.btn-danger { background: var(--color-danger); color: white; }
.btn-secondary { background: var(--color-secondary); color: white; }
.btn-close { background: var(--color-background); color: var(--color-text); }

.debugger-content {
  padding: 1rem;
  max-height: calc(80vh - 80px);
  overflow-y: auto;
}

.metrics-section,
.health-section,
.chart-section,
.dependencies-section,
.suggestions-section,
.config-section {
  margin-bottom: 1.5rem;
}

.metrics-section h4,
.health-section h4,
.chart-section h4,
.dependencies-section h4,
.suggestions-section h4,
.config-section h4 {
  margin: 0 0 0.75rem 0;
  font-size: 0.875rem;
  font-weight: 600;
  color: var(--color-text);
}

.metrics-grid {
  display: grid;
  grid-template-columns: 1fr 1fr;
  gap: 0.5rem;
}

.metric-card {
  padding: 0.75rem;
  background: var(--color-background-soft);
  border-radius: 4px;
  text-align: center;
}

.metric-label {
  display: block;
  font-size: 0.75rem;
  color: var(--color-text-light);
  margin-bottom: 0.25rem;
}

.metric-value {
  display: block;
  font-size: 1.25rem;
  font-weight: 600;
  color: var(--color-primary);
}

.health-score {
  display: flex;
  align-items: center;
  gap: 1rem;
}

.health-circle {
  width: 60px;
  height: 60px;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  font-weight: 600;
  font-size: 1rem;
}

.health-excellent .health-circle { background: var(--color-success); color: white; }
.health-good .health-circle { background: var(--color-warning); color: white; }
.health-fair .health-circle { background: var(--color-info); color: white; }
.health-poor .health-circle { background: var(--color-danger); color: white; }

.health-info {
  flex: 1;
}

.health-status {
  font-weight: 600;
  margin-bottom: 0.25rem;
}

.health-details {
  font-size: 0.75rem;
  color: var(--color-text-light);
}

.performance-chart {
  background: var(--color-background-soft);
  border-radius: 4px;
  overflow: hidden;
}

.chart-container {
  position: relative;
}

.chart-svg {
  width: 100%;
  height: 100px;
}

.chart-label {
  position: absolute;
  top: 75px;
  right: 10px;
  font-size: 0.625rem;
  color: var(--color-text-light);
}

.dependencies-list {
  display: flex;
  flex-direction: column;
  gap: 0.25rem;
}

.dependency-item {
  display: flex;
  align-items: center;
  gap: 0.5rem;
  padding: 0.5rem;
  background: var(--color-background-soft);
  border-radius: 4px;
  font-size: 0.75rem;
}

.dependency-icon {
  font-size: 0.875rem;
}

.dep-computed { border-left: 3px solid var(--color-primary); }
.dep-watcher { border-left: 3px solid var(--color-warning); }
.dep-effect { border-left: 3px solid var(--color-success); }
.dep-data { border-left: 3px solid var(--color-info); }

.suggestions-list {
  display: flex;
  flex-direction: column;
  gap: 0.5rem;
}

.suggestion-item {
  display: flex;
  align-items: flex-start;
  gap: 0.5rem;
  padding: 0.75rem;
  border-radius: 4px;
  font-size: 0.75rem;
  line-height: 1.4;
}

.priority-high { background: var(--color-danger-light); }
.priority-medium { background: var(--color-warning-light); }
.priority-low { background: var(--color-info-light); }

.suggestion-icon {
  font-size: 0.875rem;
  margin-top: 0.125rem;
}

.config-options {
  display: flex;
  flex-direction: column;
  gap: 0.5rem;
}

.config-option {
  display: flex;
  align-items: center;
  gap: 0.5rem;
  font-size: 0.75rem;
  cursor: pointer;
}

.debugger-toggle {
  position: fixed;
  bottom: 20px;
  right: 20px;
  width: 50px;
  height: 50px;
  border-radius: 50%;
  border: none;
  background: var(--color-primary);
  color: white;
  font-size: 1.25rem;
  cursor: pointer;
  box-shadow: 0 4px 12px rgba(0,0,0,0.15);
  z-index: 1000;
  transition: all 0.2s ease;
}

.debugger-toggle:hover {
  transform: translateY(-2px);
  box-shadow: 0 6px 16px rgba(0,0,0,0.2);
}
</style>
```

### Exercise 8.3: Enhanced Task Dashboard with Monitoring

Update your `src/App.vue` to include reactivity monitoring:

```vue
<template>
  <div id="app" class="app-container" :data-theme="currentTheme">
    <!-- Your existing app content -->
    <AppHeader 
      :stats="taskStats"
      :theme="currentTheme"
      @toggle-theme="toggleTheme"
    />

    <main id="main-content" class="main-content" role="main">
      <TaskForm @task-created="handleTaskCreated" />
      
      <TaskFilters 
        v-model:search="filters.search"
        v-model:category="filters.category"
        v-model:status="filters.status"
        v-model:priority="filters.priority"
        @clear-completed="clearCompletedTasks"
        @export-tasks="exportTasks"
      />
      
      <TaskList 
        :tasks="filteredTasks"
        :categories="categories"
        @task-updated="handleTaskUpdated"
        @task-deleted="handleTaskDeleted"
      />
    </main>

    <!-- Reactivity Debugger (Development only) -->
    <ReactivityDebugger 
      v-if="isDevelopment"
      component-name="TaskDashboard"
    />
  </div>
</template>

<script setup>
import { ref, computed, watch, onMounted } from 'vue'
import { useReactivityMonitoring } from './composables/useReactivityMonitoring'

// Import your existing components
import AppHeader from './components/AppHeader.vue'
import TaskForm from './components/TaskForm.vue'
import TaskFilters from './components/TaskFilters.vue'
import TaskList from './components/TaskList.vue'
import ReactivityDebugger from './components/ReactivityDebugger.vue'

// Your existing composables
import { useTasks } from './composables/useTasks'
import { useTheme } from './composables/useTheme'

// Reactivity monitoring setup
const {
  trackRender,
  trackComputed,
  trackWatcher,
  measureAsync,
  measureSync
} = useReactivityMonitoring('TaskDashboard')

// Your existing logic
const { 
  tasks, 
  filters, 
  categories,
  addTask, 
  updateTask, 
  deleteTask, 
  clearCompleted,
  exportTasks,
  filteredTasks,
  taskStats
} = useTasks()

const { currentTheme, toggleTheme } = useTheme()

// Development flag
const isDevelopment = ref(process.env.NODE_ENV === 'development')

// Track computed property evaluations
const enhancedFilteredTasks = computed(() => {
  trackComputed('filteredTasks')
  return measureSync('filteredTasks-calculation', () => {
    return filteredTasks.value
  })
})

const enhancedTaskStats = computed(() => {
  trackComputed('taskStats')
  return measureSync('taskStats-calculation', () => {
    return taskStats.value
  })
})

// Enhanced event handlers with monitoring
const handleTaskCreated = async (taskData) => {
  await measureAsync('task-creation', async () => {
    addTask(taskData)
    trackRender()
  })
}

const handleTaskUpdated = async (taskData) => {
  await measureAsync('task-update', async () => {
    updateTask(taskData)
    trackRender()
  })
}

const handleTaskDeleted = async (task) => {
  await measureAsync('task-deletion', async () => {
    deleteTask(task.id)
    trackRender()
  })
}

const clearCompletedTasks = async () => {
  await measureAsync('clear-completed', async () => {
    clearCompleted()
    trackRender()
  })
}

// Watch for theme changes with monitoring
watch(currentTheme, (newTheme, oldTheme) => {
  trackWatcher('theme-watcher')
  measureSync('theme-change', () => {
    console.log(`Theme changed from ${oldTheme} to ${newTheme}`)
  })
})

// Watch for filter changes with monitoring
watch(filters, (newFilters) => {
  trackWatcher('filters-watcher')
  measureSync('filter-processing', () => {
    console.log('Filters updated:', newFilters)
  })
}, { deep: true })

// Track initial render
onMounted(() => {
  trackRender()
})
</script>

<style>
/* Your existing styles */
</style>
```

### Success Criteria
- [ ] Reactivity monitoring system functional
- [ ] Real-time performance visualization working
- [ ] Dependency tracking operational
- [ ] Optimization suggestions generated
- [ ] Memory usage monitoring active
- [ ] Interactive debugger responsive
- [ ] Performance thresholds enforced
- [ ] Development-only features working

### 💡 Key Learning Points

1. **Reactivity Internals**: Understanding how Vue tracks dependencies and triggers updates
2. **Performance Monitoring**: Measuring reactive system performance in real-time
3. **Dependency Visualization**: Seeing how reactive data connects to UI updates
4. **Optimization Techniques**: Identifying and fixing performance bottlenecks
5. **Debug Tools**: Building custom tools to understand complex reactive systems

### 🎯 Looking Ahead

In Chapter 9, you'll explore Vue.js component templates and directives, learning how to create dynamic, interactive user interfaces that leverage the reactive system you've just mastered!

**Your reactivity monitoring system is complete** - now you can see Vue's magic in action!